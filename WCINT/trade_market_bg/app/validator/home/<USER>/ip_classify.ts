import { BaseContextClass, Context } from 'egg';
import { IpClassifyQuery } from 'ExEntitys';
import * as _ from 'lodash';
import ExError from 'utils/ex-error/ex_error';
import { getBearerToken } from 'utils/token';
import {parseObjectIdStr} from "utils/repository";

export default class IpClassifyValidator extends BaseContextClass {
  logPrefix = '[validator.home.ip_classify]';
  /**
   * 实例验证规则
   * 按需使用对应规则
   */
  rules: any = {
    page: {
      type: 'number',
      required: true,
      convertType: 'number',
      min: 0,
    },
    limit: {
      type: 'number',
      required: true,
      convertType: 'number',
      min: 1,
      max: 200,
    },
    pid: {
      type: 'object_id',
      required: false,
      convertType: value => parseObjectIdStr(value),
    },
  };

  public constructor(ctx: Context) {
    super(ctx);
    this.ctx = ctx;
  }

  public async list() {
    const { ctx } = this;
    const allowCreateRules = [
      'pid',
    ];
    const validRules = _.pick(this.rules, allowCreateRules);
    ctx.validate(validRules, ctx.query);
    ctx.state.form = _.pick(ctx.query, allowCreateRules);
  }

  public async detail() {
    const { ctx } = this;

    const validRules = _.pick(this.rules, ['_id']);
    ctx.validate(validRules, ctx.params);

    const id = ctx.params._id;
    const query: IpClassifyQuery = { _id: id };
    const ipClassify = await ctx.service.item.ipClassify.queryData(query);
    if (!ipClassify) {
      throw new ExError('TMT_DATA_NOT_EXIST', `the ipClassify(${id}) is not exist`);
    }
    ctx.state.ipClassify = ipClassify;
  }

  public async getIpOfUser() {
    const { ctx } = this;
    const bearerToken: string = getBearerToken(ctx.get('Authorization'));
    if (bearerToken) {
      const jwtState = ctx.app.jwt.decode(bearerToken);
      const userId = _.get(jwtState, 'id');
      if (userId) {
        const user = await ctx.service.user.user.queryUserById(userId);
        if (user) {
          ctx.state.user = user;
        } else {
          throw new ExError(0, 'not user', { list: [], total: 0 });
        }
      }
    } else {
      throw new ExError(0, '', {list: [], total: 0});
    }
  }
}
