import { Service } from 'egg';
import * as _ from 'lodash';
import ExError from 'utils/ex-error/ex_error';
import { md5 } from 'utils/auth';
import moment = require('moment');
import { enableBoxOfWxmini } from 'utils/item_order';
import { formatDateString } from 'utils/date';
import {
  IpClassifyEntity,
  SteamItemEntity,
  TrademarkEntity,
  UserItemEntity,
  UserItemQuery,
  UserItemUpdateQuery,
} from 'ExEntitys';
import { ObjectId, ObjectID } from 'bson';
import { UserItemStatusEnum } from 'enum/user_item/user_item_status';
import { StatusEnum } from 'enum/status';
import {UserItemReceiveTypeEnum} from 'enum/user_item/user_item_receive_type';

/**
 * SetBg Service
 */
export default class YcOpmBg extends Service {
  logPrefix = '[Service.Yc]';

  /**
   * 获取应用id
   */
  public async getAppAccess() {
    const { ctx } = this;
    const microService: Record<string, any> = _.get(ctx.app.config, 'custom.microservices.ycOpmBg', {});

    const config = _.get(ctx.app.config.custom, 'yc_app_access');
    const data = {
      client_id: config.client_id,
      client_secret: config.client_secret,
    };

    const apiUrl = `${microService.baseUrl}/open/session/app_access_token`;

    const curlOptions: any = {
      method: 'GET',
      contentType: 'json',
      data,
      dataType: 'json',
      timeout: 30000,
    };

    // 获取access_token
    let res;
    let result;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('PAT_CURL_API_FAIL', errMsg);
    }
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        result = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    return result;
  }

  /**
   * 根据用户ID查询气仓
   */
  public async queryUserItem(page, limit, open_user_id, queryStr): Promise<any> {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }
    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 开始获取
    const appInfo = await this.getAppAccess();
    const data: any = {
      page,
      limit,
      open_user_id,
      queryStr,
      sign: '',
    };

    // 调用接口渠道是否小程序
    const flag = enableBoxOfWxmini(ctx.headers.client_type, ctx.headers.app_channel, _.get(ctx.app.config, 'custom.sale'));
    if (flag) {
      data.channel_flag = true;
    }
    // 检测是否过滤虚拟卡
    const appid = await ctx.service.item.steamItem.checkAppidFilter();
    if (appid) {
      data.channel_virtual_flag = true;
    }
    data.ignore_receive_types = [ UserItemReceiveTypeEnum.WcReSell ];
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/user_item/queryUserItemsByUser`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    ctx.logger.info(`${this.logPrefix} queryUserItem apiUrl： ${apiUrl}`);
    ctx.logger.info(`${this.logPrefix} queryUserItem begin data： ${JSON.stringify(data)}, token: ${appInfo.access_token}`);
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      ctx.logger.info(`${this.logPrefix} queryUserItem res`, JSON.stringify(res));
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      return [];
    }
    ctx.logger.info(`${this.logPrefix} queryUserItem res`, JSON.stringify(res));
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        return resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      return [];
    }
  }

  /**
   * 同步steam_item表
   */
  public async sync_steam_item(updatedAt?) {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 获取本地资产表里最新的更新数据时间
    const item = await ctx.service.item.steamItem.queryJustOne({}, {}, { sort: { updated_at: -1 } });

    // 开始同步数据
    const appInfo = await this.getAppAccess();
    const data = {
      updated_at: formatDateString(updatedAt ? updatedAt : _.get(item, 'updated_at', moment().subtract(1, 'years'))),
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/nftoken/items`;
    ctx.logger.info(`${this.logPrefix} sync_steam_item begin, data: ${JSON.stringify(data)}, url：${apiUrl}`);
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      timeout: 60000,
      dataType: 'json',
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    let result: SteamItemEntity[];
    if (res.status === 200 && res.data) {
      const resData = res.data;
      ctx.logger.info(`${this.logPrefix} sync_steam_item result, data: ${JSON.stringify(resData)}`);
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix} sync_steam_item curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        result = resData.data;
        // 同步商品到steamItem表
        const options = {
          upsert: true,
          useFindAndModify: true,
        };
        let query;
        for (const item of result) {
          query = {
            _id: item._id,
          };
          _.unset(item, 'extends');
          await ctx.service.item.steamItem.findOneAndUpdate(query, { $set: item }, options);

          // 清除缓存
          const redisPrefix = ctx.app.config.custom.redis.prefix;
          const key = `${redisPrefix}:getCombination:${String(item._id)}`;
          await ctx.app.redis.del(key);
        }
        // 异步通知商城服务商品信息变更
        const itemIds = result.map(item => String(item._id));
        ctx.service.mpsBg.itemInfoChangeNotify(itemIds);
      }
    } else {
      const errMsg = `${this.logPrefix}sync_steam_item curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
    }
  }

  /**
   * 同步user_item表
   */
  public async sync_user_item(date?, open_user_ids?) {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    ctx.logger.info(`${this.logPrefix} sync_user_item ycConfig:${ycConfig}`);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 获取本地资产表里最新的更新数据时间
    // const item = await ctx.service.item.userItem.queryData({}, { updated_at: 1 }, { sort: { updated_at: -1 } });
    // 获取redis保存的同步时间
    const redisPrefix = _.get(ctx.app.config, 'custom.redis.prefix', 'TMTBG');
    const key = `${redisPrefix}:syncUserItems:updated_at`;
    let updatedAt = await ctx.app.redis.get(key);
    if (!updatedAt) {
      updatedAt = moment().toISOString();
    }

    // 开始同步数据
    const appInfo = await this.getAppAccess();
    const data: any = {
      updated_at: date ? date : updatedAt,
      open_user_ids: null,
      sign: '',
    };
    if (open_user_ids) {
      data.open_user_ids = open_user_ids;
      data.limit = 100000;
    }
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/nftoken/user_item`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    let result: UserItemEntity[];
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        result = resData.data;
        const ops: any[] = [];
        let update_at;
        for (const item of result) {
          if (!item) {
            continue;
          }
          ops.push({
            updateOne: {
              filter: {
                _id: item._id,
              } as UserItemQuery,
              update: {
                $set: item,
              } as UserItemUpdateQuery,
              upsert: true,
            },
          });
          update_at = item.updated_at;
        }
        if (ops.length > 0) {
          ctx.service.item.userItem.bulkwriteUserItem(ops);
          if (updatedAt === moment(update_at).toISOString()) {
            updatedAt = moment(updatedAt).add(10, 'ms').toISOString();
            ctx.app.redis.set(key, updatedAt);
          } else {
            ctx.app.redis.set(key, moment(update_at).toISOString());
          }
        } else {
          updatedAt = moment(updatedAt).add(100, 'ms').toISOString();
          ctx.app.redis.set(key, updatedAt);
        }
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
    }
  }

  /**
   * 根据物品ID查询信息
   */
  public async queryUserItemById(user_item_id): Promise<UserItemEntity | undefined> {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    const appInfo = await this.getAppAccess();
    const data: any = {
      user_item_id,
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/nftoken/user_item`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    let result: UserItemEntity[];
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        result = resData.data;
        return result[0];
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
    }
    return;
  }

  /**
   * 用户主动同步自己气仓物品
   */
  public async user_initiative_sync_item() {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 从redis拉取用户id
    const redisPrefix = _.get(ctx.app.config, 'custom.redis.prefix', 'TMTBG');
    const userId = await ctx.app.redis.spop(`${redisPrefix}:sync_user_item`);
    if (userId) {
      // 获取用户开放平台id以及主动拉取气仓物品次数
      const user = await ctx.service.user.user.queryUserById(new ObjectID(userId));
      let is_first = true;
      const open_user_id = _.get(user, 'open_info.open_user_id');
      if (!open_user_id) {
        throw new ExError('TMT_DATA_NOT_EXIST', 'user_initiative_sync_item open_user_id is not exists');
      }
      if (_.get(user, 'extends.sync_user_item_count', 0) > 0) {
        is_first = false;
      }
      ctx.service.user.user.update({
        _id: user._id,
      }, {
        $inc: {
          'extends.sync_user_item_count': 1,
        },
      });

      // 获取本地资产表里最新的更新数据时间
      let item;
      if (!is_first) {
        const query = {
          open_user_id,
          updated_at: {
            $gte: moment().subtract(3, 'hours'),
          },
          status: UserItemStatusEnum.Owned,
        };
        item = await ctx.service.item.userItem.queryDatas(query);
      }
      // if (item) {
      // 开始同步数据
      const appInfo = await this.getAppAccess();
      const data: any = {
        is_first,
        open_user_id: String(open_user_id),
        item_count: _.get(item, 'length', 0),
        sign: '',
      };
      data.sign = await md5(data);

      const apiUrl = `${microService.baseUrl}/open/user_item/user_initiative_sync_item`;
      const curlOptions: any = {
        method: 'POST',
        contentType: 'json',
        headers: {
          Authorization: `Bearer ${appInfo.access_token}`,
        },
        data,
        // data: payload,
        dataType: 'json',
        timeout: 30000,
      };

      let res;
      try {
        res = await ctx.curl(apiUrl, curlOptions);
      } catch (err) {
        const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
        ctx.logger.error(errMsg, res.data);
        throw new ExError('TMT_CURL_API_FAIL', errMsg);
      }
      let result: UserItemEntity[];
      if (res.status === 200 && res.data) {
        const resData = res.data;
        if (resData.code !== 0) {
          const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
          ctx.logger.error(errMsg, res.data);
          switch (resData.code) {
            default: {
              throw new ExError(resData.code, errMsg);
            }
          }
        } else {
          result = resData.data;
          if (result.length > 0) {
            const ops: any[] = [];
            for (const item of result) {
              if (!item) {
                continue;
              }
              ops.push({
                updateOne: {
                  filter: {
                    _id: item._id,
                  } as UserItemQuery,
                  update: {
                    $set: item,
                  } as UserItemUpdateQuery,
                  upsert: true,
                },
              });
            }
            if (ops.length > 0) {
              ctx.service.item.userItem.bulkwriteUserItem(ops);
            }
          }
        }
      } else {
        const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
        ctx.logger.error(errMsg, res.data);
      }
    }
  }

  /**
   * 同步ip表
   */
  public async sync_ip() {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 获取本地资产表里最新的更新数据时间
    const item = await ctx.service.item.ipClassify.queryData({}, { updated_at: 1 });

    // 开始同步数据
    const appInfo = await this.getAppAccess();
    const data = {
      updated_at: formatDateString(_.get(item, 'updated_at', moment().subtract(1, 'years'))),
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/nftoken/ip_classifys`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    let result: IpClassifyEntity[];
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        result = resData.data;
        // 同步ip
        for (const item of result) {
          const query = {
            $or: [
              { _id: item._id },
              { name: item.name },
            ],
          };
          const count = await ctx.service.item.ipClassify.countIpClassify(query);
          if (count === 0) {
            // 不存在则保存，默认禁用
            item.status = StatusEnum.Disable;
            ctx.service.item.ipClassify.createIpClassify(item);
          }
        }
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
    }
  }

  /**
   * 同步品牌表
   */
  public async sync_trademark() {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 获取本地资产表里最新的更新数据时间
    const item = await ctx.service.item.trademark.queryData({}, { updated_at: 1 });

    // 开始同步数据
    const appInfo = await this.getAppAccess();
    const data = {
      updated_at: formatDateString(_.get(item, 'updated_at', moment().subtract(1, 'years'))),
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/nftoken/trademarks`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    let result: TrademarkEntity[];
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        result = resData.data;
        // 同步user_item
        const options = {
          upsert: true,
          useFindAndModify: true,
        };
        let query;
        for (const item of result) {
          query = {
            _id: item._id,
          };
          ctx.service.item.trademark.findOneAndUpdate(query, item, options);
        }
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
    }
  }

  /**
   * 同步分类表
   */
  public async sync_item_classifys() {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 获取本地资产表里最新的更新数据时间
    const item = await ctx.service.item.itemClassify.queryData({}, { updated_at: 1 });

    // 开始同步数据
    const appInfo = await this.getAppAccess();
    const data = {
      updated_at: formatDateString(_.get(item, 'updated_at', moment().subtract(1, 'years'))),
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/nftoken/item_classifys`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    let result: TrademarkEntity[];
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        result = resData.data;
        // 同步user_item
        const options = {
          upsert: true,
          useFindAndModify: true,
        };
        let query;
        for (const item of result) {
          query = {
            _id: item._id,
          };
          ctx.service.item.itemClassify.findOneAndUpdate(query, item, options);
        }
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
    }
  }

  /**
   * 修改物品状态
   * @param userItemId
   * @param originalStatus
   * @param updateStatus
   */
  public async updateUserItemStatus(userItemId, originalStatus, updateStatus) {
    const { ctx, app } = this;
    ctx.logger.info(`${this.logPrefix} updateUserItemStatus begin, userItemId:${String(userItemId)}`, originalStatus, updateStatus);

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      // 元气玛特维护时
      throw new ExError('TMT_FAIL');
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;
    const data: any = {
      user_item_id: userItemId,
      original_status: originalStatus,
      update_status: updateStatus,
    };
    data.sign = await md5(data);
    const appInfo = await this.getAppAccess();
    const apiUrl = `${microService.baseUrl}/open/user_item/update_user_item_status`;
    const curlOptions: any = {
      method: 'PUT',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };
    ctx.logger.info(`${this.logPrefix} updateUserItemStatus apiUrl`, apiUrl);

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}updateUserItemStatus result fail, userItemId:${String(userItemId)}, (${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    if (res.status === 200 && res.data) {
      const resData = res.data;
      ctx.logger.info(`${this.logPrefix} updateUserItemStatus result, userItemId:${String(userItemId)}, resData:${JSON.stringify(resData)}`, originalStatus, updateStatus);
      if (resData.code !== 0) {
        // ctx.service.item.userItem.update({ _id: userItemId }, {
        //   $set: {
        //     status: UserItemStatusEnum.Disable,
        //   }
        // })
        const errMsg = `${this.logPrefix}updateUserItemStatus fail, userItemId:${String(userItemId)}, (${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      }
    } else {
      const errMsg = `${this.logPrefix}updateUserItemStatus result fail, userItemId:${String(userItemId)}, (${res.status})[${JSON.stringify(res.data)}], ${originalStatus}, ${updateStatus}`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError(res.status, errMsg);
    }
  }

  /**
   * 修改物品状态
   * @param userItemIds
   * @param originalStatus
   * @param updateStatus
   */
  public async updateUserItemsStatus(userItemIds: ObjectID[], originalStatus, updateStatus) {
    const { ctx, app } = this;
    ctx.logger.info(`${this.logPrefix} updateUserItemsStatus begin, userItemIds:${JSON.stringify(userItemIds)}`, originalStatus, updateStatus);

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      // 元气玛特维护时
      throw new ExError('TMT_FAIL');
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;
    const data: any = {
      user_item_ids: userItemIds,
      original_status: originalStatus,
      update_status: updateStatus,
    };
    data.sign = await md5(data);
    const appInfo = await this.getAppAccess();
    const apiUrl = `${microService.baseUrl}/open/user_item/update_user_item_status`;
    const curlOptions: any = {
      method: 'PUT',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };
    ctx.logger.info(`${this.logPrefix} updateUserItemsStatus apiUrl`, apiUrl);

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}updateUserItemsStatus fail, userItemIds:${JSON.stringify(userItemIds)}, (${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    if (res.status === 200 && res.data) {
      const resData = res.data;
      ctx.logger.info(`${this.logPrefix} updateUserItemsStatus result, userItemIds:${JSON.stringify(userItemIds)}, resData:${JSON.stringify(resData)}`, originalStatus, updateStatus);
      if (resData.code !== 0) {
        // ctx.service.item.userItem.update({ _id: userItemId }, {
        //   $set: {
        //     status: UserItemStatusEnum.Disable,
        //   }
        // })
        const errMsg = `${this.logPrefix}updateUserItemsStatus fail, userItemIds:${JSON.stringify(userItemIds)}, (${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      }
    } else {
      const errMsg = `${this.logPrefix}updateUserItemsStatus result fail, userItemIds:${JSON.stringify(userItemIds)}, (${res.status})[${JSON.stringify(res.data)}], ${originalStatus}, ${updateStatus}`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError(res.status, errMsg);
    }
  }

  /**
   * 物品转移
   * @param userItemIds
   * @param toOpenUserId
   * @param original_status
   * @param buy_price
   * @param buy_time
   */
  public async userItemTransfer(userItemIds: string[] | ObjectID[], toOpenUserId: string | ObjectID, original_status: UserItemStatusEnum, buy_price: number, buy_time: Date) {
    const { ctx, app } = this;
    ctx.logger.info(`${this.logPrefix} userItemTransfer begin, userItemIds:${JSON.stringify(userItemIds)}, toOpenUserId:${String(toOpenUserId)}`);

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      // 元气玛特维护时
      throw new ExError('TMT_FAIL');
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;
    if (!buy_time) {
      buy_time = new Date();
    }
    const buy_time_str = moment(buy_time).format('YYYY-MM-DD HH:mm:ss.SSS');
    const data: any = {
      user_item_ids: userItemIds,
      to_open_user_id: toOpenUserId,
      original_status,
      buy_price,
      buy_time: buy_time_str,
    };
    data.sign = await md5(data);
    const appInfo = await this.getAppAccess();
    const apiUrl = `${microService.baseUrl}/open/user_item/user_item_transfer`;
    const curlOptions: any = {
      method: 'PUT',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };
    ctx.logger.info(`${this.logPrefix} userItemTransfer apiUrl:${apiUrl}, data:${JSON.stringify(data)}`);

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}userItemTransfer result fail, userItemIds:${JSON.stringify(userItemIds)}, (${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    if (res.status === 200 && res.data) {
      const resData = res.data;
      ctx.logger.info(`${this.logPrefix} userItemTransfer result, userItemIds:${JSON.stringify(userItemIds)}, resData:${JSON.stringify(resData)}`);
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}userItemTransfer result fail, userItemIds:${JSON.stringify(userItemIds)}, (${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      }
      return resData.data;
    } else {
      const errMsg = `${this.logPrefix}userItemTransfer result fail, userItemIds:${JSON.stringify(userItemIds)}, (${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError(res.status, errMsg);
    }
  }

  public async sync_mall_items() {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 获取本地资产表里最新的更新数据时间
    const item = await ctx.service.item.mallItem.queryData({}, { updated_at: 1 });

    // 开始同步数据
    const appInfo = await this.getAppAccess();
    const data = {
      updated_at: formatDateString(_.get(item, 'updated_at', moment().subtract(1, 'years'))),
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/nftoken/mall_items`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      // data: payload,
      dataType: 'json',
    };
    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    let result;
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        result = resData.data;
        // 同步user_item
        const options = {
          upsert: true,
          useFindAndModify: true,
        };
        let query;
        for (const mallItem of result) {
          query = {
            _id: mallItem._id,
          };
          ctx.service.item.mallItem.updateMallItem(query, mallItem, options);
        }
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
    }
  }

  /**
   * 根据物品ID查询信息
   */
  public async querySteamItemsByUser(open_user_id) {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;
    const appInfo = await this.getAppAccess();
    const data: any = {
      open_user_id,
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/user_item/querySteamItemsByUser`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    let result: UserItemEntity[];
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        result = resData.data;
        return result;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
    }
    return;
  }

  /**
   * 根据用户ID和商品ID查询信息
   */
  public async queryUserItemsByUserAndItemId(open_user_id, item_id) {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;
    const appInfo = await this.getAppAccess();
    const data: any = {
      open_user_id,
      item_id,
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/user_item/queryUserItemsByUserAndItemId`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    let result: UserItemEntity[];
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        result = resData.data;
        return result;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
    }
    return;
  }

  /**
   * 根据ItemIds统计气仓物品数量
   * @param itemIds
   * @param open_user_id
   * @param statusList
   */
  public async countUserItemByItemIds(itemIds: string[] | ObjectID[], open_user_id, statusList: UserItemStatusEnum[]): Promise<any> {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }
    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 开始获取
    const appInfo = await this.getAppAccess();
    const data: any = {
      open_user_id,
      item_ids: itemIds,
      status_list: statusList,
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/user_item/countUserItemsByItemIds`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      return [];
    }
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        return resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      return [];
    }
  }
  /**
   * 根据ItemIds统计气仓物品数量
   * @param itemIds
   * @param statusList
   */
  public async countUserItemByItemIdsNoUserId(
    itemIds: string[] | ObjectID[],
    statusList: UserItemStatusEnum[],
    fusion?: number,
  ): Promise<any> {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }
    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 开始获取
    const appInfo = await this.getAppAccess();
    const data: any = {
      item_ids: itemIds,
      status_list: statusList,
    };

    if (fusion !== undefined && fusion !== null) {
      data.fusion = fusion;
    }

    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/user_item/countUserItemsByItemIds`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res?.status || 500})[${JSON.stringify(res?.data || err.message)}]`;
      ctx.logger.error(errMsg, res?.data || err.message);
      return [];
    }

    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        throw new ExError(resData.code, errMsg);
      } else {
        return resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      return [];
    }
  }

  /**
   * 根据ItemIds统计气仓物品数量（按用户分组）
   * @param itemIds
   * @param statusList
   */
  public async countUserItemByItemIdsGroupByUserId(
    itemIds: string[] | ObjectID[],
    statusList: UserItemStatusEnum[],
  ): Promise<any> {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }
    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 开始获取
    const appInfo = await this.getAppAccess();
    const data: any = {
      item_ids: itemIds,
      status_list: statusList,
    };

    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/user_item/count_user_items_by_item_ids_group_by_user`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res?.status || 500})[${JSON.stringify(res?.data || err.message)}]`;
      ctx.logger.error(errMsg, res?.data || err.message);
      return [];
    }

    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        throw new ExError(resData.code, errMsg);
      } else {
        return resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      return [];
    }
  }

  /**
   * 根据ItemIds统计有效的气仓物品数量（持有中、未融合、未探索）
   * @param itemIds
   * @param statusList
   */
  public async countValidUserItemByItemIds(itemIds: string[] | ObjectID[]): Promise<any> {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }
    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 开始获取
    const appInfo = await this.getAppAccess();
    const data: any = {
      item_ids: itemIds,
    };

    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/user_item/countValidUserItemsByItemIds`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
      ctx.logger.info(`${this.logPrefix} countValidUserItemsByItemIds curl success.`, res.data);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res?.status || 500})[${JSON.stringify(res?.data || err.message)}]`;
      ctx.logger.error(errMsg, res?.data || err.message);
      return [];
    }

    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        throw new ExError(resData.code, errMsg);
      } else {
        return resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      return [];
    }
  }

  /**
   * 根据持仓商品ID和数据获取符合条件的用户
   */
  public async getUserByMatchRules(matchRules): Promise<any> {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }
    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;
    // 开始获取
    const appInfo = await this.getAppAccess();
    const data: any = {
      match_rules: JSON.stringify(matchRules),
      sign: '',
    };
    data.sign = await md5(data);
    const apiUrl = `${microService.baseUrl}/open/user_item/get_user_by_match_rules`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 60000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      return [];
    }
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        return resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_DYNAMIC_ERROR', res.desc, { message: `${res.desc}` });
    }
  }

  /**
   * 从云仓获取指定商品的持仓总数
   */
  public async loadUserItemCountsFromCacheOrRemote(itemIds: Array<string|ObjectId> = []) {
    const { ctx } = this;
    const redisPrefix = _.get(ctx.app.config, 'custom.redis.prefix', 'TMTBG');
    const cacheKeys = itemIds.map(itemId => `${redisPrefix}:userItemCount:${String(itemId)}`);

    const missingItemIds: string[] = [];
    const userItemCountMap = new Map<string, any>();

    let cachedResults;
    try {
      cachedResults = await ctx.app.redis.mget(cacheKeys);
    } catch (e) {
      ctx.logger.warn(`${this.logPrefix} Redis mget failed`, e);
      cachedResults = Array(cacheKeys.length).fill(null); // 全部视为未命中
    }

    cachedResults.forEach((cachedData, index) => {
      const itemId = String(itemIds[index]);
      if (cachedData) {
        try {
          const data = JSON.parse(cachedData);
          if (data && data.item_id) {
            userItemCountMap.set(itemId, data);
          } else {
            missingItemIds.push(itemId);
          }
        } catch (e) {
          missingItemIds.push(itemId);
        }
      } else {
        missingItemIds.push(itemId);
      }
    });
    ctx.logger.info(`${this.logPrefix} cache hit rate: ${itemIds.length - missingItemIds.length}/${itemIds.length}`);

    if (missingItemIds.length > 0) {
      const missingCounts = await ctx.service.ycOpm.countValidUserItemByItemIds(missingItemIds);
      // 打印结果
      ctx.logger.info(`${this.logPrefix} missing counts:`, missingCounts);

      const missingMap = new Map<string, any>();
      const multi = ctx.app.redis.multi();

      const now = moment().utcOffset(8); // 使用北京时间（UTC+8）
      const targetTime = now.clone().startOf('day').add(7, 'days'); // 7 天后同一时间
      const secondsToExpiry = Math.floor((targetTime.valueOf() - now.valueOf()) / 1000);

      missingCounts.forEach(count => {
        const itemId = String(count.item_id);
        userItemCountMap.set(itemId, count);
        missingMap.set(itemId, count);
      });

      missingItemIds.forEach(itemId => {
        const count = missingMap.get(itemId) || { item_id: itemId, count: 0 };
        const cacheKey = `${redisPrefix}:userItemCount:${String(itemId)}`;
        const value = JSON.stringify(count);
        multi.setex(cacheKey, secondsToExpiry, value);
      });

      try {
        await multi.exec();
        ctx.logger.info(`${this.logPrefix} cache written for ${missingItemIds.length} items`);
      } catch (e) {
        ctx.logger.error(`${this.logPrefix} Redis multi exec failed`, e);
      }
    }
    return userItemCountMap;
  }

  /**
   * 物品寄存
   * @param deposit_code
   * @param open_user_id
   */
  public async depositUserItem(deposit_code, open_user_id): Promise<any> {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }
    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;

    // 开始获取
    const appInfo = await this.getAppAccess();
    const data: any = {
      open_user_id,
      deposit_code,
      sign: '',
    };
    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/user_item/depositUserItem`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      return [];
    }
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        return resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_DYNAMIC_ERROR', res.desc, { message: `${res.desc}` });
    }
  }

  /**
   * 调用steam_items_extend upsert接口
   * @param itemId 商品ID
   * @param type 扩展类型
   * @param skuNo 商品SKU
   * @param itemName 商品名称
   * @param isGuide 是否引导
   */
  public async upsertSteamItemsExtend(itemId: string, type: number, skuNo: string, itemName: string, isGuide?: boolean) {
    const { ctx, app } = this;
    ctx.logger.info(`${this.logPrefix} upsertSteamItemsExtend begin.`, { itemId, type, skuNo, itemName, isGuide });

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      ctx.logger.warn(`${this.logPrefix} upsertSteamItemsExtend skipped due to maintenance mode.`);
      throw new ExError('TMT_YC_DOWN', '云仓服务维护中');
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;
    const appInfo = await this.getAppAccess();

    const data: any = {
      data_source: 2, // 文潮
      type,
      item_id: itemId,
      sku_no: skuNo,
      item_name: itemName,
    };

    if (isGuide !== undefined) {
      data.is_guide = isGuide;
    }

    data.sign = await md5(data);

    const apiUrl = `${microService.baseUrl}/open/steam_item/upsert_steam_items_extend`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `${this.logPrefix} upsertSteamItemsExtend curl fail. err: ${JSON.stringify(err)}`;
      ctx.logger.error(errMsg);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    let result;
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix} upsertSteamItemsExtend fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        throw new ExError('TMT_CURL_API_FAIL', errMsg);
      } else {
        result = resData.data;
        ctx.logger.info(`${this.logPrefix} upsertSteamItemsExtend success.`, result);
      }
    } else {
      const errMsg = `${this.logPrefix} upsertSteamItemsExtend fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    return result;
  }

  public async curl(data, url, headers, method = 'POST') {
    const { ctx } = this;
    data.sign = await md5(data);
    const curlOptions: any = {
      method,
      contentType: 'json',
      headers,
      data,
      dataType: 'json',
      timeout: 30000,
    };
    // 发送请求
    let res;
    try {
      res = await ctx.curl(url, curlOptions);
    } catch (err) {
      const errMsg = `url curl fail.(${url}] err: ${JSON.stringify(err)}`;
      ctx.logger.error(errMsg);
      return;
    }
    let result;
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `url curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        return;
      } else {
        result = resData.data;
      }
    } else {
      const errMsg = `url curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_LOCK');
    }
    return result;
  }

  /**
   * 发放物品
   * @param data
   */
  public async batchInsertUserItem(data) {
    const { ctx, app } = this;
    ctx.logger.info(`${this.logPrefix} batchInsertUserItem begin.`, data);

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      return;
    }

    const microService: Record<string, any> = ctx.app.config.custom.microservices.ycOpmBg;
    const sign = await md5(data);
    data.sign = sign;
    const appInfo = await this.getAppAccess();
    const apiUrl = `${microService.baseUrl}/open/user_item/batch_insert_user_item`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${appInfo.access_token}`,
      },
      data,
      dataType: 'json',
      timeout: 30000,
    };

    let res;
    try {
      res = await ctx.curl(apiUrl, curlOptions);
    } catch (err) {
      const errMsg = `url curl fail.(${apiUrl}] err: ${JSON.stringify(err)}`;
      ctx.logger.error(`${this.logPrefix} batchInsertUserItem error: ${errMsg}`);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      ctx.logger.error(`${this.logPrefix} batchInsertUserItem error: ${errorMessage}`, err || {});
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    if (res && res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        ctx.logger.error(`${this.logPrefix} batchInsertUserItem fail.`, resData);
        throw new ExError(resData.code);
      }
    } else {
      ctx.logger.error(`${this.logPrefix} batchInsertUserItem fail.`, res);
      throw new ExError('TMT_CURL_API_FAIL');
    }
  }
}
