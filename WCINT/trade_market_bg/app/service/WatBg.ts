import { Service } from 'egg';
import * as _ from 'lodash';
import ExError from 'utils/ex-error/ex_error';
import { objToDotKeys } from 'utils/repository';
import { ObjectId } from 'bson';
import { TradeTypeEnum } from 'enum/trade_type';
import { UserTypeEnum } from 'enum/user/user_type';
import { OrderTypeEnum } from 'enum/order/order_type';
import { eventNameEnum } from 'enum/report/event_name';

/**
 * 交易用户
 */
export interface TradingUsers {
  /**
   * 转账用户编号
   * @tips 如果传入from_wallet_id, 则不需要这个参数
   */
  from_user_id?: string;
  /**
   * 收账用户编号
   * @tips 如果传入to_wallet_id, 则不需要这个参数
   */
  to_user_id?: string;
  /**
   * 转账钱包编号
   * @tips 如果传入from_user_id, 则不需要这个参数. 传入该参数将忽略from_user_id
   */
  from_wallet_id?: string;
  /**
   * 收账钱包编号
   * @tips 如果传入to_user_id, 则不需要这个参数. 传入该参数将忽略to_user_id
   */
  to_wallet_id?: string;
}

export interface WatBgWallet {
  /**
   * 钱包编号
   */
  wallet_id: string;
  /**
   * 钱包名称
   */
  name: string;
  /**
   * 钱包备注
   */
  remark: string;
}

/**
 * WatBg Service
 */
export default class WatBg extends Service {
  logPrefix = '[service.WatBg]';

  /**
   * getMicroServiceConf
   */
  public getMicroServiceConf() {
    const { ctx } = this;
    const microService: Record<string, any> = _.get(ctx.app.config, 'custom.microservices.watBg') || {};
    return microService;
  }

  /**
   * getWalletConfById
   * @param id id
   */
  public getWalletConfById(id: string): WatBgWallet {
    let wallet;
    const microService = this.getMicroServiceConf();
    const wallets: WatBgWallet[] = _.get(microService, 'wallets') || [];
    if (wallets.length > 0) {
      wallet = wallets.find(item => {
        return item.wallet_id === id;
      });
    }
    return wallet;
  }

  /**
   * getWalletConfByName
   * @param name name
   */
  public getWalletConfByName(name: string): WatBgWallet {
    let wallet;
    const microService = this.getMicroServiceConf();
    const wallets: WatBgWallet[] = _.get(microService, 'wallets') || [];
    if (wallets.length > 0) {
      wallet = wallets.find(item => {
        return item.name === name;
      });
    }
    return wallet;
  }

  public getWalletIdByName(name: string): string {
    const wallet = this.getWalletConfByName(name);
    const id = _.get(wallet, 'wallet_id', '');
    this.ctx.logger.info(`${this.logPrefix}getWalletIdByName`, name, id);
    return id;
  }

  /**
   * uWalletDetail
   * @param userId userId
   */
  public async uWalletDetail(userId: string): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}uWalletDetail begin.`, {
      userId,
    });

    const microService: Record<string, any> = ctx.app.config.custom.microservices.watBg;

    const apiUrl = `${microService.baseUrl}/open/wallet/v1/u_wallet_detail/${userId}`;

    const curlOptions: any = {
      method: 'GET',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    // ctx.logger.info(`${this.logPrefix}uWalletDetail done.`);
    return rspData;
  }

  /**
   * transferByBalance
   * @param tradingUsers tradingUsers
   * @param amount amount
   * @param transferType transferType
   */
  public async transferByBalance(tradingUsers: TradingUsers, amount: number, transferType: number): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}transferByBalance begin.`, {
      amount,
      transferType,
      ...tradingUsers,
    });

    const microService: Record<string, any> = ctx.app.config.custom.microservices.watBg;

    const apiUrl = `${microService.baseUrl}/open/wallet/v1/transfer_by_balance`;
    const payload: Record<string, any> = {
      amount,
      transfer_type: transferType,
      ...tradingUsers,
    };

    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: payload,
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          case 1210: {
            throw new ExError('TMT_BALANCE_NOT_ENOUGH', errMsg);
            // break;
          }
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    // if (!result) {
    //   ctx.logger.error(`${this.logPrefix}call api(${apiUrl}) failed.`, result);
    // }
    ctx.logger.info(`${this.logPrefix}transferByBalance rspData. ${JSON.stringify(rspData)}`);
    ctx.logger.info(`${this.logPrefix}transferByBalance done.`);
    return rspData;
  }

  /**
   * transferByDeposit
   * @param fundPoolWalletId fundPoolWalletId
   * @param tradingUsers tradingUsers
   * @param amount amount
   * @param transferType transferType
   */
  public async transferByDeposit(fundPoolWalletId: string, tradingUsers: TradingUsers, amount: number, transferType: number, extend?: any, tradeInfo?: any): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} transferByDeposit begin.`, JSON.stringify({
      tradingUsers,
      amount,
      transferType,
      extend,
    }));

    const microService: Record<string, any> = this.getMicroServiceConf();

    const apiUrl = `${microService.baseUrl}/open/wallet/v1/transfer_by_deposit`;
    const payload: Record<string, any> = {
      fund_pool_wallet_id: fundPoolWalletId,
      amount,
      transfer_type: transferType,
      ...tradingUsers,
      extends: extend,
    };

    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: payload,
      dataType: 'json',
      timeout: 60000,
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        if (tradeInfo.item_withdraw_orderId) {
          ctx.service.item.tradeFundErr.findOneAndUpdate({
              item_withdraw_orderId: tradeInfo.item_withdraw_orderId,
            },
            tradeInfo,
            {
              new: true,
              upsert: true,
              useFindAndModify: true,
            });
        } else {
          ctx.service.item.tradeFundErr.findOneAndUpdate({
              sale_order_id: tradeInfo.sale_order_id,
            },
            tradeInfo,
            {
              new: true,
              upsert: true,
              useFindAndModify: true,
            });
        }
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, resData.desc);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    // if (!result) {
    //   ctx.logger.error(`${this.logPrefix}call api(${apiUrl}) failed.`, result);
    // }

    ctx.logger.info(`${this.logPrefix}transferByDeposit done.`);
    return rspData;
  }

  /**
   * poolDepositTransfer
   * @param fundPoolWalletId fundPoolWalletId
   * @param tradingUsers tradingUsers
   * @param amount amount
   */
  public async poolDepositTransfer(fundPoolWalletId: string, tradingUsers: TradingUsers, amount: number, extend?: any, tradeInfo?: any): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}poolDepositTransfer begin.`, fundPoolWalletId, tradingUsers, amount);

    const microService: Record<string, any> = this.getMicroServiceConf();

    const apiUrl = `${microService.baseUrl}/open/wallet/v1/pool_deposit_transfer`;
    const payload: Record<string, any> = {
      fund_pool_wallet_id: fundPoolWalletId,
      amount,
      ...tradingUsers,
      extends: extend,
    };
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: payload,
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        // 记录错误日志
        console.info(tradeInfo);
        if (tradeInfo.item_withdraw_orderId) {
          ctx.service.item.tradeFundErr.findOneAndUpdate({
              item_withdraw_orderId: tradeInfo.item_withdraw_orderId,
            },
            tradeInfo,
            {
              new: true,
              upsert: true,
              useFindAndModify: true,
            });
        } else {
          ctx.service.item.tradeFundErr.findOneAndUpdate({
              sale_order_id: tradeInfo.sale_order_id,
            },
            tradeInfo,
            {
              new: true,
              upsert: true,
              useFindAndModify: true,
            });
        }

        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          case 1211: {
            throw new ExError('TMT_BALANCE_NOT_ENOUGH', errMsg);
            // break;
          }
          default: {
            throw new ExError('TMT_CURL_API_FAIL', errMsg);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    // if (!result) {
    //   ctx.logger.error(`${this.logPrefix}call api(${apiUrl}) failed.`, result);
    // }

    ctx.logger.info(`${this.logPrefix}poolDepositTransfer done.`);
    return rspData;
  }

  /**
   * poolDepositBulkTransfer
   * @param fundPoolWalletId fundPoolWalletId
   * @param bulkOps bulkOps
   */
  public async poolDepositBulkTransfer(fundPoolWalletId: string, bulkOps: any[]): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}poolDepositBulkTransfer begin.`, fundPoolWalletId, bulkOps);

    const microService: Record<string, any> = this.getMicroServiceConf();

    const apiUrl = `${microService.baseUrl}/open/wallet/v1/pool_deposit_bulk_transfer`;

    const payload: Record<string, any> = {
      fund_pool_wallet_id: fundPoolWalletId,
      bulk_ops: bulkOps,
    };

    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: payload,
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError('TMT_CURL_API_FAIL', errMsg);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    // if (!result) {
    //   ctx.logger.error(`${this.logPrefix}call api(${apiUrl}) failed.`, result);
    // }

    ctx.logger.info(`${this.logPrefix}poolDepositBulkTransfer done.`);
    return rspData;
  }

  public async setWalletSummary(data) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} setWalletSummary ${JSON.stringify(data)}`);
    const _id = data.user_id;
    if (!_id) {
      ctx.logger.info(`${this.logPrefix} setWalletSummary fail, user id missing`);
      return;
    }

    const form = _.pick(data, [ 'summary' ]);
    const setForm = {};
    objToDotKeys(setForm, form, '');

    const updateRet = await ctx.service.user.userState.updateUserState({
      user_id: _id,
    }, {
      $set: setForm,
    }, {
      upsert: true,
    });
    if (!updateRet) {
      ctx.logger.error(`${this.logPrefix} setWalletSummary fail. ${JSON.stringify(setForm)}`);
    } else {
      ctx.logger.error(`${this.logPrefix} setWalletSummary success. ${JSON.stringify(setForm)}`);
    }
  }

  /**
   * 不可用余额资金池交易
   * @param fund_pool_wallet_id 资金池id
   * @param user_id 用户id
   * @param amount  金额
   * @param transfer_type 交易方式
   */
  public async unavailableBalance(fund_pool_wallet_id: any, user_id: any, amount: number, transfer_type: any) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} unavailableBalance begin `, fund_pool_wallet_id, user_id, amount, transfer_type);

    // const unavailableBalanceConfig = _.get(ctx.app.config.custom, 'wallet_pool.unavailableBalance');
    // ctx.logger.info(`${this.logPrefix} unavailableBalanceConfig `, unavailableBalanceConfig);
    // if (transfer_type === PoolVirtualTransferEnum.Return) {
    //   if (!unavailableBalanceConfig.enable) {
    //     throw new ExError('TMT_UNAVAILABLEBALANCE_DISABLE', 'unavailableBalance disable');
    //   }
    //   const rate = unavailableBalanceConfig.rate || 100;
    //   amount = _.floor(amount * rate / 100);
    // }
    const microService: Record<string, any> = this.getMicroServiceConf();

    const apiUrl = `${microService.baseUrl}/open/wallet/v1/unavailable_balance_transfer`;

    const payload: Record<string, any> = {
      fund_pool_wallet_id,
      user_id,
      amount,
      transfer_type,
    };

    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: payload,
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError('TMT_CURL_API_FAIL', errMsg);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    // if (!result) {
    //   ctx.logger.error(`${this.logPrefix}call api(${apiUrl}) failed.`, result);
    // }

    ctx.logger.info(`${this.logPrefix}poolDepositBulkTransfer done.`);
    return rspData;
  }

  /**
   * 查询用户余额与资金池余额
   * @param user_id  用户id
   * @param fund_pool_wallet_id  资金池id
   */
  public async getUserBalanceAndPoolBalance(user_id: any, fund_pool_wallet_id: any) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} getUserBalanceAndPoolBalance begin `, user_id, fund_pool_wallet_id);
    const microService: Record<string, any> = this.getMicroServiceConf();
    let wallet_ids;
    if (fund_pool_wallet_id instanceof Array) {
      wallet_ids = _.join(fund_pool_wallet_id, ',');
    } else {
      wallet_ids = fund_pool_wallet_id;
    }

    const apiUrl = `${microService.baseUrl}/open/wallet/v1/get_user_balance_and_pool_balance?user_id=${user_id}&wallet_ids=${wallet_ids}`;

    const curlOptions: any = {
      method: 'GET',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError('TMT_CURL_API_FAIL', errMsg);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    // if (!result) {
    //   ctx.logger.error(`${this.logPrefix}call api(${apiUrl}) failed.`, result);
    // }

    ctx.logger.info(`${this.logPrefix}poolDepositBulkTransfer done.`, rspData);
    return rspData;
  }

  /**
   * 资金池代充
   * 描述： A池转账至B池，并指定此款项为某个用户所有
   * @param tradingUsers tradingUsers
   * @param amount amount
   * @param transferType transferType
   */
  public async payForAnotherByBalance(tradingUsers: TradingUsers, amount: number, transferType: number, extend?: any): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}payForAnotherByBalance begin.`, {
      amount,
      transferType,
      ...tradingUsers,
      extends: extend,
    });

    const microService: Record<string, any> = ctx.app.config.custom.microservices.watBg;

    const apiUrl = `${microService.baseUrl}/open/wallet/v1/pay_for_another_by_balance`;
    const payload: Record<string, any> = {
      amount,
      transfer_type: transferType,
      ...tradingUsers,
      extends: extend,
    };

    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: payload,
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          case 1210: {
            throw new ExError('TAK_UNBOX_DEFRAY_FAILED', errMsg);
            // break;
          }
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TAK_CURL_API_FAIL', errMsg);
    }
    // if (!result) {
    //   ctx.logger.error(`${this.logPrefix}call api(${apiUrl}) failed.`, result);
    // }

    ctx.logger.info(`${this.logPrefix}transferByBalance done.`);
    return rspData;
  }

  /**
   * 用户购买用户物品
   * @param item
   * @param itemWithDrawOrder
   * @param err_seat
   */
  public async userToUser(item: any, itemWithDrawOrder: any, err_seat = 0) {
    const { ctx } = this;
    let userWallet = 'bs_user_balance';
    const balanceWalletName = 'bs_user_trade_balance';
    let balance_from = {
      balance_from: 'user_balance',
    };
    // bs_user_balance以前是用户余额池 易宝商户余额拆分后用易宝余额池 bs_user_luckyitem_balance
    // payer是wat那边的支付商编号 11是原生支付宝 3006是易宝的，3是余额支付 4是商户余额支付
    if (itemWithDrawOrder.extends.payer === 3006 || itemWithDrawOrder.extends.payer === 4) {
      userWallet = 'bs_user_luckyitem_balance';
      balance_from = {
        balance_from: 'spdb',
      };
    }

    const tradeTipName = 'bs_trade_tip';
    const balanceWalletConf = await ctx.service.watBg.getWalletConfByName(balanceWalletName);
    const userWalletConf = await ctx.service.watBg.getWalletConfByName(userWallet);
    const tradeTipConf = await ctx.service.watBg.getWalletConfByName(tradeTipName);
    const balanceWalletId = balanceWalletConf.wallet_id;
    const userWalletId = userWalletConf.wallet_id;
    const tradeTipId = tradeTipConf.wallet_id;
    const saleOrder = await ctx.service.item.saleOrder.queryJustOne({ _id: item.sale_order_id });
    const buy_user_id = itemWithDrawOrder.user_id;
    const sale_user_id = saleOrder.sale_user_id;

    ctx.logger.info(`${this.logPrefix} userToUser begini! buy_user_id :${buy_user_id} sale_user_id:${sale_user_id}`);

    const extend = {
      from_user_id: String(buy_user_id),
      balance_from: balance_from.balance_from,
      itemWithDrawOrderId: String(itemWithDrawOrder._id),
    };

    if (err_seat <= 1) {
      // 买卖池买家资金转平台
      await this.poolDepositTransfer(balanceWalletId, {
          from_user_id: String(buy_user_id),
          to_wallet_id: balanceWalletId,
        }, _.get(item, 'pay_amount'), extend, {
          item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
          sale_order_id: _.get(item, 'sale_order_id'),
          trade_type: TradeTypeEnum.UserToUser,
          err_seat: 1,
        },
      );
    }

    if (err_seat <= 2) {
      // 买卖池平台资金抽手续费并转给卖家
      await this.poolDepositTransfer(balanceWalletId, {
          from_wallet_id: balanceWalletId,
          to_user_id: String(sale_user_id),
        }, _.get(item, 'pay_amount') - _.get(item, 'fee', 0), extend,
        {
          item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
          sale_order_id: _.get(item, 'sale_order_id'),
          trade_type: TradeTypeEnum.UserToUser,
          err_seat: 2,
        },
      );
    }

    if (err_seat <= 3) {
      if (_.get(item, 'fee', 0) > 0) {
        // 买卖池平台资金抽手续费转到用户担保池-用户担保买卖手续费
        await this.poolDepositTransfer(balanceWalletId, {
            from_wallet_id: balanceWalletId,
            to_wallet_id: tradeTipId,
          }, _.get(item, 'fee', 0), extend, {
            item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
            sale_order_id: _.get(item, 'sale_order_id'),
            trade_type: TradeTypeEnum.UserToUser,
            err_seat: 3,
          },
        );
      }
    }

    if (err_seat <= 4) {
      // 买卖池卖家资金转余额池
      const amount = _.get(item, 'pay_amount');
      const fee = _.get(item, 'fee', 0);
      await this.transferByDeposit(balanceWalletId, {
          from_user_id: String(sale_user_id),
          to_wallet_id: userWalletId,
        }, amount - fee, 211, extend, {
          item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
          sale_order_id: _.get(item, 'sale_order_id'),
          trade_type: TradeTypeEnum.UserToUser,
          err_seat: 4,
        },
      );
      await this.isYopTransfer(itemWithDrawOrder, buy_user_id, sale_user_id, fee, amount);
    }

  }

  /**
   * 用户购买主播物品 用户担保池转担保池庄池
   */
  public async userToPromoter(item: any, itemWithDrawOrder: any, err_seat = 0) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} userToPromoter begini!`);

    const balanceWalletName = 'bs_user_trade_balance';
    const tradeTipName = 'bs_trade_tip';
    const tradeProfitName = 'bs_trade_profit';
    const balanceWalletConf = await ctx.service.watBg.getWalletConfByName(balanceWalletName);
    const tradeTipConf = await ctx.service.watBg.getWalletConfByName(tradeTipName);
    const tradeProfitConf = await ctx.service.watBg.getWalletConfByName(tradeProfitName);
    const balanceWalletId = balanceWalletConf.wallet_id;
    const tradeTipId = tradeTipConf.wallet_id;
    const tradeProfitId = tradeProfitConf.wallet_id;
    const buy_user_id = itemWithDrawOrder.user_id;
    if (err_seat <= 1) {
      // 买卖池买家资金转平台
      await this.poolDepositTransfer(balanceWalletId, {
          from_user_id: String(buy_user_id),
          to_wallet_id: balanceWalletId,
        }, _.get(item, 'pay_amount'), {}, {
          item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
          sale_order_id: _.get(item, 'sale_order_id'),
          trade_type: TradeTypeEnum.UserToPromoter,
          err_seat: 1,
        },
      );
    }

    if (err_seat <= 2) {
      // 买卖池平台资金抽手续费转到用户担保池-用户担保买卖手续费
      await this.poolDepositTransfer(balanceWalletId, {
          from_wallet_id: balanceWalletId,
          to_wallet_id: tradeTipId,
        }, _.get(item, 'fee', 0), {}, {
          item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
          sale_order_id: _.get(item, 'sale_order_id'),
          trade_type: TradeTypeEnum.UserToPromoter,
          err_seat: 2,
        },
      );
    }

    if (err_seat <= 3) {
      // 买卖池剩余资金转用户担保池-用户担保买卖利润
      await this.poolDepositTransfer(balanceWalletId, {
          from_wallet_id: balanceWalletId,
          to_wallet_id: tradeProfitId,
        }, _.get(item, 'pay_amount') - _.get(item, 'fee', 0), {}, {
          item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
          sale_order_id: _.get(item, 'sale_order_id'),
          trade_type: TradeTypeEnum.UserToPromoter,
          err_seat: 3,
        },
      );
    }
  }

  /**
   * 主播买用户物品 -> 庄池转用户余额
   */
  public async promoterToUser(item: any, itemWithDrawOrder: any) {
    const { ctx } = this;

    const saleOrder = await ctx.service.item.saleOrder.queryData({ _id: item.sale_order_id });
    const amount = saleOrder.order_amount;
    if (!saleOrder) {
      throw new ExError('TMT_DATA_NOT_EXIST');
    }
    const buy_user_id = _.get(saleOrder, 'buy_user_id');
    const sale_user_id = _.get(saleOrder, 'sale_user_id');
    let userWallet = 'bs_user_balance';
    let balance_from = {
      balance_from: 'user_balance',
    };
    // bs_user_balance以前是用户余额池 易宝商户余额拆分后用易宝余额池 bs_user_luckyitem_balance
    // payer是wat那边的支付商编号 11是原生支付宝 3006是易宝的，3是余额支付 4是商户余额支付
    if (itemWithDrawOrder.extends.payer === 3006 || itemWithDrawOrder.extends.payer === 4) {
      userWallet = 'bs_user_luckyitem_balance';
      balance_from = {
        balance_from: 'spdb',
      };
    }

    const extend = {
      from_user_id: String(buy_user_id),
      balance_from: balance_from.balance_from,
      itemWithDrawOrderId: String(itemWithDrawOrder._id),
    };

    const balanceWalletName = 'bs_user_trade_balance';
    const promoterTradetName = 'bs_promoter_trade_balance';
    const balanceWalletConf = await ctx.service.watBg.getWalletConfByName(balanceWalletName);
    const promoterTradetConf = await ctx.service.watBg.getWalletConfByName(promoterTradetName);
    const userWalletConf = await ctx.service.watBg.getWalletConfByName(userWallet);
    const balanceWalletId = balanceWalletConf.wallet_id;
    const promoterTradetId = promoterTradetConf.wallet_id;
    const userWalletId = userWalletConf.wallet_id;

    // 主播账号担保池资金转庄池
    await this.poolDepositTransfer(promoterTradetId, {
        from_user_id: String(buy_user_id),
        to_wallet_id: promoterTradetId,
      },
      amount,
      extend,
    );
    // 买卖池平台资金抽手续费并转给卖家
    await this.poolDepositTransfer(balanceWalletId, {
        from_wallet_id: balanceWalletId,
        to_user_id: String(sale_user_id),
      },
      amount,
      extend,
    );
    // 买卖池卖家资金转余额池
    await this.transferByDeposit(balanceWalletId, {
        from_user_id: String(sale_user_id),
        to_wallet_id: userWalletId,
      },
      amount,
      211, extend,
    );
  }

  /**
   * 主播买主播物品 -> 主播担保池转账
   */
  public async promoterTopromoter(sale_order_id: ObjectId, amount: number, diffAmount: number) {
    const { ctx } = this;

    const saleOrder = await ctx.service.item.saleOrder.queryData({ _id: sale_order_id });
    if (!saleOrder) {
      throw new ExError('TMT_DATA_NOT_EXIST');
    }
    const buy_user_id = _.get(saleOrder, 'buy_user_id');
    const sale_user_id = _.get(saleOrder, 'sale_user_id');

    const balanceWalletName = 'bs_promoter_trade_balance';
    const userWallet = 'bs_promoter_balance';
    const balanceWalletConf = await ctx.service.watBg.getWalletConfByName(balanceWalletName);
    const userWalletConf = await ctx.service.watBg.getWalletConfByName(userWallet);
    const balanceWalletId = balanceWalletConf.wallet_id;
    const userWalletId = userWalletConf.wallet_id;

    await this.poolDepositTransfer(balanceWalletId, {
        from_user_id: String(buy_user_id),
        to_wallet_id: balanceWalletId,
      },
      amount,
      {},
    );
    // 买卖池平台资金抽手续费并转给卖家
    await this.poolDepositTransfer(balanceWalletId, {
        from_wallet_id: balanceWalletId,
        to_user_id: String(sale_user_id),
      },
      diffAmount,
      {},
    );
    // 买卖池卖家资金转余额池
    const extend = {
      from_user_id: String(buy_user_id),
    };
    await this.transferByDeposit(balanceWalletId, {
        from_user_id: String(sale_user_id),
        to_wallet_id: userWalletId,
      },
      diffAmount,
      211, extend,
    );
  }

  /**
   * 机器人  用户购买用户物品
   * @param sale_order_id
   * @param amount
   */
  public async robotToUser(saleOrder: any, robotUserId, err_seat = 0) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} robotToUser begini!`);

    const robotBalanceWalletName = 'bs_robot_trade_balance';
    const robotWallet = 'bs_robot_balance';
    const userWallet = 'bs_user_balance';
    const robotTradeTipName = 'bs_robot_trade_tip';
    const robotBalanceWalletConf = await ctx.service.watBg.getWalletConfByName(robotBalanceWalletName);
    const userWalletConf = await ctx.service.watBg.getWalletConfByName(userWallet);
    const robotWalletConf = await ctx.service.watBg.getWalletConfByName(robotWallet);
    const robotTradeTipConf = await ctx.service.watBg.getWalletConfByName(robotTradeTipName);
    const robotBalanceWalletId = robotBalanceWalletConf.wallet_id;
    const userWalletId = userWalletConf.wallet_id;
    const robotWalletId = robotWalletConf.wallet_id;
    const robotTradeTipId = robotTradeTipConf.wallet_id;

    const saleUserId = _.get(saleOrder, 'sale_user_id');

    // 机器人余额池转担保池
    if (err_seat <= 1) {
      await this.transferByDeposit(robotWalletId, {
          from_user_id: robotUserId,
          to_wallet_id: robotBalanceWalletId,
        },
        _.get(saleOrder, 'order_amount'),
        211, {}, {
          sale_order_id: _.get(saleOrder, '_id'),
          trade_type: TradeTypeEnum.RobotToUser,
          err_seat: 1,
        },
      );
    }
    // 担保池机器人转庄池
    if (err_seat <= 2) {
      await this.poolDepositTransfer(robotBalanceWalletId, {
          from_user_id: robotUserId,
          to_wallet_id: robotBalanceWalletId,
        },
        _.get(saleOrder, 'order_amount'),
        {},
        {
          sale_order_id: _.get(saleOrder, '_id'),
          trade_type: TradeTypeEnum.RobotToUser,
          err_seat: 2,
        },
      );
    }
    // 担保池转手续费到机器人手续费池
    if (err_seat <= 3 && _.get(saleOrder, 'tip_fee', 0) > 0) {
      await this.poolDepositTransfer(robotBalanceWalletId, {
          from_wallet_id: robotBalanceWalletId,
          to_wallet_id: robotTradeTipId,
        },
        _.get(saleOrder, 'tip_fee', 0),
        {},
        {
          sale_order_id: _.get(saleOrder, '_id'),
          trade_type: TradeTypeEnum.RobotToUser,
          err_seat: 3,
        },
      );
    }
    if (err_seat <= 4) {
      // 担保池转剩余价值到用户
      await this.poolDepositTransfer(robotBalanceWalletId, {
          from_wallet_id: robotBalanceWalletId,
          to_user_id: saleUserId,
        },
        _.get(saleOrder, 'order_amount') - _.get(saleOrder, 'tip_fee', 0),
        {},
        {
          sale_order_id: _.get(saleOrder, '_id'),
          trade_type: TradeTypeEnum.RobotToUser,
          err_seat: 4,
        },
      );
    }
    // 担保池转剩余价值到用户余额池
    if (err_seat <= 5) {
      const extend = {
        from_user_id: robotUserId,
      };
      await this.transferByDeposit(robotBalanceWalletId, {
          from_user_id: saleUserId,
          to_wallet_id: userWalletId,
        },
        _.get(saleOrder, 'order_amount') - _.get(saleOrder, 'tip_fee', 0),
        211, extend, {
          sale_order_id: _.get(saleOrder, '_id'),
          trade_type: TradeTypeEnum.RobotToUser,
          err_seat: 5,
        },
      );
    }
  }

  /**
   * 出售物品算入记账池
   */
  public async saleoutItemPool(saleOrder: any, err_seat = 0) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} userToUser begini!`);

    const saleoutItemWalletName = 'bs_saleout_item';
    const saleoutItemWalletConf = await ctx.service.watBg.getWalletConfByName(saleoutItemWalletName);
    const saleoutItemWalletId = saleoutItemWalletConf.wallet_id;

    const saleUserId = _.get(saleOrder, 'sale_user_id');

    if (err_seat <= 1) {
      // 担保池转剩余价值到用户
      await this.poolDepositTransfer(saleoutItemWalletId, {
          from_wallet_id: saleoutItemWalletId,
          to_user_id: saleUserId,
        },
        _.get(saleOrder, 'order_amount') - _.get(saleOrder, 'tip_fee', 0),
        {},
        {
          sale_order_id: _.get(saleOrder, '_id'),
          trade_type: TradeTypeEnum.FinancialToUser,
          err_seat: 1,
        },
      );
    }
  }

  /**
   * 检查支付密码是否正确
   */
  public async checkUserWalletPayPwd(user_id: any, pwd: string) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} checkUserWalletPayPwd begin `, user_id, pwd);
    const microService: Record<string, any> = this.getMicroServiceConf();

    const apiUrl = `${microService.baseUrl}/open/wallet/v1/check_user_wallet_pay_pwd?user_id=${user_id}&pwd=${pwd}`;

    const curlOptions: any = {
      method: 'GET',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          case 80205230:
            throw new ExError(80205230);
          case 80205229:
            throw new ExError(80205229);
          default: {
            ctx.logger.error(errMsg);
            throw new ExError('TMT_CURL_API_FAIL');
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    return rspData;
  }

  /**
   * autoRefund
   * @param userId userId
   */
  public async autoRefund(data: any): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}autoRefund begin.`, {
      data,
    });

    const microService: Record<string, any> = ctx.app.config.custom.microservices.watBg;

    const apiUrl = `${microService.baseUrl}/open/wallet/recharge/v1/autoRefund`;

    const curlOptions: any = {
      method: 'POST',
      data,
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    // ctx.logger.info(`${this.logPrefix}uWalletDetail done.`);
    return rspData;
  }

  /**
   * 用户购买用户物品
   * @param sale_user_id
   * @param itemWithDrawOrderId
   * @param err_seat
   */
  public async userToUserOfLcl(sale_user_id, itemWithDrawOrderId, err_seat = 0) {
    const { ctx } = this;

    const itemWithDrawOrder = await ctx.service.item.itemWithdrawOrder.queryItemWithdrawOrder({
      _id: itemWithDrawOrderId,
    });
    const user = await ctx.service.user.user.queryUsersByRedis(sale_user_id);
    const balanceWalletName = 'bs_user_trade_balance';
    let userWallet = 'bs_user_balance';
    let balance_from = {
      balance_from: 'user_balance',
    };
    if (user.type === UserTypeEnum.Promoter) {
      // 主播收益收归主播交易担保池
      userWallet = 'bs_promoter_trade_balance';
    } else {
      // bs_user_balance以前是用户余额池 易宝商户余额拆分后用易宝余额池 bs_user_luckyitem_balance
      // payer是wat那边的支付商编号 11是原生支付宝 3006是易宝的，3是余额支付 4是商户余额支付
      if (itemWithDrawOrder.extends.payer === 3006 || itemWithDrawOrder.extends.payer === 4) {
        userWallet = 'bs_user_luckyitem_balance';
        balance_from = {
          balance_from: 'spdb',
        };
      }
    }

    const tradeTipName = 'bs_trade_tip';
    const balanceWalletConf = await ctx.service.watBg.getWalletConfByName(balanceWalletName);
    const userWalletConf = await ctx.service.watBg.getWalletConfByName(userWallet);
    const tradeTipConf = await ctx.service.watBg.getWalletConfByName(tradeTipName);
    const balanceWalletId = balanceWalletConf.wallet_id;
    const userWalletId = userWalletConf.wallet_id;
    const tradeTipId = tradeTipConf.wallet_id;
    const buy_user_id = itemWithDrawOrder.user_id;

    ctx.logger.info(`${this.logPrefix} userToUser begini! buy_user_id :${buy_user_id} sale_user_id:${sale_user_id}`);
    const extend = {
      from_user_id: String(buy_user_id),
      balance_from: balance_from.balance_from,
      itemWithDrawOrderId: String(itemWithDrawOrder._id),
    };

    if (err_seat <= 1) {
      // 买卖池买家资金转平台
      await this.poolDepositTransfer(balanceWalletId, {
          from_user_id: String(buy_user_id),
          to_wallet_id: balanceWalletId,
        }, _.get(itemWithDrawOrder, 'pay_amount'), extend, {
          item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
          trade_type: TradeTypeEnum.UserToUser,
          err_seat: 1,
        },
      );
    }

    if (err_seat <= 2) {
      // 买卖池平台资金抽手续费并转给卖家
      await this.poolDepositTransfer(balanceWalletId, {
          from_wallet_id: balanceWalletId,
          to_user_id: String(sale_user_id),
        }, _.get(itemWithDrawOrder, 'pay_amount') - _.get(itemWithDrawOrder, 'extends.tip_fee', 0), extend,
        {
          item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
          trade_type: TradeTypeEnum.UserToUser,
          err_seat: 2,
        },
      );
    }

    if (err_seat <= 3) {
      if (_.get(itemWithDrawOrder, 'extends.tip_fee', 0) > 0) {
        // 买卖池平台资金抽手续费转到用户担保池-用户担保买卖手续费
        await this.poolDepositTransfer(balanceWalletId, {
            from_wallet_id: balanceWalletId,
            to_wallet_id: tradeTipId,
          }, _.get(itemWithDrawOrder, 'extends.tip_fee', 0), extend, {
            item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
            trade_type: TradeTypeEnum.UserToUser,
            err_seat: 3,
          },
        );
      }
    }

    if (err_seat <= 4) {
      // 买卖池卖家资金转余额池
      const amount = _.get(itemWithDrawOrder, 'pay_amount');
      const fee = _.get(itemWithDrawOrder, 'extends.tip_fee', 0);
      await this.transferByDeposit(balanceWalletId, {
          from_user_id: String(sale_user_id),
          to_wallet_id: userWalletId,
        }, amount - fee, 211, extend, {
          item_withdraw_orderId: _.get(itemWithDrawOrder, '_id'),
          trade_type: TradeTypeEnum.UserToUser,
          err_seat: 4,
        },
      );
      await this.isYopTransfer(itemWithDrawOrder, buy_user_id, sale_user_id, fee, amount);
    }
  }

  /**
   * lcl_open_log
   */
  public async lcl_open_log(lcl_yop_pay: any): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}lcl_open_log begin.`, {
      lcl_yop_pay,
    });

    const microService: Record<string, any> = ctx.app.config.custom.microservices.watBg;

    const apiUrl = `${microService.baseUrl}/open/wallet/recharge/v1/lcl_open_log`;

    const curlOptions: any = {
      method: 'POST',
      data: {
        lcl_yop_pay,
      },
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    // ctx.logger.info(`${this.logPrefix}uWalletDetail done.`);
    return rspData;
  }

  public async yopWithdrawChange(itemWithDrawOrderId: string, buyUserId: string, sellUserId: string, amount: number, fee: number) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} yopWithdrawChange begin.`, JSON.stringify({ buyUserId, sellUserId, amount, fee }));

    const microService: Record<string, any> = this.getMicroServiceConf();
    const apiUrl = `${microService.baseUrl}/open/wallet/yopRecharge/v1/yopWithdrawChange`;
    const payload: Record<string, any> = {
      itemWithDrawOrderId,
      amount,
      buyUserId,
      sellUserId,
      fee,
    };

    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: payload,
      dataType: 'json',
      timeout: 60000,
    };

    // 发送请求
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
            // break;
          }
        }
      } else {
        ctx.logger.info(`${this.logPrefix}yopWithdrawChange done. ${res.data}`);
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
  }

  // 判断是否有要进行转让可提现订单
  public async isYopTransfer(itemWithDrawOrder: any, buy_user_id, sale_user_id: ObjectId, fee: number, amount: number) {
    if (itemWithDrawOrder.extends.payer === 4) {
      await this.yopWithdrawChange(itemWithDrawOrder._id.toString(), buy_user_id.toString(), sale_user_id.toString(), amount, fee);
    }
  }

  /**
   * 用户购买用户物品
   * @param item  商品信息
   * @param itemWithDrawOrder  业务订单对象
   * @param forNum  批量购买时循环的次数
   */
  public async tradePoolTransfer(item: any, itemWithDrawOrder: any, forNum: number) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}tradePoolTransferBegin`, JSON.stringify(itemWithDrawOrder));
    const userTradePoolName = 'bs_user_trade_balance';
    let userBalancePool = 'bs_user_balance';
    let balance_from = {
      balance_from: 'user_balance',
    };
    // bs_user_balance以前是用户余额池 易宝商户余额拆分后用易宝余额池 bs_user_luckyitem_balance
    // payer是wat那边的支付商编号 11是原生支付宝 3006是易宝的，3是余额支付 4是商户余额支付
    if (itemWithDrawOrder.extends.payer === 3006 || itemWithDrawOrder.extends.payer === 4) {
      userBalancePool = 'bs_user_luckyitem_balance';
      balance_from = {
        balance_from: 'spdb',
      };
    }
    const tradeTipName = 'bs_trade_tip';
    const userTradePoolNameConf = await ctx.service.watBg.getWalletConfByName(userTradePoolName);
    const userBalancePoolConf = await ctx.service.watBg.getWalletConfByName(userBalancePool);
    const tradeTipConf = await ctx.service.watBg.getWalletConfByName(tradeTipName);
    const userTradePoolId = userTradePoolNameConf.wallet_id;
    const userWalletPoolId = userBalancePoolConf.wallet_id;
    const tradeTipPoolId = tradeTipConf.wallet_id;
    const saleOrder = await ctx.service.item.saleOrder.queryJustOne({ _id: item.sale_order_id });
    const buy_user_id = itemWithDrawOrder.user_id;
    const sale_user_id = saleOrder.sale_user_id;

    const amount = _.get(item, 'pay_amount');
    const fee = _.get(item, 'fee', 0);
    const sellGetAmount = _.floor(amount - fee);

    const itemWithDrawOrderId = String(itemWithDrawOrder._id);
    const extend = {
      from_user_id: String(buy_user_id),
      balance_from: balance_from.balance_from,
      itemWithDrawOrderId,
    };
    ctx.logger.info(`${this.logPrefix}dataFromExtend`, extend);
    const buyToPlatform = {
      // 从买家担保池移到平台担保池
      pool_wallet_id: userTradePoolId,
      from_user_id: buy_user_id,
      to_wallet_id: userTradePoolId,
      amount,
      extends: extend,
    };
    const tradeToTip = {
      // 从平台担保池移到平台手续费池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_wallet_id: tradeTipPoolId,
      amount: fee,
      extends: extend,
    };
    const platformToSell = {
      // 从平台担保池移到卖家担保池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_user_id: sale_user_id,
      amount: sellGetAmount,
      extends: extend,
    };
    const tradeToBalance = {
      // 从卖家担保池移到卖家余额池
      pool_wallet_id: userTradePoolId,
      from_user_id: sale_user_id,
      to_wallet_id: userWalletPoolId,
      amount: sellGetAmount,
      extends: extend,
      transfer_type: 211,
      payer: itemWithDrawOrder.extends.payer,
      yopOrderTransferMsg: {},
      is_send_bull: false,
    };
    if (itemWithDrawOrder.extends.payer === 4 && (forNum === itemWithDrawOrder.extends.quantity || itemWithDrawOrder.extends.order_type === OrderTypeEnum.Supply)) {
      // 批量购买的话 只在最后一个商品流转完的时候才进行易宝订单流转，减少并发 金额和手续费是取订单的，不是子订单的
      const allAmount = _.get(itemWithDrawOrder, 'pay_amount');
      const allFee = _.get(itemWithDrawOrder, 'extends.tip_fee', 0);
      tradeToBalance.yopOrderTransferMsg = {
        itemWithDrawOrderId,
        buyUserId: buy_user_id,
        sellUserId: sale_user_id,
        amount: allAmount,
        fee: allFee,
      };
      tradeToBalance.is_send_bull = true;
    }

    let requestParams;
    if (fee > 0) {
      requestParams = { dataFrom : [ buyToPlatform, tradeToTip, platformToSell, tradeToBalance ] };
    } else {
      requestParams = { dataFrom : [ buyToPlatform, platformToSell, tradeToBalance ] };
    }
    await this.tradePoolTransferRequest(requestParams);
  }

  /**
   * 用户批量购买用户物品
   * @param item  商品信息
   * @param itemWithDrawOrder  业务订单对象
   * @param forNum  批量购买时循环的次数
   */
  public async tradePoolTransferOfBatchBuy(item: any, itemWithDrawOrder: any) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}tradePoolTransferBegin`, itemWithDrawOrder);
    const userTradePoolName = 'bs_user_trade_balance';
    let userBalancePool = 'bs_user_balance';
    let balance_from = {
      balance_from: 'user_balance',
    };
    // bs_user_balance以前是用户余额池 易宝商户余额拆分后用易宝余额池 bs_user_luckyitem_balance
    // payer是wat那边的支付商编号 11是原生支付宝 3006是易宝的，3是余额支付 4是商户余额支付
    if (itemWithDrawOrder.extends.payer === 3006 || itemWithDrawOrder.extends.payer === 4) {
      userBalancePool = 'bs_user_luckyitem_balance';
      balance_from = {
        balance_from: 'spdb',
      };
    }
    const tradeTipName = 'bs_trade_tip';
    const userTradePoolNameConf = await ctx.service.watBg.getWalletConfByName(userTradePoolName);
    const userBalancePoolConf = await ctx.service.watBg.getWalletConfByName(userBalancePool);
    const tradeTipConf = await ctx.service.watBg.getWalletConfByName(tradeTipName);
    const userTradePoolId = userTradePoolNameConf.wallet_id;
    const userWalletPoolId = userBalancePoolConf.wallet_id;
    const tradeTipPoolId = tradeTipConf.wallet_id;
    const saleOrder = await ctx.service.item.saleOrder.queryJustOne({ _id: item.sale_order_id });
    const buy_user_id = itemWithDrawOrder.user_id;
    const sale_user_id = saleOrder.sale_user_id;

    const amount = _.get(item, 'pay_amount');
    const fee = _.get(item, 'fee', 0);
    const sellGetAmount = _.floor(amount - fee);

    const itemWithDrawOrderId = String(itemWithDrawOrder._id);
    const extend = {
      from_user_id: String(buy_user_id),
      balance_from: balance_from.balance_from,
      itemWithDrawOrderId,
    };
    ctx.logger.info(`${this.logPrefix}dataFromExtend`, extend);
    const buyToPlatform = {
      // 从买家担保池移到平台担保池
      pool_wallet_id: userTradePoolId,
      from_user_id: buy_user_id,
      to_wallet_id: userTradePoolId,
      amount,
      extends: extend,
    };
    const tradeToTip = {
      // 从平台担保池移到平台手续费池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_wallet_id: tradeTipPoolId,
      amount: fee,
      extends: extend,
    };
    const platformToSell = {
      // 从平台担保池移到卖家担保池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_user_id: sale_user_id,
      amount: sellGetAmount,
      extends: extend,
    };
    const tradeToBalance = {
      // 从卖家担保池移到卖家余额池
      pool_wallet_id: userTradePoolId,
      from_user_id: sale_user_id,
      to_wallet_id: userWalletPoolId,
      amount: sellGetAmount,
      extends: extend,
      transfer_type: 211,
      payer: itemWithDrawOrder.extends.payer,
      yopOrderTransferMsg: {},
      is_send_bull: false,
    };
    if (itemWithDrawOrder.extends.payer === 4) {
      tradeToBalance.yopOrderTransferMsg = {
        itemWithDrawOrderId,
        buyUserId: buy_user_id,
        sellUserId: sale_user_id,
        amount,
        fee,
      };
      tradeToBalance.is_send_bull = true;
    }

    let requestParams;
    if (fee > 0) {
      requestParams = { dataFrom : [ buyToPlatform, tradeToTip, platformToSell, tradeToBalance ] };
    } else {
      requestParams = { dataFrom : [ buyToPlatform, platformToSell, tradeToBalance ] };
    }
    await this.tradePoolTransferRequest(requestParams);
  }

  /**
   * tradePoolTransferRequest
   * @param dataFrom dataFrom
   */
  public async tradePoolTransferRequest(dataFrom: any): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}tradePoolTransferRequest begin.`, dataFrom);
    const microService: Record<string, any> = this.getMicroServiceConf();
    const apiUrl = `${microService.baseUrl}/open/wallet/v1/trade_pool_transfer_request`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: dataFrom,
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        ctx.service.morBg.tradePoolTransferErrWarn('', JSON.stringify(dataFrom));
        const errMsg = `${this.logPrefix}tradePoolTransferRequestFail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          case 1211: {
            throw new ExError('TMT_BALANCE_NOT_ENOUGH', errMsg);
          }
          default: {
            throw new ExError('TMT_CURL_API_FAIL', errMsg);
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      ctx.service.morBg.tradePoolTransferErrWarn('', JSON.stringify(dataFrom));
      const errMsg = `${this.logPrefix}tradePoolTransferRequestFail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    ctx.logger.info(`${this.logPrefix}poolDepositTransfer done.`);
    return rspData;
  }

  /**
   * 拼箱资金流转
   * @param sale_user_id
   * @param itemWithDrawOrderId
   */
  public async tradePoolTransferOfLcl(sale_user_id, itemWithDrawOrderId) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}tradePoolTransferOfLcl`, itemWithDrawOrderId);
    const itemWithDrawOrder = await ctx.service.item.itemWithdrawOrder.queryItemWithdrawOrder({
      _id: itemWithDrawOrderId,
    });
    const user = await ctx.service.user.user.queryUsersByRedis(sale_user_id);
    const userTradePoolName = 'bs_user_trade_balance';
    let userBalancePool = 'bs_user_balance';
    let balance_from = {
      balance_from: 'user_balance',
    };
    if (user.type === UserTypeEnum.Promoter) {
      // 主播收益收归主播交易担保池
      userBalancePool = 'bs_promoter_trade_balance';
    } else {
      if (itemWithDrawOrder.extends.payer === 3006 || itemWithDrawOrder.extends.payer === 4) {
        userBalancePool = 'bs_user_luckyitem_balance';
        balance_from = {
          balance_from: 'spdb',
        };
      }
    }

    const tradeTipName = 'bs_trade_tip';
    const userTradePoolNameConf = await ctx.service.watBg.getWalletConfByName(userTradePoolName);
    const userBalancePoolConf = await ctx.service.watBg.getWalletConfByName(userBalancePool);
    const tradeTipConf = await ctx.service.watBg.getWalletConfByName(tradeTipName);
    const userTradePoolId = userTradePoolNameConf.wallet_id;
    const userWalletPoolId = userBalancePoolConf.wallet_id;
    const tradeTipPoolId = tradeTipConf.wallet_id;
    const buy_user_id = itemWithDrawOrder.user_id;

    const amount = _.get(itemWithDrawOrder, 'pay_amount');
    const fee = _.get(itemWithDrawOrder, 'extends.tip_fee', 0);
    const sellGetAmount = _.floor(amount - fee);

    const extend = {
      from_user_id: String(buy_user_id),
      balance_from: balance_from.balance_from,
      itemWithDrawOrderId: String(itemWithDrawOrderId),
    };

    const buyToPlatform = {
      // 从买家担保池移到平台担保池
      pool_wallet_id: userTradePoolId,
      from_user_id: buy_user_id,
      to_wallet_id: userTradePoolId,
      amount,
      extends: extend,
    };
    const tradeToTip = {
      // 从平台担保池移到平台手续费池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_wallet_id: tradeTipPoolId,
      amount: fee,
      extends: extend,
    };
    const platformToSell = {
      // 从平台担保池移到卖家担保池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_user_id: sale_user_id,
      amount: sellGetAmount,
      extends: extend,
    };
    const tradeToBalance = {
      // 从卖家担保池移到卖家余额池
      pool_wallet_id: userTradePoolId,
      from_user_id: sale_user_id,
      to_wallet_id: userWalletPoolId,
      amount: sellGetAmount,
      extends: extend,
      transfer_type: 211,
      payer: itemWithDrawOrder.extends.payer,
      yopOrderTransferMsg: {},
      is_send_bull: false,
    };
    if (itemWithDrawOrder.extends.payer === 4) {
      tradeToBalance.yopOrderTransferMsg = {
        itemWithDrawOrderId,
        buyUserId: buy_user_id,
        sellUserId: sale_user_id,
        amount,
        fee,
      };
      tradeToBalance.is_send_bull = true;
    }

    let requestParams;
    if (fee > 0) {
      requestParams = { dataFrom : [ buyToPlatform, tradeToTip, platformToSell, tradeToBalance ] };
    } else {
      requestParams = { dataFrom : [ buyToPlatform, platformToSell, tradeToBalance ] };
    }
    await this.tradePoolTransferRequest(requestParams);
  }

  /**
   * 拼箱开奖失败组装参数
   * @param sale_user_id
   * @param itemWithDrawOrderId
   */
  public async failTradePoolTransferOfLcl(sale_user_id, itemWithDrawOrderId) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}tradePoolTransferOfLcl`, itemWithDrawOrderId);
    const itemWithDrawOrder = await ctx.service.item.itemWithdrawOrder.queryItemWithdrawOrder({
      _id: itemWithDrawOrderId,
    });
    const user = await ctx.service.user.user.queryUsersByRedis(sale_user_id);
    const userTradePoolName = 'bs_user_trade_balance';
    let userBalancePool = 'bs_user_balance';
    let balance_from = {
      balance_from: 'user_balance',
    };
    if (user.type === UserTypeEnum.Promoter) {
      // 主播收益收归主播交易担保池
      userBalancePool = 'bs_promoter_trade_balance';
    } else {
      if (itemWithDrawOrder.extends.payer === 3006 || itemWithDrawOrder.extends.payer === 4) {
        userBalancePool = 'bs_user_luckyitem_balance';
        balance_from = {
          balance_from: 'spdb',
        };
      }
    }

    const tradeTipName = 'bs_trade_tip';
    const userTradePoolNameConf = await ctx.service.watBg.getWalletConfByName(userTradePoolName);
    const userBalancePoolConf = await ctx.service.watBg.getWalletConfByName(userBalancePool);
    const tradeTipConf = await ctx.service.watBg.getWalletConfByName(tradeTipName);
    const userTradePoolId = userTradePoolNameConf.wallet_id;
    const userWalletPoolId = userBalancePoolConf.wallet_id;
    const tradeTipPoolId = tradeTipConf.wallet_id;
    const buy_user_id = itemWithDrawOrder.user_id;

    const amount = _.get(itemWithDrawOrder, 'pay_amount');
    const fee = _.get(itemWithDrawOrder, 'extends.tip_fee', 0);
    const sellGetAmount = _.floor(amount - fee);

    const extend = {
      from_user_id: String(buy_user_id),
      balance_from: balance_from.balance_from,
      itemWithDrawOrderId: String(itemWithDrawOrderId),
    };

    const buyToPlatform = {
      // 从买家担保池移到平台担保池
      pool_wallet_id: userTradePoolId,
      from_user_id: buy_user_id,
      to_wallet_id: userTradePoolId,
      amount,
      extends: extend,
    };
    const tradeToTip = {
      // 从平台担保池移到平台手续费池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_wallet_id: tradeTipPoolId,
      amount: fee,
      extends: extend,
    };
    const platformToSell = {
      // 从平台担保池移到卖家担保池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_user_id: sale_user_id,
      amount: sellGetAmount,
      extends: extend,
    };
    const tradeToBalance = {
      // 从卖家担保池移到卖家余额池
      pool_wallet_id: userTradePoolId,
      from_user_id: sale_user_id,
      to_wallet_id: userWalletPoolId,
      amount: sellGetAmount,
      extends: extend,
      transfer_type: 211,
      payer: itemWithDrawOrder.extends.payer,
      yopOrderTransferMsg: {},
      is_send_bull: false,
    };
    if (itemWithDrawOrder.extends.payer === 4) {
      tradeToBalance.yopOrderTransferMsg = {
        itemWithDrawOrderId,
        buyUserId: buy_user_id,
        sellUserId: sale_user_id,
        amount,
        fee,
      };
      tradeToBalance.is_send_bull = true;
    }

    let requestParams;
    if (fee > 0) {
      requestParams = { dataFrom : [ buyToPlatform, tradeToTip, platformToSell, tradeToBalance ] };
    } else {
      requestParams = { dataFrom : [ buyToPlatform, platformToSell, tradeToBalance ] };
    }
    return requestParams;
  }

  /**
   * 神策上报用户钱包余额变动
   * @param userId
   */
  public async walletBalanceChange(userId) {
    const { ctx } = this;
    ctx.service.morBg.sendEventDataToShence(
      eventNameEnum.WalletBalanceChange,
      userId.toString(),
      {
      },
      ctx.get('sensors_anonymous_id'),
    );
  }

  /**
   * check_micro_state
   * @param userId userId
   */
  public async check_micro_state(userId: string): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}check_micro_state begin.`, {
      userId,
    });

    const microService: Record<string, any> = ctx.app.config.custom.microservices.watBg;
    const apiUrl = `${microService.baseUrl}/open/wallet/v1/check_micro_state?user_id=${userId}`;
    const curlOptions: any = {
      method: 'GET',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    return rspData;
  }

  /**
   * 用户购买一手物品付款
   * @param buyUserId 购买人用户id
   * @param orderAmount 订单金额
   * @param orderId 订单号
   * @param pwd 密码
   */
  public async payIssueItem(buyUserId: ObjectId, orderAmount: number, orderId: string, pwd: string) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}payIssueItem buyUserId: ${buyUserId}, orderAmount:${orderAmount}, orderId: ${orderId}, pwd: ${pwd}`);

    // 查询支付密码是否正确
    await ctx.service.watBg.checkUserWalletPayPwd(String(buyUserId), pwd);

    const userTradePoolName = 'bs_user_trade_balance';
    const userBalancePool = 'bs_user_luckyitem_balance';
    const userTradePoolNameConf = await ctx.service.watBg.getWalletConfByName(userTradePoolName);
    const userBalancePoolConf = await ctx.service.watBg.getWalletConfByName(userBalancePool);
    const userTradePoolId = userTradePoolNameConf.wallet_id;
    const userWalletPoolId = userBalancePoolConf.wallet_id;

    const userBalance = await ctx.service.watBg.getUserBalanceAndPoolBalance(buyUserId, [ userWalletPoolId ]);
    const theUserYopBalance = _.find(userBalance.poolWallets, pool => {
      return String(_.get(pool, 'fund_wallet_id', '0')) === userWalletPoolId;
    });
    if (_.get(theUserYopBalance, 'amount') < orderAmount) {
      throw new ExError(80201210);
    }

    try {
      // 用户余额池资金转移至担保池
      await ctx.service.watBg.transferByDeposit(userWalletPoolId, {
          from_user_id: String(buyUserId),
          to_wallet_id: userTradePoolId,
        }, orderAmount, 212, { balance_from: 'spdb' }, {
          item_withdraw_orderId: orderId,
          trade_type: TradeTypeEnum.UserToFinancial,
          err_seat: 1,
        },
      );
    } catch (err) {
      switch (_.get(err, 'code')) {
        case 80201211:
          throw new ExError(80201210);
        default: {
          ctx.logger.error(err);
          throw err;
        }
      }
    }

    const balance_from = {
      balance_from: 'spdb',
    };
    const buy_user_id = buyUserId;
    const sale_user_id = _.get(ctx.app.config, 'custom.wjs.proceeds_user_id', '66f92de8cea50f002ef1493f');

    const amount = orderAmount;
    const fee = 0;
    const sellGetAmount = _.floor(amount - fee);
    const issueItemOrder = orderId;
    const extend = {
      from_user_id: String(buy_user_id),
      balance_from: balance_from.balance_from,
      issueItemOrder,
    };
    ctx.logger.info(`${this.logPrefix}dataFromExtend`, extend);
    const buyToPlatform = {
      // 从买家担保池移到平台担保池
      pool_wallet_id: userTradePoolId,
      from_user_id: buy_user_id,
      to_wallet_id: userTradePoolId,
      amount,
      extends: extend,
    };
    const platformToSell = {
      // 从平台担保池移到卖家担保池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_user_id: sale_user_id,
      amount: sellGetAmount,
      extends: extend,
    };
    const tradeToBalance = {
      // 从卖家担保池移到卖家余额池
      pool_wallet_id: userTradePoolId,
      from_user_id: sale_user_id,
      to_wallet_id: userWalletPoolId,
      amount: sellGetAmount,
      extends: extend,
      transfer_type: 211,
      // 4是商户余额支付
      payer: 4,
      yopOrderTransferMsg: {},
      is_send_bull: false,
    };

    const requestParams = { dataFrom : [ buyToPlatform, platformToSell, tradeToBalance ] };
    await this.tradePoolTransferRequest(requestParams);
  }

  /**
   * 用户购买一手物品付款(浦发)
   * @param buyUserId 购买人用户id
   * @param orderAmount 订单金额
   * @param orderId 订单号
   * @param pwd 密码
   */
  public async spdbPayIssueItem(buyUserId: ObjectId, orderAmount: number, orderId: string, pwd: string) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}payIssueItem buyUserId: ${buyUserId}, orderAmount:${orderAmount}, orderId: ${orderId}, pwd: ${pwd}`);

    // 查询支付密码是否正确
    await ctx.service.watBg.checkUserWalletPayPwd(String(buyUserId), pwd);
    const userBalancePool = 'bs_user_luckyitem_balance';
    const userBalancePoolConf = await ctx.service.watBg.getWalletConfByName(userBalancePool);
    const userWalletPoolId = userBalancePoolConf.wallet_id;

    const userBalance = await ctx.service.watBg.getUserBalanceAndPoolBalance(buyUserId, [ userWalletPoolId ]);
    const theUserYopBalance = _.find(userBalance.poolWallets, pool => {
      return String(_.get(pool, 'fund_wallet_id', '0')) === userWalletPoolId;
    });
    if (_.get(theUserYopBalance, 'amount') < orderAmount) {
      throw new ExError(80201210);
    }

    const extend = {
      from_user_id: String(buyUserId),
      balance_from: 'spdb',
      issueItemOrderId: orderId,
    };
    const requestParams = {
      buy_user_id: String(buyUserId),
      amount: orderAmount,
      extends: extend,
    };
    await this.spdbB2CBalancePay(requestParams);
  }

  /**
   * 用户购买二手物品付款(浦发)
   * @param buyUserId 购买人用户id
   * @param saleUserId 出售人用户id
   * @param orderAmount 订单金额
   * @param fee 手续费
   * @param orderId 订单号
   */
  public async spdbPayUsedItem(buyUserId: ObjectId, saleUserId: ObjectId, orderAmount: number, fee: number, orderId: string) {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}payIssueItem buyUserId: ${buyUserId}, saleUserId: ${saleUserId}, orderAmount:${orderAmount}, orderId: ${orderId}`);

    const userTradePoolName = 'bs_user_trade_balance';
    const userBalancePool = 'bs_user_luckyitem_balance';
    const tradeTipName = 'bs_trade_tip';
    const userTradePoolNameConf = await ctx.service.watBg.getWalletConfByName(userTradePoolName);
    const userBalancePoolConf = await ctx.service.watBg.getWalletConfByName(userBalancePool);
    const tradeTipConf = await ctx.service.watBg.getWalletConfByName(tradeTipName);
    const userTradePoolId = userTradePoolNameConf.wallet_id;
    const userWalletPoolId = userBalancePoolConf.wallet_id;
    const tradeTipPoolId = tradeTipConf.wallet_id;

    const buy_user_id = String(buyUserId);
    const sale_user_id = String(saleUserId);
    const extend = {
      from_user_id: String(buyUserId),
      balance_from: 'spdb',
      itemWithDrawOrderId: orderId,
    };
    const sellGetAmount = _.floor(orderAmount - fee);
    const buyToPlatform = {
      // 从买家担保池移到平台担保池
      pool_wallet_id: userTradePoolId,
      from_user_id: buy_user_id,
      to_wallet_id: userTradePoolId,
      amount: orderAmount,
      extends: extend,
    };
    const tradeToTip = {
      // 从平台担保池移到平台手续费池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_wallet_id: tradeTipPoolId,
      amount: fee,
      extends: extend,
    };
    const platformToSell = {
      // 从平台担保池移到卖家担保池
      pool_wallet_id: userTradePoolId,
      from_wallet_id: userTradePoolId,
      to_user_id: sale_user_id,
      amount: sellGetAmount,
      extends: extend,
    };
    const tradeToBalance = {
      // 从卖家担保池移到卖家余额池
      pool_wallet_id: userTradePoolId,
      from_user_id: sale_user_id,
      to_wallet_id: userWalletPoolId,
      amount: sellGetAmount,
      extends: extend,
      transfer_type: 211,
      // 4是商户余额支付
      payer: 4,
    };

    const dataFrom = [ buyToPlatform, tradeToTip, platformToSell, tradeToBalance ];
    const requestParams = {
      buy_user_id: String(buyUserId),
      sale_user_id: String(saleUserId),
      amount: orderAmount,
      fee,
      extends: extend,
      dataFrom,
    };
    await this.spdbC2CBalancePay(requestParams);
  }

  /**
   * spdbB2CBalancePay
   * @param dataFrom dataFrom
   */
  public async spdbB2CBalancePay(dataFrom: any): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}spdbB2CBalancePay begin.`, dataFrom);
    const microService: Record<string, any> = this.getMicroServiceConf();
    const apiUrl = `${microService.baseUrl}/open/wallet/v1/spdb_b2c_balance_pay`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: dataFrom,
      dataType: 'json',
      timeout: 30000,
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        ctx.service.morBg.spdbB2CBalancePayErrWarn(dataFrom?.extends?.issueItemOrderId, JSON.stringify(dataFrom));
        const errMsg = `${this.logPrefix}spdbB2CBalancePay Fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        throw new ExError(resData.code, resData.desc, resData.data);
      } else {
        rspData = resData.data;
      }
    } else {
      ctx.service.morBg.spdbB2CBalancePayErrWarn(dataFrom?.extends?.issueItemOrderId, JSON.stringify(dataFrom));
      const errMsg = `${this.logPrefix}spdbB2CBalancePay Fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    ctx.logger.info(`${this.logPrefix}spdbB2CBalancePay done.`);
    return rspData;
  }

  /**
   * spdbC2CBalancePay
   * @param dataFrom dataFrom
   */
  public async spdbC2CBalancePay(dataFrom: any): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix}spdbC2CBalancePay begin.`, dataFrom);
    const microService: Record<string, any> = this.getMicroServiceConf();
    const apiUrl = `${microService.baseUrl}/open/wallet/v1/spdb_c2c_balance_pay`;
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: dataFrom,
      dataType: 'json',
    };

    // 发送请求
    let rspData;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        if (resData.code === 80201218) {
          // ctx.service.morBg.spdbC2CBalancePayErrWarn(dataFrom?.extends?.itemWithDrawOrderId, JSON.stringify(dataFrom), true);
        } else {
          ctx.service.morBg.spdbC2CBalancePayErrWarn(dataFrom?.extends?.itemWithDrawOrderId, JSON.stringify(dataFrom));
        }
        const errMsg = `${this.logPrefix}spdbC2CBalancePay Fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError(resData.code, errMsg);
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      ctx.service.morBg.spdbC2CBalancePayErrWarn(dataFrom?.extends?.itemWithDrawOrderId, JSON.stringify(dataFrom));
      const errMsg = `${this.logPrefix}spdbC2CBalancePay Fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }
    ctx.logger.info(`${this.logPrefix}spdbC2CBalancePay done.`);
    return rspData;
  }

  /**
   * 获取当日充值&提现金额
   */
  public async getRechargeAndDrawPayAmount(start: string,
                                           end: string): Promise<{ rechargeAmount: number;rechargeUserCount:number; drawAmount: number;drawUserCount:number }> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} getRechargeAndDrawPayAmount begin `);
    const microService: Record<string, any> = this.getMicroServiceConf();

    const apiUrl = `${microService.baseUrl}/open/wallet/adminPanel/v1/getRechargeAndDrawPayAmount`;
    ctx.logger.info(`microService apiUrl `,`${apiUrl}`);
    ctx.logger.info(`microService secret `,`Bearer ${microService.secret}`);


    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data:{
        start,
        end,
      },
      dataType: 'json',
    };

    ctx.logger.info(`${this.logPrefix} secret ${microService.secret} `);
    // 发送请求
    let rspData: { rechargeAmount: number;rechargeUserCount:number; drawAmount: number;drawUserCount:number };
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError('TMT_CURL_API_FAIL', errMsg);
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    ctx.logger.info(`${this.logPrefix}getRechargeAndDrawPayAmount done.`);
    return {
      rechargeAmount: rspData.rechargeAmount,
      rechargeUserCount: rspData.rechargeUserCount,
      drawAmount: rspData.drawAmount,
      drawUserCount: rspData.drawUserCount,
    };
  }

  /**
   * 获取用户资金总和
   */
  public async getWalletAllAmount(): Promise<Record<string, any>> {
    const { ctx } = this;
    ctx.logger.info(`${this.logPrefix} getWalletAllAmount begin---------------------/ `);
    const microService: Record<string, any> = this.getMicroServiceConf();

    const apiUrl = `${microService.baseUrl}/open/v1/statistics/getWalletAllAmount`;
    ctx.logger.info(`microService apiUrl `,`${apiUrl}`);
    ctx.logger.info(`microService secret `,`Bearer ${microService.secret}`);


    const curlOptions: any = {
      method: 'GET',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      dataType: 'json',
    };

    // 发送请求
    let rspData:any;
    const res = await ctx.curl(apiUrl, curlOptions);
    if (res.status === 200 && res.data) {
      const resData = res.data;

      if (resData.code !== 0) {
        const errMsg = `${this.logPrefix}curl fail.(${resData.code})[${resData.desc}]`;
        ctx.logger.error(errMsg, res.data);
        switch (resData.code) {
          default: {
            throw new ExError('TMT_CURL_API_FAIL', errMsg);
          }
        }
      } else {
        rspData = resData.data;
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    ctx.logger.info(`${this.logPrefix}getWalletAllAmount done.`);
    return rspData;
  }

  /**
   * 校验出售密码是否正确
   */
  public async verifySellPwd({ user_id, sell_pwd }: {user_id: string; sell_pwd: string}) {
    const { ctx } = this;
    const microService: Record<string, any> = this.getMicroServiceConf();
    const curlOptions: any = {
      method: 'POST',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      data: {
        user_id,
        sell_pwd,
      },
      dataType: 'json',
    };
    const apiUrl = `${microService.baseUrl}/open/wallet/v1/verify_sell_pwd`;
    const res = await ctx.curl(apiUrl, curlOptions);

    if (res.status === 200 && res.data) {
      const resData = res.data;
      if (resData.code !== 0) {
        // 出售密码错误
        throw new ExError(resData.code, resData.desc);
      }
    } else {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    return true;
  }

  /**
   * 获取出售密码相关信息
   */
  public async checkSellPwdInfo(user_id: string) {
    const { ctx } = this;
    const microService: Record<string, any> = this.getMicroServiceConf();
    const curlOptions: any = {
      method: 'GET',
      contentType: 'json',
      headers: {
        Authorization: `Bearer ${microService.secret}`,
      },
      dataType: 'json',
    };
    const apiUrl = `${microService.baseUrl}/open/wallet/v1/check_sell_pwd_info?user_id=${user_id}`;
    const res = await ctx.curl(apiUrl, curlOptions);

    if (res.status !== 200 || !res.data || res.data.code !== 0) {
      const errMsg = `${this.logPrefix}curl fail.(${res.status})[${JSON.stringify(res.data)}]`;
      ctx.logger.error(errMsg, res.data);
      throw new ExError('TMT_CURL_API_FAIL', errMsg);
    }

    return res.data.data;
  }
}
