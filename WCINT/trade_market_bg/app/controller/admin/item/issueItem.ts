import {Controller} from 'egg';
import IssueItemValidator from 'validator/admin/item/issue_item';
import {
  filingQuerysByMongo,
  filingSortsByMongo, objToDotKeys,
  SearchField,
  SearchTypeEnum,
  urlParse,
  UrlParseSearchOp,
} from 'utils/repository';
import {
  CirculationStatusEnum,
  IssueDateEnum,
  IssueItemAuditStatusEnum, IssueItemPriorityBuyStatusEnum,
  IssueItemStatusEnum,
} from 'enum/item/issue_item';
import ExError from 'utils/ex-error/ex_error';
import * as _ from 'lodash';
import { UpChainTypeEnum } from 'enum/user_item/up_chain_order';
import {ItemSaleStatusEnum} from 'enum/item/item_sale_status';
import {UserItemStatusEnum} from 'enum/user_item/user_item_status';
import {StatusEnum} from 'enum/status';
import * as xlsx from 'xlsx';
import { ObjectId } from 'bson';
import {addQueueMessage} from 'utils/queue';
import {getRedisLock} from 'utils/redis_helper';
import moment = require('moment');
import {SaleOrderTypeEnum} from 'enum/item/sale_order_type';
import { SteamItemsExtendTypeEnum } from "enum/user_item/steam_items_extend_type";
import { Message, TopicMessages } from 'kafkajs';

export default class IssueItemController extends Controller {
  public logPrefix = '[controller.admin.issue_item]';
  private validator: IssueItemValidator;

  constructor(ctx) {
    super(ctx);
    this.validator = new IssueItemValidator(ctx);
  }

  public async list() {
    const { ctx } = this;
    await this.validator.list();

    const allowSearchFields: SearchField[] = [
      {
        key: 'created_at',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: 'submit_time',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: 'review_time',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: 'sku_no',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'item_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'item_name',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.LIKE,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: '_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'ip_classify_ids',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'issuer_name',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.LIKE,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'sale_mode',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'audit_status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'admin_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'circulation_status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'review_admin_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },

    ];
    const allowSortFields = [
      '_id', 'level',
    ];

    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    const query = filingQuerysByMongo({}, queryParse);

    const sort = filingSortsByMongo({ created_at: -1 }, queryParse);

    let list: any[] = [];
    const total = await ctx.service.item.issueItem.countIssueItem(query);
    if (total > 0) {
      list = await ctx.service.item.issueItem.queryIssueItems(query, [], {
        page: ctx.state.queryOptions.page,
        limit: ctx.state.queryOptions.limit,
        sort,
      });
    }
    if (!_.isEmpty(list)) {
      const adminIds: any[] = [];
      const itemIds: any[] = [];
      for (const item of list) {
        adminIds.push(item.admin_id);
        itemIds.push(item.item_id);
        if (item.review_admin_id) {
          adminIds.push(item.review_admin_id);
        }
      }

      const adminList = await ctx.service.admin.admin.queryAdmins({ _id: { $in: adminIds } });
      //最新成交价
      const lastSaleOrders = await ctx.service.item.saleOrder.query(
        {
          item_id: { $in: itemIds },
          order_status: ItemSaleStatusEnum.sellOut
        },
        [ 'order_amount' ,'item_id'],
        { sort: { order_tiem: -1 } },
      );
      let lastSaleOrdersGroup = _.groupBy(lastSaleOrders, 'item_id');


      //获取发行价
      const issueItems = await ctx.service.item.issueItem.queryIssueItem({ item_id: {$in:itemIds} }, [ [ 'item_id', 'price' ] ]);
      let issueItemsGroup = _.groupBy(issueItems, 'item_id');

      // 获取上一个交易日的收盘价
      const dateStr = ctx.service.statistics.getDateStr(new Date(), 'YYYY-MM-DD');
      const lastDailyQuotations = await ctx.service.item.dailyQuotation.queryDailyQuotations({
          item_id: {$in: itemIds }, date: {$lte: dateStr}}, [ 'close_price','item_id' ],
        {sort: {date: -1},group:{_id:"item_id"}});
      let lastDailyQuotationsGroup = _.groupBy(lastDailyQuotations, 'item_id');

      // 获取商品id的持仓数
      const userItemCountMap = await ctx.service.ycOpm.loadUserItemCountsFromCacheOrRemote(itemIds);
      // let userItemCounts = [{item_id:111,count:111}];
      let userItemCountsIgnoreFusion = await ctx.service.ycOpm.countUserItemByItemIdsGroupByUserId(itemIds ,  [ UserItemStatusEnum.Owned, UserItemStatusEnum.Saleing ]);
      let userItemCountsIgnoreFusionGroup = _.groupBy(userItemCountsIgnoreFusion, 'item_id');


      for (const item of list) {
        const admin = adminList.find(a => String(a._id) === String(item.admin_id));
        if (admin) {
          _.set(item, '_doc.admin_name', admin.name);
        }
        if (item.review_admin_id) {
          const reviewAdmin = adminList.find(a => String(a._id) === String(item.review_admin_id));
          if (reviewAdmin) {
            _.set(item, '_doc.review_admin_name', reviewAdmin.name);
          }
        }
        if (item.status==IssueItemStatusEnum.INITIAL){
          _.set(item, '_doc.lastPayAmount', 0);
          _.set(item, '_doc.holderQuantity', 0);
          _.set(item, '_doc.marketAmount', 0);
          _.set(item, '_doc.holdings_user_count', 0);
        }else {
          // 添加 latestPayAmount
          let lastPayAmount = _.get(_.first(lastSaleOrdersGroup[item.item_id]), 'order_amount', 0) || _.get(_.first(issueItemsGroup[item.item_id]), 'price', 0);
          _.set(item, '_doc.lastPayAmount', lastPayAmount);
          // 流通数量 = 该商品首发剩余库存(首发已结束不计算)+该商品当前总用户持仓数量(排除已提货、已融合的，持仓数量每小时计算更新一次)
          const userItemCount: any = userItemCountMap.get(String(item.item_id)) ?? { count: 0 };
          let totalCirculation = userItemCount.count ?? 0; // 获取流通数量，默认为 0
          const sale_end = item.sale_end ? moment(item.sale_end) : null;
          if (!sale_end || sale_end.isAfter(moment())) {
            totalCirculation = totalCirculation + item.quantity - item.sales_volume;
          }
          _.set(item, '_doc.holderQuantity', totalCirculation);
          //添加市值
          let close_price = _.get(_.first(lastDailyQuotationsGroup[item.item_id]), 'close_price', 0);
          let marketAmount = close_price * totalCirculation;
          _.set(item, '_doc.marketAmount', marketAmount);
          // 添加持仓用户数
          const holdingsUserCount = _.get(_.first(userItemCountsIgnoreFusionGroup[item.item_id]), 'user_count', 0);
          _.set(item, '_doc.holdings_user_count', holdingsUserCount);
        }
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: {
        list,
        total,
      },
    };
  }

  public async simpleList() {
    const { ctx } = this;
    await this.validator.list();

    const allowSearchFields: SearchField[] = [
      {
        key: 'created_at',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: 'submit_time',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: 'review_time',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: 'sku_no',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'item_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'item_name',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.LIKE,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: '_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'ip_classify_ids',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'issuer_name',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.LIKE,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'sale_mode',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'audit_status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'admin_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'circulation_status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'review_admin_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'priority_buy.status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'synthesis.status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
    ];
    const allowSortFields = [
      '_id', 'level',
    ];

    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    const query = filingQuerysByMongo({}, queryParse);

    const sort = filingSortsByMongo({ created_at: -1 }, queryParse);

    let list: any[] = [];
    const total = await ctx.service.item.issueItem.countIssueItem(query);
    if (total > 0) {
      list = await ctx.service.item.issueItem.queryIssueItems(query,
        [ '_id', 'item_id', 'status', 'audit_status', 'image_url', 'item_name', 'quantity', 'price', 'issue_date', 'issue_time', 'priority_buy', 'sale_stock_limit', 'synthesis' ],
        {
        page: ctx.state.queryOptions.page,
        limit: ctx.state.queryOptions.limit,
        sort,
      });
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: {
        list,
        total,
      },
    };
  }

  //新增IP发行
  public async create() {
    const { ctx } = this;
    await this.validator.create();
    const form = ctx.state.form;
    form.admin_id = ctx.state.admin._id;
    await ctx.service.item.issueItem.createIssueItem(form);
    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  public async update() {
    const { ctx } = this;
    await this.validator.update();
    const { id, form } = ctx.state;
    const result = await ctx.service.item.issueItem.updateIssueItem({
      _id: id,
      audit_status: {
        $in: [ IssueItemAuditStatusEnum.ToBeSubmitted, IssueItemAuditStatusEnum.Recall, IssueItemAuditStatusEnum.ReviewFailed ],
      },
    }, {
      $set: form,
    });
    if (!result) {
      throw new ExError('TMT_DATA_STATUS_FORBID');
    }
    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  public async submit() {
    const { ctx } = this;
    await this.validator.submit();
    const id = ctx.state.id;
    const result = await ctx.service.item.issueItem.updateIssueItem({
      _id: id,
      audit_status: {
        $in: [ IssueItemAuditStatusEnum.ToBeSubmitted, IssueItemAuditStatusEnum.Recall, IssueItemAuditStatusEnum.ReviewFailed ],
      },
    }, {
      $set: {
        audit_status: IssueItemAuditStatusEnum.ToBeReviewed,
        submit_time: new Date(),
      },
      $push: {
        operation_logs: await this.getLogForm('提审'),
      },
    });
    if (!result) {
      throw new ExError('TMT_DATA_STATUS_FORBID');
    }
    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  private async getLogForm(content) {
    const { ctx } = this;
    return {
      time: new Date(),
      admin_id: ctx.state.admin._id,
      admin_name: ctx.state.admin.name,
      content,
    };
  }

  public async recall() {
    const { ctx } = this;
    await this.validator.recall();
    const id = ctx.state.id;
    const result = await ctx.service.item.issueItem.updateIssueItem({
      _id: id,
      audit_status: IssueItemAuditStatusEnum.ToBeReviewed,
    }, {
      $set: {
        audit_status: IssueItemAuditStatusEnum.Recall,
      },
      $push: {
        operation_logs: await this.getLogForm('已撤回'),
      },
    });
    if (!result) {
      throw new ExError('TMT_DATA_STATUS_FORBID');
    }
    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  public async review() {
    const { ctx } = this;
    await this.validator.review();
    const { form, issueItem } = ctx.state;
    const pass = IssueItemAuditStatusEnum.ReviewPass === form.audit_status;
    const currTime = new Date();
    const updateForm: any = {
      audit_status: form.audit_status,
      review_admin_id: ctx.state.admin._id,
      review_time: currTime,
    };
    if (pass && IssueDateEnum.Approved === issueItem.issue_date) {
      updateForm.issue_time = currTime;
    }
    const result = await ctx.service.item.issueItem.updateIssueItem({
      _id: form.id,
      audit_status: IssueItemAuditStatusEnum.ToBeReviewed,
    }, {
      $set: updateForm,
      $push: {
        operation_logs: await this.getLogForm(pass ? '审核通过' : '审核不通过'),
      },
    });
    if (!result) {
      throw new ExError('TMT_DATA_STATUS_FORBID');
    }
    if (pass) {
      await this.reviewUpChain(issueItem);
    }
    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  private async reviewUpChain(issueItem) {
    const { ctx } = this;
    // 审核通过上链存在溯源
    const data: any = {
      itemId: issueItem.item_id,
      skuNo: issueItem.sku_no,
      itemName: issueItem.item_name,
      itemType: issueItem.item_type,
      itemCategory: issueItem.item_category,
      ipClassifyNames: issueItem.ip_classify_names,
      itemSpecs: issueItem.item_specs,
      issuerName: issueItem.issuer_name,
      issuerShortName: issueItem.issuer_short_name,
      copyrightName: issueItem.copyright_name,
      copyrightNo: issueItem.copyright_no,
      quantity: issueItem.quantity,
      price: issueItem.price,
      issueTime: issueItem.issue_time,
      saleMode: issueItem.sale_mode,
      deliveryTime: issueItem.delivery_time || '',
      imageUrl: issueItem.image_url,
      imageInfos: issueItem.image_infos || [],
      detail: issueItem.detail,
      adminId: issueItem.admin_id,
      submitTime: issueItem.submit_time,
      registerAgency: '安徽省文化产权交易所',
      reviewAdminId: ctx.state.admin._id,
      reviewTime: new Date(),
    };
    if (!data.issue_time) {
      data.issueTime = new Date();
    }
    const chain = await ctx.service.chainBg.upChain(data, UpChainTypeEnum.Issue);
    await ctx.service.item.issueItem.updateIssueItem({
      _id: issueItem._id,
    }, {
      $set: {
        chain_hash: chain.hash,
        chain_dataId:chain.dataId
      },
    });
  }

  public async shelves() {
    const { ctx } = this;
    await this.validator.shelves();
    const issueItem = ctx.state.issueItem;
    let updateForm;
    let content;
    if (IssueItemStatusEnum.OFF === issueItem.status || IssueItemStatusEnum.INITIAL === issueItem.status) {
      updateForm = {
        status: IssueItemStatusEnum.ON,
        shelf_time: new Date(),
      };
      if (IssueDateEnum.Approved === issueItem.issue_date && !issueItem.issue_time) {
        updateForm.issue_time = new Date();
      }
      content = '首发上架';
    } else {
      updateForm = {
        status: IssueItemStatusEnum.OFF,
      };
      content = '首发下架';
    }
    await ctx.service.item.issueItem.updateIssueItem({
      _id: issueItem._id,
    }, {
      $set: updateForm,
      $push: {
        operation_logs: await this.getLogForm(content),
      },
    });
    let saleOrderInit: any = {
      item_id: issueItem.item_id,
      // 审核状态
      examine_status: StatusEnum.Enable,
      // 订单状态
      order_status: ItemSaleStatusEnum.sell,
      // 出售渠道来源（同样根据StatusEnum定义）
      sale_from: StatusEnum.Enable,
      // 流转次数
      sale_count: 0,
      // 完成订单时间，这里设置为当前时间
      order_tiem: new Date(ctx.service.statistics.getDateStr(moment(), 'YYYY-MM-DD HH:mm:ss')),
      // 平台手续费
      tip_fee: 0,
      // 交易金额
      order_amount: 0,
      // 订单类型（根据SaleOrderTypeEnum定义）
      order_type: SaleOrderTypeEnum.UserItem,

      };
      // 同步match表
      await ctx.service.match.addMatch(saleOrderInit,true);


    // 发送Kafka
    try {
      const messages: Message[] = [];
      const info = {
        item_id: issueItem.item_id,
        status: updateForm.status,
      };
      messages.push({
        value: JSON.stringify(info),
      });
      const batchMsg: TopicMessages[] = [{
        topic: 'issue_item_shelves',
        messages,
      }];
      await this.app.producer.sendBatch(batchMsg);
    } catch (err) {
      ctx.logger.error(`${this.logPrefix} send kafka message to topic issue_item_shelves fail.`, err);
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  public async circulation_control() {
    const { ctx } = this;

    if (typeof this.validator.circulation_control === 'function') {
      await this.validator.circulation_control();
    }

    const { form, issueItem } = ctx.state;
    let operationLog: any;

    const isUnlimitedToProhibit = issueItem.circulation_status === CirculationStatusEnum.Unlimited && form.circulation_status === CirculationStatusEnum.Prohibit;
    const isProhibitToUnlimited = issueItem.circulation_status === CirculationStatusEnum.Prohibit && form.circulation_status === CirculationStatusEnum.Unlimited;

    if (isUnlimitedToProhibit) {
      operationLog = await this.getLogForm('禁止流通');
    } else if (isProhibitToUnlimited) {
      operationLog = await this.getLogForm('不限流通');
    }

    const updateForm: any = {
      $set: _.omit(form, [ 'id' ]),
    };

    if (operationLog) {
      updateForm.$push = {
        operation_logs: operationLog,
      };
    }

    await ctx.service.item.issueItem.updateIssueItem({
      _id: issueItem._id,
    }, updateForm);

    if (isUnlimitedToProhibit) {
      await ctx.service.item.saleOrder.batchOffSaleOrder([ issueItem.item_id ]);
    }

    // 调用steam_items_extend upsert接口
    ctx.service.ycOpm.upsertSteamItemsExtend(
      issueItem.item_id,
      SteamItemsExtendTypeEnum.FirstRelease,
      issueItem.sku_no,
      issueItem.item_name,
      form.circulation_status === CirculationStatusEnum.Unlimited
    ).catch((error) => {
      ctx.logger.error(`${this.logPrefix} upsertSteamItemsExtend failed for item ${issueItem.item_id}:`, error);
      ctx.service.morBg.steamItemsExtendUpdateErrWarn(issueItem.item_id, issueItem.sku_no, issueItem.item_name, error.message);
    });


    // 发送Kafka
    try {
      const messages: Message[] = [];
      const info = {
        item_id: issueItem.item_id,
        circulation_status: form.circulation_status,
        circulation_start: form.circulation_start,
        circulation_end: form.circulation_end,
        circulation_end_show_type: form.circulation_end_show_type,
      };
      messages.push({
        value: JSON.stringify(info),
      });
      const batchMsg: TopicMessages[] = [{
        topic: 'circulation_control',
        messages,
      }];
      await this.app.producer.sendBatch(batchMsg);
    } catch (err) {
      ctx.logger.error(`${this.logPrefix} send kafka message to topic circulation_control fail.`, err);
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  /**
   * 首发管理
   */
  public async issueItemManage() {
    const { ctx, app } = this;
    // 加锁防并发
    const prefix = ctx.app.config.custom.redis.prefix;
    const id = _.get(ctx.request.body, 'id');
    const lockKey = `${prefix}:update_issue_item:${id}`;
    const lock: any = await getRedisLock(app, lockKey, 1000 * 30);
    if (!lock) {
      throw new ExError('TMT_CURRENCY_ERROR', '正在处理中，请稍后重试', { message: '正在处理中，请稍后重试' });
    }
    try {
      if (typeof this.validator.issueItemManage === 'function') {
        await this.validator.issueItemManage();
      }
      let form = ctx.state.form;
      form = _.omit(form, [ 'priority_buy.used_stock' ]);
      const issueItem = ctx.state.issueItem;
      const script = `
      local stockKey = KEYS[1]
      local priorityBuyStockKey = KEYS[2]
      local saleQuantityKey = KEYS[3]
      local priorityBuySaleQuantityKey = KEYS[4]
      local newStock = tonumber(ARGV[1])
      local newPriorityBuyStock = tonumber(ARGV[2])
      local saleQuantity = redis.call("GET", saleQuantityKey) or 0
      local priorityBuySaleQuantity = redis.call("GET", priorityBuySaleQuantityKey) or 0
      if tonumber(newStock) < tonumber(saleQuantity) then
        return 0
      end
      if tonumber(newPriorityBuyStock) < tonumber(priorityBuySaleQuantity) then
        return 0
      end
      redis.call("SET", stockKey, newStock)
      redis.call("SET", priorityBuyStockKey, newPriorityBuyStock)
    `;
      // 公售库存
      const saleLimitStock = form.sale_stock_limit.limit_type === 1 ? form.sale_stock_limit?.stock : issueItem.quantity;
      const priorityBuyStock = form.priority_buy.status === IssueItemPriorityBuyStatusEnum.Open ? form.priority_buy?.stock : saleLimitStock;
      const result = await app.redis.eval(script, 4, [
        `${prefix}:issue_item_stock:${String(issueItem._id)}`,
        `${prefix}:issue_item_priority_buy_stock:${String(issueItem._id)}`,
        `${prefix}:issue_item_sale_quantity:${String(issueItem._id)}`,
        `${prefix}:issue_item_priority_buy_sale_quantity:${String(issueItem._id)}`,
        saleLimitStock,
        priorityBuyStock,
      ]);
      if (result === 0) {
        throw new ExError('TMT_CURRENCY_ERROR', '已出售数量大于新设置库存', { message: '已出售数量大于新设置库存' });
      }

      const updateForm: Record<string, any> = {};
      objToDotKeys(updateForm, form, '');
      const unsetForm: Record<string, any> = {};
      if (form.sale_stock_limit.limit_type === 0) {
        unsetForm['sale_stock_limit.stock'] = null;
      }
      if (form.priority_buy.status === IssueItemPriorityBuyStatusEnum.Close) {
        unsetForm['priority_buy.stock'] = null;
        unsetForm['priority_buy.advance_minutes'] = null;
      }
      await ctx.service.item.issueItem.updateIssueItem({
        _id: new ObjectId(issueItem._id),
      }, {
        $set: updateForm,
        $unset: unsetForm,
      });
    } catch (error) {
      ctx.app.logger.error(`issueItemManage fail: ${error.message}`, error);
      throw error;
    } finally {
      // 设置2秒防止并发
      await app.redis.expire(lockKey, 2);
    }
    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  /**
   * 首发限购
   */
  public async sale_control() {
    const { ctx } = this;

    if (typeof this.validator.sale_control === 'function') {
      await this.validator.sale_control();
    }

    const { form, issueItem } = ctx.state;
    let operationLog: any;
    const updateForm: any = {
      $set: _.omit(form, [ 'id' ]),
    };
    if (operationLog) {
      updateForm.$push = {
        operation_logs: operationLog,
      };
    }
    await ctx.service.item.issueItem.updateIssueItem({
      _id: issueItem._id,
    }, updateForm);

    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  /**
   * 开启广告推送
   */
  public async is_open_ad() {
    const { ctx } = this;
    await this.validator.is_open_ad();
    const { form, issueItem } = ctx.state;

    await ctx.service.item.issueItem.updateIssueItem({
      _id: issueItem._id,
    }, {
      $set: {
        ad_ids: form.ad_ids,
      }
    });
    ctx.body = {
      code: 0,
      desc: '',
      data: {},
    };
  }

  /**
   * 获取第三方广告商id
   */
  public async get_ad_ids() {
    const { ctx } = this;
    let list = await ctx.service.item.advertiser.queryAdvertisers({status:StatusEnum.Enable},['name']);
    ctx.body = {
      code: 0,
      desc: '',
      data: {list},
    };
  }

  public async order_list() {
    const { ctx } = this;
    await this.validator.order_list();

    const allowSearchFields: SearchField[] = [
      {
        key: 'created_at',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: 'pay_time',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: '_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'item_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'sku_no',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'item_name',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.LIKE,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'issue_item.ip_classify_ids',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'user_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'order_type',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'priority_buy_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
    ];
    const allowSortFields = [
      '_id',
    ];

    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    const query = filingQuerysByMongo({}, queryParse);

    let sort = filingSortsByMongo({ created_at: -1 }, queryParse);

    let aggregate = [
      {
        $lookup: {
          from: 'issue_item',
          localField: 'issue_item_id',
          foreignField: '_id',
          as: 'issue_item',
        },
      },
      {
        $unwind: '$issue_item',
      },
      {
        $match: query,
      },
    ];

    let list: any[] = [];
    const total = await ctx.service.item.issueItemOrder.countAggregateIssueItemOrder(aggregate);
    if (total > 0) {
      const skip = { $skip: ctx.state.queryOptions.page*ctx.state.queryOptions.limit };
      const limit = { $limit: ctx.state.queryOptions.limit };
      sort = { $sort: sort };
      aggregate = aggregate.concat([ sort,skip, limit ]);
      list = await ctx.service.item.issueItemOrder.model.aggregate(aggregate).allowDiskUse(true);
      const userIds = list.map(item => item.user_id);
      const users = await ctx.service.user.user.queryUsers({ _id: { $in: userIds } });
      for (const item of list) {
        const user = users.find(user => String(user._id) === String(item.user_id));
        if (user) {
          item.nickname = _.get(user, 'patbg_detail.nickname', '');
        }
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: {
        list,
        total,
      },
    };
  }

  // 发行商品空投用户校验
  public async airdropUserValidate() {
    const { ctx } = this;
    if (typeof this.validator.airdropUserValidate === 'function') {
      await this.validator.airdropUserValidate();
    }
    const { issue_item_id: issueItemId } = ctx.state.form;

    // 判断发行商品是否存在
    const issueItem: any = await ctx.service.item.issueItem.queryIssueItem({ _id: issueItemId });
    if (!issueItem) {
      throw new ExError('TMT_DATA_NOT_EXIST', '发行商品不存在');
    }
    if (issueItem.audit_status !== IssueItemAuditStatusEnum.ReviewPass) {
      throw new ExError('TMT_DATA_STATUS_FORBID', '发行商品未通过审核');
    }
    const files = ctx.request.files;
    if (!files || !Array.isArray(files) || files.length === 0) {
      throw new ExError('TMT_UPLOAD_FILE_EMPTY', '文件未上传');
    }
    const file = files[0];

    const workbook = xlsx.readFile(file.filepath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data: any[] = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
    if (!data.length) {
      throw new ExError('TMT_AIRDROP_USER_LIMIT', '名单不能为空');
    }

    const errList: any[] = [];
    const userIds: any[] = [];
    let totalQuantity = 0;
    // 获取第一列的数据并判断是否是bsonID
    for (const item of data) {
      if (!Array.isArray(item) || item.length < 2) {
        errList.push({ row: item[0], msg: '列数不足' });
        continue;
      }
      const userIdStr = String(item[0]).trim();
      const quantityVal = Number(item[1]);
      if (!userIdStr || !ObjectId.isValid(userIdStr)) {
        errList.push({ row: userIdStr, msg: '无效ID' });
        continue;
      }
      if (!quantityVal || !Number.isInteger(quantityVal) || quantityVal < 1) {
        errList.push({ row: userIdStr, msg: '无效数量' });
        continue;
      }
      totalQuantity += quantityVal;
      userIds.push(userIdStr);
    }
    const uniqueUserIds = [ ...new Set(userIds) ].map(userId => new ObjectId(userId));
    if (userIds.length !== uniqueUserIds.length) {
      throw new ExError('TMT_AIRDROP_USER_ERROR', '名单中存在重复用户');
    }

    if (uniqueUserIds.length > 5000) {
      throw new ExError('TMT_AIRDROP_USER_ERROR', '名单数量超限，请核对无误后重新上传');
    }

    if (totalQuantity > 5000) {
      throw new ExError('TMT_AIRDROP_QUANTITY_LIMIT', '单次空投总数不得超过5000件');
    }

    if (totalQuantity > issueItem.quantity - issueItem.sales_volume) {
      throw new ExError('TMT_AIRDROP_STOCK_NOT_ENOUGH', '库存不足');
    }

    // 判断用户状态
    const users: any[] = [];
    for (let i = 0; i < uniqueUserIds.length; i += 1000) {
      const batchUserIds = uniqueUserIds.slice(i, i + 1000);
      const batchUserResult = await ctx.service.user.user.queryUsers({ _id: { $in: batchUserIds } }, [ '_id', 'status', 'patbg_detail', 'open_info' ]);
      users.push(...batchUserResult);
    }
    const userMap = new Map(users.map(user => [ user._id.toString(), user ]));
    for (const userId of uniqueUserIds) {
      const user = userMap.get(userId.toString());
      if (!user) {
        errList.push({ row: String(userId), msg: '不存在' });
      } else {
        if (user.status === StatusEnum.Disable) {
          errList.push({ row: String(userId), msg: '已禁用' });
          continue;
        }
        if (user.status === StatusEnum.Deleted) {
          errList.push({ row: String(userId), msg: '已注销' });
          continue;
        }
        if (!user.patbg_detail?.is_certified) {
          errList.push({ row: String(userId), msg: '未实名' });
          continue;
        }
        if (!user.open_info?.open_user_id) {
          errList.push({ row: String(userId), msg: '未绑定云仓' });
        }
      }
    }

    ctx.body = {
      code: 0,
      desc: 'success',
      data: errList,
    };
  }

  // 发行商品空投
  public async airdrop() {
    const { ctx } = this;

    if (typeof this.validator.airdrop === 'function') {
      await this.validator.airdrop();
    }
    const { issue_item_id: issueItemId, airdrop_limit: airdropLimit } = ctx.state.form;
    // 加锁防并发
    const prefix = ctx.app.config.custom.redis.prefix;
    const lockKey = `${prefix}:issue_item_airdrop:${issueItemId}`;
    const lock: any = await getRedisLock(ctx.app, lockKey, 1000 * 30);
    if (!lock) {
      throw new ExError('TMT_CURRENCY_ERROR', '操作频繁，请稍后重试', { message: '操作频繁，请稍后重试' });
    }
    try {
      // 判断发行商品是否存在
      const issueItem: any = await ctx.service.item.issueItem.queryIssueItem({ _id: issueItemId });
      if (!issueItem) {
        throw new ExError('TMT_DATA_NOT_EXIST', '发行商品不存在');
      }
      if (issueItem.audit_status !== IssueItemAuditStatusEnum.ReviewPass) {
        throw new ExError('TMT_DATA_STATUS_FORBID', '发行商品未通过审核');
      }
      const files = ctx.request.files;
      if (!files || !Array.isArray(files) || files.length === 0) {
        throw new ExError('TMT_UPLOAD_FILE_EMPTY', '文件未上传');
      }
      const file = files[0];

      const workbook = xlsx.readFile(file.filepath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data: any[] = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
      // 输出日志data
      ctx.logger.info(`${this.logPrefix} airdrop user data:${JSON.stringify(data)}`);

      if (!data.length) {
        throw new ExError('TMT_AIRDROP_USER_LIMIT', '名单不能为空');
      }

      const errList: any[] = [];
      const userIds: any[] = [];
      const airdropUsers: any[] = [];
      let totalQuantity = 0;
      // 获取第一列的数据并判断是否是bsonID
      for (const item of data) {
        if (!Array.isArray(item) || item.length < 2) {
          errList.push({ row: item[0], msg: '列数不足' });
          continue;
        }
        const userIdStr = String(item[0]).trim();
        const quantityVal = Number(item[1]);
        if (!userIdStr || !ObjectId.isValid(userIdStr)) {
          errList.push({ row: userIdStr, msg: '无效ID' });
          continue;
        }
        if (!quantityVal || !Number.isInteger(quantityVal) || quantityVal < 1) {
          errList.push({ row: userIdStr, msg: '无效数量' });
          continue;
        }
        if (quantityVal > airdropLimit) {
          errList.push({ row: userIdStr, msg: '不能大于重复空投限制' });
          continue;
        }
        totalQuantity += quantityVal;
        userIds.push(userIdStr);
        airdropUsers.push({
          user_id: new ObjectId(userIdStr),
          quantity: quantityVal,
        });
      }
      const uniqueUserIds = [ ...new Set(userIds) ].map(userId => new ObjectId(userId));
      if (userIds.length !== uniqueUserIds.length) {
        throw new ExError('TMT_AIRDROP_USER_ERROR', '名单中存在重复用户');
      }

      if (uniqueUserIds.length > 5000) {
        throw new ExError('TMT_AIRDROP_USER_ERROR', '名单数量超限，请核对无误后重新上传');
      }

      if (totalQuantity > 5000) {
        throw new ExError('TMT_AIRDROP_QUANTITY_LIMIT', '单次空投总数不得超过5000件');
      }

      if (totalQuantity > issueItem.quantity - issueItem.sales_volume) {
        throw new ExError('TMT_AIRDROP_STOCK_NOT_ENOUGH', '库存不足');
      }

      // 判断用户状态
      const users: any[] = [];
      for (let i = 0; i < uniqueUserIds.length; i += 1000) {
        const batchUserIds = uniqueUserIds.slice(i, i + 1000);
        const batchUserResult = await ctx.service.user.user.queryUsers({ _id: { $in: batchUserIds } }, [ '_id', 'status', 'patbg_detail', 'open_info' ]);
        users.push(...batchUserResult);
      }
      const userMap = new Map(users.map(user => [ user._id.toString(), user ]));
      for (const userId of uniqueUserIds) {
        const user = userMap.get(userId.toString());
        if (!user) {
          errList.push({ row: String(userId), msg: '不存在' });
        } else {
          if (user.status === StatusEnum.Disable) {
            errList.push({ row: String(userId), msg: '已禁用' });
            continue;
          }
          if (user.status === StatusEnum.Deleted) {
            errList.push({ row: String(userId), msg: '已注销' });
            continue;
          }
          if (!user.patbg_detail?.is_certified) {
            errList.push({ row: String(userId), msg: '未实名' });
            continue;
          }
          if (!user.open_info?.open_user_id) {
            errList.push({ row: String(userId), msg: '未绑定云仓' });
          }
        }
      }
      if (errList.length > 0) {
        throw new ExError('TMT_AIRDROP_USER_ERROR', '空投用户异常', errList);
      }

      const adminId = _.get(ctx.state.admin, '_id', 'unknown');
      // 新增空投记录
      const airdropRecord: any = await ctx.service.item.airdropRecord.createAirdropRecord({
        issue_item_id: issueItemId,
        airdrop_limit: airdropLimit,
        operator: adminId,
        airdrop_users: airdropUsers,
      });

      const bullPrefix = _.get(ctx.app.config, 'custom.bull.prefix', 'XIONGMAOMART-TMTBG:Bull');
      const tmtAirdropQname = _.get(ctx.app.config, 'custom.bull.tmt_airdrop_qname', '{issueItemAirdropTask}');
      for (const item of airdropUsers) {
        const message: Record<string, any> = {
          airdropRecordId: airdropRecord._id,
          userId: String(item?.user_id),
          issueItemId: String(issueItem?._id),
          airdropQuantity: item?.quantity,
          airdropLimit,
          adminId,
        };
        try {
          addQueueMessage(ctx, tmtAirdropQname, bullPrefix, message);
        } catch (e) {
          ctx.logger.error(`${this.logPrefix} TmtBg addQueueMessage error: ${JSON.stringify(e)} data:${JSON.stringify(message)}`);
        }
      }

      ctx.body = {
        code: 0,
        desc: 'success',
        data: {},
      };
    } catch (error) {
      throw error;
    } finally {
      await ctx.app.redis.del(lockKey);
    }
  }

  public async airdrop_order_list() {
    const { ctx } = this;
    if (typeof this.validator.airdrop_order_list === 'function') {
      await this.validator.airdrop_order_list();
    }

    const allowSearchFields: SearchField[] = [
      {
        key: 'created_at',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: '_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'item_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'sku_no',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'item_name',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.LIKE,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'user_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },

    ];
    const allowSortFields = [
      '_id',
    ];

    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    const query = filingQuerysByMongo({}, queryParse);

    const sort = filingSortsByMongo({ created_at: -1 }, queryParse);

    const aggregate = [
      {
        $match: query,
      },
    ];

    let list: any[] = [];
    const total = await ctx.service.item.airdropOrder.countAggregateAirdropOrder(aggregate);
    if (total > 0) {
      list = await ctx.service.item.airdropOrder.aggregateAirdropOrder(aggregate, {
        page: ctx.state.queryOptions.page,
        limit: ctx.state.queryOptions.limit,
        sort,
      });
      const userIds = list.map(item => item.user_id);
      const users = await ctx.service.user.user.queryUsers({ _id: { $in: userIds } });
      const operatorAdminIds = list.map(item => new ObjectId(item.operator));
      const adminUsers = await ctx.service.admin.admin.queryAdmins({ _id: { $in: operatorAdminIds } });
      // 发行商品
      const issueItemIds = list.map(item => new ObjectId(item.issue_item_id));
      const issueItems = await ctx.service.item.issueItem.queryIssueItems({ _id: { $in: issueItemIds } }, [ 'issuer_name', 'copyright_name', 'ip_classify_ids', 'ip_classify_names' ]);

      for (const item of list) {
        const user = users.find(user => String(user._id) === String(item.user_id));
        if (user) {
          item.nickname = _.get(user, 'patbg_detail.nickname', '');
        }
        const adminUser: any = adminUsers.find(admin => String(admin._id) === String(item.operator));
        const adminUserObj = adminUser.toObject();
        item.operator_real_name = adminUserObj?.real_name ?? '';
        item.operator_name = adminUserObj?.name ?? '';

        const issueItem = issueItems.find(issueItem => String(issueItem._id) === String(item.issue_item_id));
        if (issueItem) {
          item.ip_classify_names = issueItem.ip_classify_names;
          item.ip_classify_ids = issueItem.ip_classify_ids;
          item.issuer_name = issueItem.issuer_name;
          item.copyright_name = issueItem.copyright_name;
        }
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: {
        list,
        total,
      },
    };
  }

  /**
   * 修复首发订单发放气仓物品
   */
  public async fixOrderToYcUserItem() {
    const { ctx, app } = this;
    const orderIds = ctx.request.body?.order_ids;
    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      ctx.body = {
        code: 1,
        desc: '参数错误',
        data: {},
      };
      return;
    }
    // 查询首发订单
    const issueItemOrders: any[] = await ctx.service.item.issueItemOrder.queryIssueItemOrders(
      {
        _id: {
          $in: orderIds,
        },
      },
      [ '_id', 'issue_item_id', 'user_id', 'quantity' ],
    );
    const userIds = issueItemOrders.map(issueItemOrder => new ObjectId(issueItemOrder.user_id));
    // 查询用户数据
    const users: any[] = await ctx.service.user.user.queryUsers({
      _id: {
        $in: userIds,
      },
    });
    const userMap = new Map(users.map(item => [ String(item._id), item ]));

    const issueItemIds = issueItemOrders.map(issueItemOrder => new ObjectId(issueItemOrder.issue_item_id));
    const issueItems: any[] = await ctx.service.item.issueItem.queryIssueItems({
      _id: {
        $in: issueItemIds,
      },
    }, [ '_id', 'item_name', 'image_url', 'item_id', 'sale_mode', 'delivery_time', 'price' ]);
    const issueItemMap = new Map(issueItems.map(item => [ String(item._id), item ]));

    const prefix = ctx.app.config.custom.redis.prefix;
    const errors: any[] = [];
    for (const order of issueItemOrders) {
      const lockKey = `${prefix}:fixOrderToYcUserItem:${order._id}`;
      const lock: any = await getRedisLock(app, lockKey, 1000 * 60 * 24);
      if (!lock) {
        errors.push({
          order_id: order._id,
          msg: '重复执行',
        });
        continue;
      }

      const user = userMap.get(String(order.user_id));
      if (!user) {
        errors.push({
          order_id: order._id,
          msg: '用户不存在',
        });
        continue;
      }
      const issueItem = issueItemMap.get(String(order.issue_item_id));
      if (!issueItem) {
        errors.push({
          order_id: order._id,
          msg: '首发商品不存在',
        });
        continue;
      }

      try {
        const openUserId = _.get(user, 'open_info.open_user_id');
        await ctx.service.ycOpm.batchInsertUserItem({
          user_id: openUserId,
          receive_type: 124,
          items: [{
            item_id: issueItem.item_id,
            item_num: order.quantity,
            sale_mode: issueItem.sale_mode,
            delivery_time: issueItem.delivery_time,
            buy_price: issueItem.price,
          }],
          source_order_id: order._id,
        });
      } catch (error) {
        this.ctx.logger.error(`Failed to insert user item for order ID: ${order._id}`, error);
        errors.push({
          order_id: order._id,
          msg: '发放失败',
        });
      }
    }

    ctx.body = {
      code: 0,
      desc: 'success',
      data: errors,
    };
  }

  public async nonDeliveryStatistics() {
    const { ctx } = this;
    await this.validator.nonDeliveryStatistics();
    const id = ctx.state.id;

    // 查item_id
    const issueItem = await ctx.service.item.issueItem.queryIssueItem({ _id: id });
    if (!issueItem) {
      throw new ExError('TMT_DATA_NOT_EXIST');
    }
    const itemId = issueItem.item_id;

    const result = await ctx.service.ycOpm.countUserItemByItemIdsGroupByUserId([ itemId ],  [ UserItemStatusEnum.Owned, UserItemStatusEnum.Saleing ]);
    const itemStats = result && result.length > 0 ? result[0] : {
      item_id: itemId,
      user_count: 0,
      count: 0,
      cost: 0,
    };

    ctx.body = {
      code: 0,
      desc: 'success',
      data: {
        item_id: itemStats.item_id || itemId,
        user_count: itemStats.user_count || 0,
        item_count: itemStats.count || 0,
      },
    };
  }
}
