import { Application } from 'egg';
import { RouterGroup } from 'egg-router-group';
import saleOrderRouter from './sale_order';
import combinationRouter from './combination';
import userRouter from './user';
import adminRouter from './admin';
import steamItemRouter from './steam_item';
import issueItemRouter from './issue_item';
import reportRouter from './report';
import priorityBuyRouter from './priority_buy';
import bonusRouter from './bonus_order';
import configRouter from './config';


export default (app: Application) => {
  const {
    // controller,
    middleware,
    router,
  } = app;

  // middleware - auth_open
  const maintenance = middleware.maintenance({
    group: 'open',
    maintenanceModes: app.config.custom.maintenance_modes,
  });
  const authOpen = middleware.authOpen();

  router.group({
    name: 'openModule::',
    prefix: '/open',
    middlewares: [
      maintenance,
      authOpen,
    ],
  }, (groupRouter: RouterGroup) => {
    // 订单路由
    saleOrderRouter(app, groupRouter);

    // 拼箱路由
    combinationRouter(app, groupRouter);

    // 用户路由
    userRouter(app, groupRouter);

    // 管理端路由
    adminRouter(app, groupRouter);

    // 商品路由
    steamItemRouter(app, groupRouter);

    //广告商数据推送路由
    issueItemRouter(app, groupRouter);

    //数据统计路由
    reportRouter(app, groupRouter);

    //优先购路由
    priorityBuyRouter(app, groupRouter);

    //积分商城路由
    bonusRouter(app, groupRouter);

    // 配置相关路由
    configRouter(app, groupRouter);
  });
  router.group({
    name: 'openAdvertiser::',
    prefix: '/openAdvertiser',
    middlewares: [],
  }, (groupRouter: RouterGroup) => {
    //广告商数据推送路由
    issueItemRouter(app, groupRouter);
    reportRouter(app, groupRouter);
  });
};
