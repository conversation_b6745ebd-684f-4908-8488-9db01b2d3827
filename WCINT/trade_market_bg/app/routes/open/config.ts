import { Application } from 'egg';
import { RouterGroup } from 'egg-router-group';
import { apiUrlVersion } from 'utils/route';

export default (app: Application, groupRouter: RouterGroup) => {
  const {
    controller,
  } = app;

  groupRouter.group({
    name: 'config::',
    prefix: '/config',
    middlewares: [],
  }, (RouterGroup: RouterGroup) => {
    const ctrl = controller.open.config.config;

    RouterGroup.get(apiUrlVersion('/get_custom_config', 'v1'), ctrl.getCustomConfig);
  });
};
