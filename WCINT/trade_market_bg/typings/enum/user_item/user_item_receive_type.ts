/**
 * 背包物品获得类型
 */
export enum UserItemReceiveTypeEnum {
  /**
   * 其它
   */
  Other = 0,
  /**
   * 奖励
   */
  Reward = 1,
  /**
   * 赠送
   */
  Present = 2,
  /**
   * 造物
   */
  Creation = 3,
  /**
   * 领主
   */
  Suzerain = 4,
  /**
   * 二手交易
   */
  FantasyMartBuy = 10,
  /**
   * 首发商品
   */
  IssueItem= 124,
  /**
   * 空投商品
   */
  Airdrop= 125,
  /**
   * 融合
   */
  Synthesis = 126,
  /**
   * 故事探索
   */
  Story= 127,
  /**
   * 文潮转卖
   */
  WcReSell = 128,
}
export const UserItemReceiveTypeEnumKeys = Object.keys(UserItemReceiveTypeEnum).filter(k => typeof UserItemReceiveTypeEnum[k as any] === 'number');
export const UserItemReceiveTypeEnumValues = UserItemReceiveTypeEnumKeys.map(k => UserItemReceiveTypeEnum[k as any]);
