{"swagger": "2.0", "info": {"description": "接口文档", "title": "app API", "contact": {}, "version": "1.0.0"}, "paths": {"/admin/v1/common_config/detail": {"get": {"security": [{"Bearer": []}], "description": "获取配置详情", "produces": ["application/json"], "tags": ["管理端-配置管理"], "summary": "获取配置详情", "parameters": [{"type": "string", "description": "config_key", "name": "config_key", "in": "query"}, {"type": "string", "example": "0", "description": "主键", "name": "id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetConfigDetailResp"}}}]}}}, "x-apifox-folder": "管理端/配置管理"}}, "/admin/v1/common_config/edit": {"post": {"security": [{"Bearer": []}], "description": "编辑配置", "produces": ["application/json"], "tags": ["管理端-配置管理"], "summary": "编辑配置", "parameters": [{"description": "编辑参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.EditConfigReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.EditConfigResp"}}}]}}}, "x-apifox-folder": "管理端/配置管理"}}, "/admin/v1/common_config/list": {"get": {"security": [{"Bearer": []}], "description": "获取配置列表", "produces": ["application/json"], "tags": ["管理端-配置管理"], "summary": "获取配置列表", "parameters": [{"type": "string", "description": "逗号分隔", "name": "config_keys", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetConfigListResp"}}}]}}}, "x-apifox-folder": "管理端/配置管理"}}, "/admin/v1/home_feed/refresh": {"post": {"security": [{"Bearer": []}], "description": "管理端刷新首页商品", "produces": ["application/json"], "tags": ["管理端-首页管理"], "summary": "刷新首页商品", "parameters": [{"description": "商品信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.RefreshHomeFeedReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.RefreshHomeFeedResp"}}}]}}}, "x-apifox-folder": "管理端/首页管理"}}, "/admin/v1/mall_item/add": {"post": {"security": [{"Bearer": []}], "description": "管理端创建新商品", "produces": ["application/json"], "tags": ["管理端-直购商品管理"], "summary": "创建商品", "parameters": [{"description": "商品信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.AddMallItemReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.AddMallItemResp"}}}]}}}, "x-apifox-folder": "管理端/直购商品管理"}}, "/admin/v1/mall_item/detail": {"get": {"security": [{"Bearer": []}], "description": "管理端获取商品详细信息", "produces": ["application/json"], "tags": ["管理端-直购商品管理"], "summary": "获取商品详情", "parameters": [{"type": "string", "example": "0", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.MallItemDetailResp"}}}]}}}, "x-apifox-folder": "管理端/直购商品管理"}}, "/admin/v1/mall_item/edit": {"post": {"security": [{"Bearer": []}], "description": "管理端更新商品信息", "produces": ["application/json"], "tags": ["管理端-直购商品管理"], "summary": "更新商品", "parameters": [{"description": "更新参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.EditMallItemReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.EditMallItemResp"}}}]}}}, "x-apifox-folder": "管理端/直购商品管理"}}, "/admin/v1/mall_item/edit_priority": {"post": {"security": [{"Bearer": []}], "description": "管理端更新商品优先级", "produces": ["application/json"], "tags": ["管理端-直购商品管理"], "summary": "更新商品优先级", "parameters": [{"description": "更新参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.EditMallItemPriorityReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.EditMallItemPriorityResp"}}}]}}}, "x-apifox-folder": "管理端/直购商品管理"}}, "/admin/v1/mall_item/edit_status": {"post": {"security": [{"Bearer": []}], "description": "管理端更新商品信息", "produces": ["application/json"], "tags": ["管理端-直购商品管理"], "summary": "更新商品状态", "parameters": [{"description": "更新参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.EditMallItemStatusReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.EditMallItemStatusResp"}}}]}}}, "x-apifox-folder": "管理端/直购商品管理"}}, "/admin/v1/mall_item/list": {"get": {"security": [{"Bearer": []}], "description": "管理端获取商品分页列表", "produces": ["application/json"], "tags": ["管理端-直购商品管理"], "summary": "获取商品列表", "parameters": [{"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "分类", "name": "category_ids", "in": "query"}, {"type": "string", "example": "", "description": "直购商品ID", "name": "id", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "ip", "name": "ip_ids", "in": "query"}, {"type": "string", "description": "商品ID", "name": "item_id", "in": "query"}, {"type": "string", "description": "商品名称", "name": "item_name", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "description": "出售价格", "name": "sale_price_gte", "in": "query"}, {"type": "integer", "description": "出售价格", "name": "sale_price_lte", "in": "query"}, {"type": "string", "description": "sku_id", "name": "sku_id", "in": "query"}, {"type": "integer", "description": "状态", "name": "status", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "品牌", "name": "trademark_ids", "in": "query"}, {"type": "string", "description": "操作人", "name": "updated_by", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.MallItemPageResp"}}}}]}}}, "x-apifox-folder": "管理端/直购商品管理"}}, "/admin/v1/resale_item/add": {"post": {"security": [{"Bearer": []}], "description": "管理端创建转卖商品", "produces": ["application/json"], "tags": ["管理端-转卖商品管理"], "summary": "创建转卖商品", "parameters": [{"description": "商品信息", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.AddResaleItemReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.AddResaleItemResp"}}}]}}}, "x-apifox-folder": "管理端/转卖商品管理"}}, "/admin/v1/resale_item/detail": {"get": {"security": [{"Bearer": []}], "description": "管理端获取转卖商品详细信息", "produces": ["application/json"], "tags": ["管理端-转卖商品管理"], "summary": "获取转卖商品详情", "parameters": [{"type": "string", "example": "0", "description": "转卖商品ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.ResaleItemDetailResp"}}}]}}}, "x-apifox-folder": "管理端/转卖商品管理"}}, "/admin/v1/resale_item/edit": {"post": {"security": [{"Bearer": []}], "description": "管理端更新商品信息", "produces": ["application/json"], "tags": ["管理端-转卖商品管理"], "summary": "更新转卖商品", "parameters": [{"description": "更新参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.EditResaleItemReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.EditResaleItemResp"}}}]}}}, "x-apifox-folder": "管理端/转卖商品管理"}}, "/admin/v1/resale_item/edit_priority": {"post": {"security": [{"Bearer": []}], "description": "管理端更新转卖商品优先级", "produces": ["application/json"], "tags": ["管理端-转卖商品管理"], "summary": "更新转卖商品优先级", "parameters": [{"description": "更新参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.EditResaleItemPriorityReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.EditResaleItemPriorityResp"}}}]}}}, "x-apifox-folder": "管理端/转卖商品管理"}}, "/admin/v1/resale_item/edit_status": {"post": {"security": [{"Bearer": []}], "description": "管理端更新转卖商品状态", "produces": ["application/json"], "tags": ["管理端-转卖商品管理"], "summary": "更新转卖商品状态", "parameters": [{"description": "更新参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.EditResaleItemStatusReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.EditResaleItemStatusResp"}}}]}}}, "x-apifox-folder": "管理端/转卖商品管理"}}, "/admin/v1/resale_item/list": {"get": {"security": [{"Bearer": []}], "description": "管理端获取转卖商品分页列表", "produces": ["application/json"], "tags": ["管理端-转卖商品管理"], "summary": "获取转卖商品列表", "parameters": [{"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "商品分类", "name": "category_ids", "in": "query"}, {"type": "string", "example": "", "description": "转卖商品ID", "name": "id", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "商品ip", "name": "ip_ids", "in": "query"}, {"type": "integer", "description": "是否使用特定限价（0-关闭, 1-开启）", "name": "is_custom_price_limit", "in": "query"}, {"type": "string", "description": "商品ID", "name": "item_id", "in": "query"}, {"type": "string", "description": "商品名称", "name": "item_name", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "description": "转卖状态（0=关闭，1=开启）", "name": "resale_status", "in": "query"}, {"type": "string", "description": "商品 sku_id", "name": "sku_id", "in": "query"}, {"type": "integer", "description": "状态（0=待上架，1=已上架，2=已下架）", "name": "status", "in": "query"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "description": "商品品牌", "name": "trademark_ids", "in": "query"}, {"type": "string", "description": "操作人", "name": "updated_by", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.ResaleItemPageResp"}}}}]}}}, "x-apifox-folder": "管理端/转卖商品管理"}}, "/admin/v1/resale_listings/detail": {"get": {"security": [{"Bearer": []}], "description": "管理端获取转卖挂单详细信息", "produces": ["application/json"], "tags": ["管理端-转卖挂单管理"], "summary": "获取转卖挂单详情", "parameters": [{"type": "string", "example": "0", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetAdminResaleListingsDetailResp"}}}]}}}, "x-apifox-folder": "管理端/转卖挂单管理"}}, "/admin/v1/resale_listings/export": {"get": {"security": [{"Bearer": []}], "description": "导出转卖挂单列表，下载文件", "produces": ["application/json"], "tags": ["管理端-转卖挂单管理"], "summary": "导出转卖挂单列表", "parameters": [{"type": "string", "description": "创建时间开始", "name": "created_at_gte", "in": "query"}, {"type": "string", "description": "创建时间结束", "name": "created_at_lte", "in": "query"}, {"type": "string", "example": "0", "description": "出售单号", "name": "id", "in": "query"}, {"type": "string", "description": "商品ID", "name": "item_id", "in": "query"}, {"type": "string", "description": "商品名称", "name": "item_name", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "string", "description": "卖家 id", "name": "seller_id", "in": "query"}, {"type": "string", "description": "卖家手机号", "name": "seller_phone", "in": "query"}, {"type": "string", "description": "商品sku_id", "name": "sku_id", "in": "query"}, {"type": "integer", "description": "挂单状态（0-已下架，1-出售中，2-已出售，3-已取消）", "name": "status", "in": "query"}, {"type": "string", "description": "终端", "name": "terminal", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetAdminResaleListingsListResp"}}}]}}}, "x-apifox-folder": "管理端/转卖挂单管理"}}, "/admin/v1/resale_listings/list": {"get": {"security": [{"Bearer": []}], "description": "管理端获取转卖挂单分页列表", "produces": ["application/json"], "tags": ["管理端-转卖挂单管理"], "summary": "获取转卖挂单列表", "parameters": [{"type": "string", "description": "创建时间开始", "name": "created_at_gte", "in": "query"}, {"type": "string", "description": "创建时间结束", "name": "created_at_lte", "in": "query"}, {"type": "string", "example": "0", "description": "出售单号", "name": "id", "in": "query"}, {"type": "string", "description": "商品ID", "name": "item_id", "in": "query"}, {"type": "string", "description": "商品名称", "name": "item_name", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "string", "description": "卖家 id", "name": "seller_id", "in": "query"}, {"type": "string", "description": "卖家手机号", "name": "seller_phone", "in": "query"}, {"type": "string", "description": "商品sku_id", "name": "sku_id", "in": "query"}, {"type": "integer", "description": "挂单状态（0-已下架，1-出售中，2-已出售，3-已取消）", "name": "status", "in": "query"}, {"type": "string", "description": "终端", "name": "terminal", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.GetAdminResaleListingsListResp"}}}}]}}}, "x-apifox-folder": "管理端/转卖挂单管理"}}, "/admin/v1/resale_listings/update_status": {"post": {"security": [{"Bearer": []}], "description": "管理端更新转卖挂单状态，比如下架", "produces": ["application/json"], "tags": ["管理端-转卖挂单管理"], "summary": "更新转卖挂单状态", "parameters": [{"type": "string", "example": "0", "name": "id", "in": "query", "required": true}, {"type": "integer", "name": "status", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.UpdateResaleListingsStatusResp"}}}]}}}, "x-apifox-folder": "管理端/转卖挂单管理"}}, "/admin/v1/resale_order/detail": {"get": {"security": [{"Bearer": []}], "description": "管理端获取转卖订单详细信息", "produces": ["application/json"], "tags": ["管理端-转卖订单管理"], "summary": "获取转卖订单详情", "parameters": [{"type": "string", "example": "0", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetAdminResaleOrderDetailResp"}}}]}}}, "x-apifox-folder": "管理端/转卖订单管理"}}, "/admin/v1/resale_order/export": {"get": {"security": [{"Bearer": []}], "description": "导出转卖订单列表，下载文件", "produces": ["application/json"], "tags": ["管理端-转卖订单管理"], "summary": "导出转卖订单列表", "parameters": [{"type": "string", "description": "购买用户ID", "name": "buyer_id", "in": "query"}, {"type": "string", "description": "购买用户手机号码", "name": "buyer_phone", "in": "query"}, {"type": "string", "description": "创建时间开始", "name": "created_at_gte", "in": "query"}, {"type": "string", "description": "创建时间结束", "name": "created_at_lte", "in": "query"}, {"type": "string", "example": "0", "description": "合同编号", "name": "id", "in": "query"}, {"type": "string", "description": "商品ID", "name": "item_id", "in": "query"}, {"type": "string", "description": "商品名称", "name": "item_name", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "string", "example": "0", "description": "出售单号", "name": "resale_listings_id", "in": "query"}, {"type": "string", "description": "出售用户ID", "name": "seller_id", "in": "query"}, {"type": "string", "description": "出售用户手机号码", "name": "seller_phone", "in": "query"}, {"type": "string", "description": "商品sku_id", "name": "sku_id", "in": "query"}, {"type": "integer", "description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "name": "status", "in": "query"}, {"type": "string", "description": "终端", "name": "terminal", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetAdminResaleOrderListResp"}}}]}}}, "x-apifox-folder": "管理端/转卖订单管理"}}, "/admin/v1/resale_order/list": {"get": {"security": [{"Bearer": []}], "description": "管理端获取转卖订单分页列表", "produces": ["application/json"], "tags": ["管理端-转卖订单管理"], "summary": "获取转卖订单列表", "parameters": [{"type": "string", "description": "购买用户ID", "name": "buyer_id", "in": "query"}, {"type": "string", "description": "购买用户手机号码", "name": "buyer_phone", "in": "query"}, {"type": "string", "description": "创建时间开始", "name": "created_at_gte", "in": "query"}, {"type": "string", "description": "创建时间结束", "name": "created_at_lte", "in": "query"}, {"type": "string", "example": "0", "description": "合同编号", "name": "id", "in": "query"}, {"type": "string", "description": "商品ID", "name": "item_id", "in": "query"}, {"type": "string", "description": "商品名称", "name": "item_name", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "string", "example": "0", "description": "出售单号", "name": "resale_listings_id", "in": "query"}, {"type": "string", "description": "出售用户ID", "name": "seller_id", "in": "query"}, {"type": "string", "description": "出售用户手机号码", "name": "seller_phone", "in": "query"}, {"type": "string", "description": "商品sku_id", "name": "sku_id", "in": "query"}, {"type": "integer", "description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "name": "status", "in": "query"}, {"type": "string", "description": "终端", "name": "terminal", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.GetAdminResaleOrderListResp"}}}}]}}}, "x-apifox-folder": "管理端/转卖订单管理"}}, "/admin/v1/trade_order/cancel": {"post": {"security": [{"Bearer": []}], "description": "取消订单", "produces": ["application/json"], "tags": ["管理端-直购订单管理"], "summary": "取消订单", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.CancelAdminTradeOrderReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.CancelAdminTradeOrderResp"}}}]}}}, "x-apifox-folder": "管理端/直购订单管理"}}, "/admin/v1/trade_order/detail": {"get": {"security": [{"Bearer": []}], "description": "管理端获取订单详细信息", "produces": ["application/json"], "tags": ["管理端-直购订单管理"], "summary": "获取订单详情", "parameters": [{"type": "string", "example": "0", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetAdminTradeOrderDetailResp"}}}]}}}, "x-apifox-folder": "管理端/直购订单管理"}}, "/admin/v1/trade_order/list": {"get": {"security": [{"Bearer": []}], "description": "管理端获取订单分页列表", "produces": ["application/json"], "tags": ["管理端-直购订单管理"], "summary": "获取订单列表", "parameters": [{"type": "string", "description": "创建时间开始", "name": "created_at_gte", "in": "query"}, {"type": "string", "description": "创建时间结束", "name": "created_at_lte", "in": "query"}, {"type": "string", "name": "delivery_number", "in": "query"}, {"type": "string", "example": "0", "description": "订单号", "name": "id", "in": "query"}, {"type": "string", "description": "商品ID", "name": "item_id", "in": "query"}, {"type": "string", "description": "商品名称", "name": "item_name", "in": "query"}, {"type": "string", "example": "0", "description": "直购商品ID", "name": "mall_item_id", "in": "query"}, {"type": "integer", "description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）", "name": "order_status", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "string", "description": "支付订单ID", "name": "pay_order_id", "in": "query"}, {"type": "string", "description": "商品 sku_id", "name": "sku_id", "in": "query"}, {"type": "string", "description": "用户ID", "name": "user_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.GetAdminTradeOrderListResp"}}}}]}}}, "x-apifox-folder": "管理端/直购订单管理"}}, "/admin/v1/trade_order/private_info": {"get": {"security": [{"Bearer": []}], "description": "管理端获取订单隐私详情", "produces": ["application/json"], "tags": ["管理端-直购订单管理"], "summary": "获取订单隐私详情", "parameters": [{"type": "string", "example": "0", "description": "订单id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.AdminGetOrderPrivateInfoResp"}}}]}}}, "x-apifox-folder": "管理端/直购订单管理"}}, "/admin/v1/trade_order/refund": {"post": {"security": [{"Bearer": []}], "description": "订单发起退款", "produces": ["application/json"], "tags": ["管理端-直购订单管理"], "summary": "订单发起退款", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.TradeOrderRefundReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.TradeOrderRefundResp"}}}]}}}, "x-apifox-folder": "管理端/直购订单管理"}}, "/admin/v1/trade_order/sync_freight": {"post": {"security": [{"Bearer": []}], "description": "同步订单物流信息", "produces": ["application/json"], "tags": ["管理端-直购订单管理"], "summary": "同步订单物流信息", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.SyncAdminTradeOrderFreightReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.SyncAdminTradeOrderFreightResp"}}}]}}}, "x-apifox-folder": "管理端/直购订单管理"}}, "/open/v1/home_feed/sync_view_count": {"post": {"security": [{"Bearer": []}], "description": "同步首页浏览量到数据库", "produces": ["application/json"], "tags": ["open端-首页管理"], "summary": "同步首页浏览量到数据库", "parameters": [{"description": "获取参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.OpenSyncViewCountToDBReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.OpenSyncViewCountToDBResp"}}}]}}}, "x-apifox-folder": "open端/首页管理"}}, "/open/v1/mall_item/trigger_display": {"post": {"security": [{"Bearer": []}], "description": "处理开始展示的直购商品", "produces": ["application/json"], "tags": ["open端-直购商品"], "summary": "处理开始展示的直购商品", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.OpenTriggerDisplayByStartTimeReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/response.Data"}}}, "x-apifox-folder": "open端/直购商品"}}, "/open/v1/mall_item/update_notify": {"post": {"security": [{"Bearer": []}], "description": "商品信息变动通知", "produces": ["application/json"], "tags": ["open端-直购商品"], "summary": "商品信息变动通知", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.OpenUpdateMallItemNotifyReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/response.Data"}}}, "x-apifox-folder": "open端/直购商品"}}, "/open/v1/notice/supply_chain": {"post": {"security": [{"Bearer": []}], "description": "供应链通知", "produces": ["application/json"], "tags": ["open端-通知管理"], "summary": "供应链通知", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.SupplyChainNoticeReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.SupplyChainNoticeResp"}}}]}}}, "x-apifox-folder": "open端/通知管理"}}, "/open/v1/resale_order/locked_handler": {"post": {"security": [{"Bearer": []}], "description": "物品异常锁定订单处理", "produces": ["application/json"], "tags": ["open端-转卖订单"], "summary": "物品异常锁定订单处理", "parameters": [{"description": "获取参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.LockedHandlerReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.LockedHandlerResp"}}}]}}}, "x-apifox-folder": "open端/转卖订单"}}, "/open/v1/resale_order/release_user_items": {"post": {"security": [{"Bearer": []}], "description": "释放锁单失败订单的背包物品", "produces": ["application/json"], "tags": ["open端-转卖订单"], "summary": "释放锁单失败订单的背包物品", "parameters": [{"description": "获取参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.ReleaseUserItemsReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.ReleaseUserItemsResp"}}}]}}}, "x-apifox-folder": "open端/转卖订单"}}, "/open/v1/resale_order/resale_item_transfer_handler": {"post": {"security": [{"Bearer": []}], "description": "物品发放异常订单处理", "produces": ["application/json"], "tags": ["open端-转卖订单"], "summary": "物品发放异常订单处理", "parameters": [{"description": "获取参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.ResaleItemTransferHandlerReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.ResaleItemTransferHandlerResp"}}}]}}}, "x-apifox-folder": "open端/转卖订单"}}, "/open/v1/trade_order/detail": {"get": {"security": [{"Bearer": []}], "description": "获取订单详情", "produces": ["application/json"], "tags": ["open端-直购订单"], "summary": "获取订单详情", "parameters": [{"type": "string", "example": "0", "name": "id", "in": "query", "required": true}, {"type": "string", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.GetTradeOrderDetailResp"}}}}]}}}, "x-apifox-folder": "open端/直购订单"}}, "/open/v1/trade_order/finish": {"post": {"security": [{"Bearer": []}], "description": "订单支付自动收货", "produces": ["application/json"], "tags": ["open端-直购订单"], "summary": "订单支付自动收货", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.TradeOrderFinishReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.TradeOrderFinishResp"}}}]}}}, "x-apifox-folder": "open端/直购订单"}}, "/open/v1/trade_order/pay_success": {"post": {"security": [{"Bearer": []}], "description": "订单支付成功回调", "produces": ["application/json"], "tags": ["open端-直购订单"], "summary": "订单支付成功回调", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.TradeOrderPaySuccessReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.TradeOrderPaySuccessResp"}}}]}}}, "x-apifox-folder": "open端/直购订单"}}, "/open/v1/trade_order/retry_upload_to_supply": {"post": {"security": [{"Bearer": []}], "description": "重新上传失败的订单到供应链", "produces": ["application/json"], "tags": ["open端-直购订单"], "summary": "重新上传失败的订单到供应链", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.TradeOrderFailedRetryUploadToSupplyReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/response.Data"}}}, "x-apifox-folder": "open端/直购订单"}}, "/open/v1/trade_order/timeout_close": {"post": {"security": [{"Bearer": []}], "description": "订单支付超时关闭", "produces": ["application/json"], "tags": ["open端-直购订单"], "summary": "订单支付超时关闭", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.TradeOrderTimeoutCloseReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/response.Data"}}}, "x-apifox-folder": "open端/直购订单"}}, "/web/v1/home_feed/add_view_count": {"post": {"security": [{"Bearer": []}], "description": "增加浏览数量", "produces": ["application/json"], "tags": ["用户端-首页管理"], "summary": "增加浏览数量", "parameters": [{"description": "获取参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.WebAddHomeFeedViewCountReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.WebAddHomeFeedViewCountResp"}}}]}}}, "x-apifox-folder": "用户端/首页管理"}}, "/web/v1/home_feed/detail": {"get": {"security": [{"Bearer": []}], "description": "获取首页详情", "produces": ["application/json"], "tags": ["用户端-首页管理"], "summary": "获取首页详情", "parameters": [{"type": "string", "example": "0", "description": "首页ID", "name": "id", "in": "query"}, {"type": "string", "description": "商品ID", "name": "item_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.WebGetHomeFeedDetailResp"}}}]}}}, "x-apifox-folder": "用户端/首页管理"}}, "/web/v1/home_feed/list": {"get": {"security": [{"Bearer": []}], "description": "获取首页列表", "produces": ["application/json"], "tags": ["用户端-首页管理"], "summary": "获取首页列表", "parameters": [{"type": "string", "description": "商品 ip_id", "name": "ip_id", "in": "query"}, {"type": "integer", "description": "排序类型 1=综合，2=销量，3=价格", "name": "order_by", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "string", "description": "排序方式  asc-升序 desc-降序", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.WebGetHomeFeedListResp"}}}]}}}, "x-apifox-folder": "用户端/首页管理"}}, "/web/v1/mall_item/buy": {"post": {"security": [{"Bearer": []}], "description": "下单购买", "produces": ["application/json"], "tags": ["用户端-直购商品"], "summary": "下单购买", "parameters": [{"description": "获取参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.WebBuyReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.WebBuyResp"}}}]}}}, "x-apifox-folder": "用户端/直购商品"}}, "/web/v1/mall_item/detail": {"get": {"security": [{"Bearer": []}], "description": "获取直购商品详情", "produces": ["application/json"], "tags": ["用户端-直购商品"], "summary": "获取直购商品详情", "parameters": [{"type": "string", "example": "0", "description": "直购商品ID", "name": "id", "in": "query"}, {"type": "string", "description": "商品ID", "name": "item_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.WebGetMallItemDetailResp"}}}]}}}, "x-apifox-folder": "用户端/直购商品"}}, "/web/v1/mall_item/list": {"get": {"security": [{"Bearer": []}], "description": "获取直购商品列表", "produces": ["application/json"], "tags": ["用户端-直购商品"], "summary": "获取直购商品列表", "parameters": [{"type": "string", "description": "商品 ip_id", "name": "ip_id", "in": "query"}, {"type": "integer", "description": "排序类型 1=综合，2=销量，3=价格", "name": "order_by", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "string", "description": "排序方式  asc-升序 desc-降序", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.WebGetMallItemListResp"}}}]}}}, "x-apifox-folder": "用户端/直购商品"}}, "/web/v1/resale_item/buy": {"post": {"security": [{"Bearer": []}], "description": "转卖商品购买", "produces": ["application/json"], "tags": ["用户端-转卖商品"], "summary": "转卖商品购买", "parameters": [{"description": "获取参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.ResaleItemBuyReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.ResaleItemBuyResp"}}}]}}}, "x-apifox-folder": "用户端/转卖商品"}}, "/web/v1/resale_listings/add": {"post": {"security": [{"Bearer": []}], "description": "挂单出售", "produces": ["application/json"], "tags": ["用户端-转卖挂单管理"], "summary": "挂单出售", "parameters": [{"description": "参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.AddResaleListingsReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.AddResaleListingsResp"}}}]}}}, "x-apifox-folder": "用户端/转卖挂单"}}, "/web/v1/resale_listings/published_list": {"get": {"security": [{"Bearer": []}], "description": "查询我发布的转卖挂单列表", "produces": ["application/json"], "tags": ["用户端-转卖挂单"], "summary": "查询我发布的转卖挂单列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebPublishedResaleListingsListResp"}}}]}}}, "x-apifox-folder": "用户端/转卖挂单"}}, "/web/v1/resale_listings/take_down": {"post": {"security": [{"Bearer": []}], "description": "下架转卖挂单", "produces": ["application/json"], "tags": ["用户端-转卖挂单"], "summary": "下架转卖挂单", "parameters": [{"description": "查询参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.TakeDownResaleListingsFromWebReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.TakeDownResaleListingsFromWebResp"}}}]}}}, "x-apifox-folder": "用户端/转卖挂单"}}, "/web/v1/resale_listings_item/on_sale_list": {"get": {"security": [{"Bearer": []}], "description": "查询某个挂单商品在售列表", "produces": ["application/json"], "tags": ["用户端-转卖挂单"], "summary": "查询某个挂单商品在售列表", "parameters": [{"type": "string", "description": "商品 id", "name": "item_id", "in": "query", "required": true}, {"type": "string", "description": "排序字段，sale_price: 按照出售单价排序，created_at: 按照创建时间/发布时间排序", "name": "order_by", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "string", "description": "排序方式，asc：升序，desc：降序", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebResaleListingsItemOnSaleListResp"}}}]}}}, "x-apifox-folder": "用户端/转卖挂单"}}, "/web/v1/resale_listings_item/settlement_list": {"get": {"security": [{"Bearer": []}], "description": "查询某个挂单商品购买结算列表", "produces": ["application/json"], "tags": ["用户端-转卖挂单管理"], "summary": "查询某个挂单商品购买结算列表", "parameters": [{"type": "string", "description": "商品 id", "name": "item_id", "in": "query", "required": true}, {"minimum": 1, "type": "integer", "description": "购买数量", "name": "quantity", "in": "query", "required": true}, {"type": "integer", "description": "出售单价（分）", "name": "sale_price", "in": "query"}, {"type": "string", "description": "卖家 id", "name": "seller_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebResaleListingsItemSettlementListResp"}}}]}}}, "x-apifox-folder": "用户端/转卖挂单"}}, "/web/v1/resale_order/buy/detail": {"get": {"security": [{"Bearer": []}], "description": "查询我购买的转卖订单详情", "produces": ["application/json"], "tags": ["用户端-转卖订单"], "summary": "查询我购买的转卖订单详情", "parameters": [{"type": "string", "example": "0", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleOrderBuyDetailResp"}}}}]}}}, "x-apifox-folder": "用户端/转卖订单"}}, "/web/v1/resale_order/buy/list": {"get": {"security": [{"Bearer": []}], "description": "查询我购买的转卖订单列表", "produces": ["application/json"], "tags": ["用户端-转卖订单"], "summary": "查询我购买的转卖订单列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebResaleOrderBuyListResp"}}}]}}}, "x-apifox-folder": "用户端/转卖订单"}}, "/web/v1/resale_order/recent_list": {"get": {"security": [{"Bearer": []}], "description": "查询最近成交记录", "produces": ["application/json"], "tags": ["用户端-转卖订单"], "summary": "查询最近成交记录", "parameters": [{"type": "string", "description": "商品ID", "name": "item_id", "in": "query", "required": true}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebResaleOrderRecentListResp"}}}]}}}, "x-apifox-folder": "用户端/转卖订单"}}, "/web/v1/resale_order/sale/detail": {"get": {"security": [{"Bearer": []}], "description": "查询我出售的转卖订单详情", "produces": ["application/json"], "tags": ["用户端-转卖订单"], "summary": "查询我出售的转卖订单详情", "parameters": [{"type": "string", "example": "0", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleOrderSaleDetailResp"}}}}]}}}, "x-apifox-folder": "用户端/转卖订单"}}, "/web/v1/resale_order/sale/list": {"get": {"security": [{"Bearer": []}], "description": "查询我出售的转卖订单列表", "produces": ["application/json"], "tags": ["用户端-转卖订单"], "summary": "查询我出售的转卖订单列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}, {"type": "integer", "description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebResaleOrderSaleListResp"}}}]}}}, "x-apifox-folder": "用户端/转卖订单"}}, "/web/v1/resale_user_item/cancel": {"post": {"security": [{"Bearer": []}], "description": "取消出售", "produces": ["application/json"], "tags": ["用户端-转卖商品持仓"], "summary": "取消出售", "parameters": [{"description": "查询参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.CancelResaleUserItemFromWebReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.CancelResaleUserItemFromWebResp"}}}]}}}, "x-apifox-folder": "用户端/转卖商品持仓"}}, "/web/v1/resale_user_item/item_list": {"get": {"security": [{"Bearer": []}], "description": "查询转卖商品持仓商品列表", "produces": ["application/json"], "tags": ["用户端-转卖商品持仓"], "summary": "查询转卖商品持仓商品列表", "parameters": [{"type": "string", "description": "商品名称", "name": "item_name", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebResaleUserItemListByItemResp"}}}]}}}, "x-apifox-folder": "用户端/转卖商品持仓"}}, "/web/v1/resale_user_item/trade_info": {"get": {"security": [{"Bearer": []}], "description": "获取持仓商品交易信息", "produces": ["application/json"], "tags": ["用户端-转卖商品持仓"], "summary": "获取持仓商品交易信息", "parameters": [{"type": "string", "description": "商品 id", "name": "item_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebResaleUserItemTradeInfoResp"}}}]}}}, "x-apifox-folder": "用户端/转卖商品持仓"}}, "/web/v1/resale_user_item/user_item_list": {"get": {"security": [{"Bearer": []}], "description": "获取某个转卖商品的持仓列表", "produces": ["application/json"], "tags": ["用户端-转卖商品持仓"], "summary": "获取某个转卖商品的持仓列表", "parameters": [{"type": "string", "description": "商品 id", "name": "item_id", "in": "query", "required": true}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebResaleUserItemListByUserItemResp"}}}]}}}, "x-apifox-folder": "用户端/转卖商品持仓"}}, "/web/v1/trade_order/cancel": {"post": {"security": [{"Bearer": []}], "description": "取消订单", "produces": ["application/json"], "tags": ["用户端-直购订单"], "summary": "取消订单", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.CancelTradeOrderReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.CancelTradeOrderResp"}}}]}}}, "x-apifox-folder": "用户端/直购订单"}}, "/web/v1/trade_order/confirm": {"post": {"security": [{"Bearer": []}], "description": "确定收货", "produces": ["application/json"], "tags": ["用户端-直购订单"], "summary": "确定收货", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.ConfirmTradeOrderReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.ConfirmTradeOrderResp"}}}]}}}, "x-apifox-folder": "用户端/直购订单"}}, "/web/v1/trade_order/del": {"post": {"security": [{"Bearer": []}], "description": "删除订单", "produces": ["application/json"], "tags": ["用户端-直购订单"], "summary": "删除订单", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.DelTradeOrderReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.DelTradeOrderResp"}}}]}}}, "x-apifox-folder": "用户端/直购订单"}}, "/web/v1/trade_order/detail": {"get": {"security": [{"Bearer": []}], "description": "查询直购订单详情", "tags": ["用户端-直购订单"], "summary": "查询直购订单详情", "parameters": [{"type": "string", "example": "0", "description": "订单id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebTradeOrderDetailResp"}}}}]}}}, "x-apifox-folder": "用户端/直购订单"}}, "/web/v1/trade_order/freight_info": {"get": {"security": [{"Bearer": []}], "description": "查询直购订单物流信息", "produces": ["application/json"], "tags": ["用户端-直购订单"], "summary": "查询直购订单物流信息", "parameters": [{"type": "string", "description": "快递单号", "name": "delivery_number", "in": "query", "required": true}, {"type": "string", "example": "0", "description": "订单id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebTradeOrderFreightInfoResp"}}}}]}}}, "x-apifox-folder": "用户端/直购订单"}}, "/web/v1/trade_order/list": {"get": {"security": [{"Bearer": []}], "description": "查询直购订单列表", "tags": ["用户端-直购订单"], "summary": "查询直购订单列表", "parameters": [{"type": "integer", "description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）", "name": "order_status", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.GetWebTradeOrderListResp"}}}]}}}, "x-apifox-folder": "用户端/直购订单"}}, "/web/v1/trade_order/status_stat": {"get": {"security": [{"Bearer": []}], "description": "查询直购订单状态统计", "produces": ["application/json"], "tags": ["用户端-直购订单"], "summary": "查询直购订单状态统计", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebTradeOrderStatusStatResp"}}}}]}}}, "x-apifox-folder": "用户端/直购订单"}}, "/web/v1/trade_order/update_address": {"post": {"security": [{"Bearer": []}], "description": "修改地址", "produces": ["application/json"], "tags": ["用户端-直购订单"], "summary": "修改地址", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.UpdateTradeOrderAddressReq"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.UpdateTradeOrderAddressResp"}}}]}}}, "x-apifox-folder": "用户端/直购订单"}}, "/web/v1/user_wishlist/add": {"post": {"security": [{"Bearer": []}], "description": "添加想要", "produces": ["application/json"], "tags": ["用户端-商品想要"], "summary": "添加想要", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.WebWishlistReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/response.Data"}}}, "x-apifox-folder": "用户端/商品想要"}}, "/web/v1/user_wishlist/list": {"get": {"security": [{"Bearer": []}], "description": "用户想要列表", "produces": ["application/json"], "tags": ["用户端-商品想要"], "summary": "用户想要列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页显示的条目数量", "name": "page_size", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/response.Data"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/define.WebWishlistListResp"}}}]}}}, "x-apifox-folder": "用户端/商品想要"}}, "/web/v1/user_wishlist/remove": {"post": {"security": [{"Bearer": []}], "description": "移除想要", "produces": ["application/json"], "tags": ["用户端-商品想要"], "summary": "移除想要", "parameters": [{"description": "请求参数", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/define.WebWishlistReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/response.Data"}}}, "x-apifox-folder": "用户端/商品想要"}}}, "definitions": {"define.AddMallItemReq": {"type": "object", "required": ["daily_limit", "freight", "item_id", "sale_price", "sku_id", "start_time", "stock", "stock_type", "total_limit"], "properties": {"daily_limit": {"description": "每日限购数量", "type": "integer", "minimum": 0}, "discount": {"description": "折扣", "type": "integer", "maximum": 100, "minimum": 1}, "freight": {"description": "运费(分)", "type": "integer", "minimum": 0}, "item_id": {"description": "商品ID", "type": "string"}, "priority": {"description": "优先级", "type": "integer", "maximum": 9999, "minimum": 0}, "sale_price": {"description": "售价(分)", "type": "integer", "minimum": 1}, "sku_id": {"description": "商品 sku_id", "type": "string"}, "start_time": {"description": "开始展示时间", "type": "string"}, "stock": {"description": "限定库存量", "type": "integer", "minimum": 1}, "stock_type": {"description": "库存类型（1=限定库存，2=同步商品库存）", "type": "integer"}, "total_limit": {"description": "总限购数量", "type": "integer", "minimum": 0}}}, "define.AddMallItemResp": {"type": "object", "properties": {"id": {"description": "直购商品ID", "type": "string", "example": "0"}}}, "define.AddResaleItemReq": {"type": "object", "required": ["item_id", "sku_id"], "properties": {"item_id": {"description": "商品ID", "type": "string"}, "priority": {"description": "优先级", "type": "integer", "maximum": 9999, "minimum": 0}, "sku_id": {"description": "商品 sku_id", "type": "string"}}}, "define.AddResaleItemResp": {"type": "object", "properties": {"id": {"description": "转卖商品ID", "type": "string", "example": "0"}}}, "define.AddResaleListingsReq": {"type": "object", "required": ["item_id", "quantity", "sale_price"], "properties": {"item_id": {"description": "商品 id", "type": "string"}, "quantity": {"description": "出售数量", "type": "integer", "minimum": 1}, "sale_price": {"description": "卖出价格（分）", "type": "integer", "minimum": 2}, "sell_password": {"description": "出售密码（MD5 32位纯小写）", "type": "string"}, "user_item_id": {"description": "持仓 id/背包 id，如果传了表示单个出售", "type": "string"}}}, "define.AddResaleListingsResp": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "id": {"description": "主键", "type": "string", "example": "0"}, "quantity": {"description": "出售数量", "type": "integer"}}}, "define.AdminGetOrderPrivateInfoResp": {"type": "object", "properties": {"consignee_phone": {"description": "收货人电话", "type": "string"}}}, "define.CancelAdminTradeOrderReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "订单id", "type": "string", "example": "0"}}}, "define.CancelAdminTradeOrderResp": {"type": "object"}, "define.CancelResaleUserItemFromWebReq": {"type": "object", "properties": {"id": {"type": "string"}}}, "define.CancelResaleUserItemFromWebResp": {"type": "object", "properties": {"canceled_at": {"type": "string"}, "id": {"type": "string"}}}, "define.CancelTradeOrderReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "订单id", "type": "string", "example": "0"}}}, "define.CancelTradeOrderResp": {"type": "object"}, "define.Config": {"type": "object", "properties": {"config_key": {"description": "config_key", "type": "string"}, "id": {"description": "主键", "type": "string", "example": "0"}, "remark": {"description": "备注", "type": "string"}, "value": {"description": "value"}}}, "define.ConfirmTradeOrderReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "订单id", "type": "string", "example": "0"}}}, "define.ConfirmTradeOrderResp": {"type": "object"}, "define.DelTradeOrderReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "订单id", "type": "string", "example": "0"}}}, "define.DelTradeOrderResp": {"type": "object"}, "define.EditConfigReq": {"type": "object", "required": ["config_key", "value"], "properties": {"config_key": {"description": "config_key", "type": "string"}, "value": {"description": "value"}}}, "define.EditConfigResp": {"type": "object", "properties": {"id": {"type": "string", "example": "0"}}}, "define.EditMallItemPriorityReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "string", "example": "0"}, "priority": {"description": "优先级", "type": "integer", "maximum": 9999, "minimum": 1}}}, "define.EditMallItemPriorityResp": {"type": "object", "properties": {"id": {"description": "直购商品ID", "type": "string", "example": "0"}}}, "define.EditMallItemReq": {"type": "object", "required": ["daily_limit", "freight", "id", "item_id", "sale_price", "sku_id", "start_time", "stock", "stock_type", "total_limit"], "properties": {"daily_limit": {"description": "每日限购数量", "type": "integer", "minimum": 0}, "discount": {"description": "折扣", "type": "integer", "maximum": 100, "minimum": 1}, "freight": {"description": "运费(分)", "type": "integer", "minimum": 0}, "id": {"description": "直购商品ID", "type": "string", "example": "0"}, "item_id": {"description": "商品ID", "type": "string"}, "priority": {"description": "优先级", "type": "integer", "maximum": 9999, "minimum": 0}, "sale_price": {"description": "售价(分)", "type": "integer", "minimum": 1}, "sku_id": {"description": "商品 sku_id", "type": "string"}, "start_time": {"description": "开始展示时间", "type": "string"}, "stock": {"description": "限定库存量", "type": "integer", "minimum": 1}, "stock_type": {"description": "库存类型（1=限定库存，2=同步商品库存）", "type": "integer"}, "total_limit": {"description": "总限购数量", "type": "integer", "minimum": 0}}}, "define.EditMallItemResp": {"type": "object", "properties": {"id": {"description": "直购商品ID", "type": "string", "example": "0"}}}, "define.EditMallItemStatusReq": {"type": "object", "required": ["id", "status"], "properties": {"id": {"description": "ID", "type": "string", "example": "0"}, "status": {"description": "状态（0=待上架，1=已上架，2=已下架）", "type": "integer", "minimum": 0}}}, "define.EditMallItemStatusResp": {"type": "object", "properties": {"id": {"description": "直购商品ID", "type": "string", "example": "0"}}}, "define.EditResaleItemPriorityReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "string", "example": "0"}, "priority": {"description": "优先级", "type": "integer", "maximum": 9999, "minimum": 1}}}, "define.EditResaleItemPriorityResp": {"type": "object", "properties": {"id": {"description": "转卖商品ID", "type": "string", "example": "0"}}}, "define.EditResaleItemReq": {"type": "object", "required": ["id", "is_custom_price_limit", "resale_status", "trade_frequency_type"], "properties": {"id": {"description": "ID", "type": "string", "example": "0"}, "is_custom_price_limit": {"description": "是否使用特定限价（0-关闭, 1-开启）", "type": "integer"}, "max_resale_ratio": {"description": "最高售价比例（基于历史最高成交价）", "type": "integer"}, "min_resale_ratio": {"description": "最低售价百分比（基于市场公允进价）", "type": "integer"}, "resale_status": {"description": "转卖状态（0-关闭, 1-开启）", "type": "integer"}, "trade_frequency_type": {"description": "交易频率类型（1-同步全局标准, 2-特定交易频次）", "type": "integer"}, "trade_interval": {"description": "交易间隔（分）", "type": "integer"}}}, "define.EditResaleItemResp": {"type": "object", "properties": {"id": {"description": "转卖商品ID", "type": "string", "example": "0"}}}, "define.EditResaleItemStatusReq": {"type": "object", "required": ["id", "status"], "properties": {"id": {"description": "ID", "type": "string", "example": "0"}, "status": {"description": "状态（0=待上架，1=已上架，2=已下架）", "type": "integer", "minimum": 0}}}, "define.EditResaleItemStatusResp": {"type": "object", "properties": {"id": {"description": "转卖商品ID", "type": "string", "example": "0"}}}, "define.GetAdminResaleListingsDetailData": {"type": "object", "properties": {"item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "listing_quantity": {"description": "挂单数量", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "sku_id": {"description": "商品sku_id", "type": "string"}, "sold_quantity": {"description": "已成交数量", "type": "integer"}, "total_fee": {"description": "手续费", "type": "integer"}, "total_income": {"description": "卖家收入（分）", "type": "integer"}}}, "define.GetAdminResaleListingsDetailResp": {"type": "object", "properties": {"app_version": {"description": "版本号", "type": "string"}, "created_at": {"description": "创建时间/上架时间", "type": "string"}, "id": {"description": "出售单号", "type": "string", "example": "0"}, "list": {"description": "商品信息", "type": "array", "items": {"$ref": "#/definitions/define.GetAdminResaleListingsDetailData"}}, "operator": {"description": "最后操作人 id", "type": "string"}, "seller_id": {"description": "卖家 ID", "type": "string"}, "seller_nickname": {"description": "卖家昵称", "type": "string"}, "status": {"description": "挂单状态（0-已下架，1-出售中，2-已出售，3-已取消）", "type": "integer"}, "terminal": {"description": "终端", "type": "string"}}}, "define.GetAdminResaleListingsListData": {"type": "object", "properties": {"created_at": {"description": "创建时间/上架时间", "type": "string"}, "id": {"description": "出售单号", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图 URL", "type": "string"}, "item_id": {"description": "商品 id", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "listing_quantity": {"description": "挂单数量", "type": "integer"}, "sale_price": {"description": "出售单价（分）", "type": "integer"}, "seller_id": {"description": "卖家 id", "type": "string"}, "seller_nickname": {"description": "卖家昵称", "type": "string"}, "seller_phone": {"description": "卖家手机号", "type": "string"}, "sku_id": {"description": "SKUID", "type": "string"}, "sold_quantity": {"description": "成交数量", "type": "integer"}, "status": {"description": "出售状态状态（0-已下架，1-出售中，2-已出售，3-已取消）", "type": "integer"}, "terminal": {"description": "终端", "type": "string"}, "total_fee": {"description": "手续费", "type": "integer"}, "total_income": {"description": "卖家收入（分）", "type": "integer"}}}, "define.GetAdminResaleListingsListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetAdminResaleListingsListData"}}, "total": {"type": "integer"}}}, "define.GetAdminResaleOrderDetailData": {"type": "object", "properties": {"item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "pay_amount": {"description": "支付金额", "type": "integer"}, "quantity": {"description": "购买数量", "type": "integer"}, "resale_listings_id": {"description": "出售单号", "type": "string", "example": "0"}, "sale_price": {"description": "售价", "type": "integer"}, "sku_id": {"description": "商品sku_id", "type": "string"}, "total_amount": {"description": "总金额", "type": "integer"}, "total_fee": {"description": "手续费", "type": "integer"}}}, "define.GetAdminResaleOrderDetailResp": {"type": "object", "properties": {"app_version": {"description": "版本号", "type": "string"}, "buyer_id": {"description": "购买用户ID", "type": "string"}, "buyer_nickname": {"description": "购买用户昵称", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "finished_at": {"description": "交易完成时间", "type": "string"}, "id": {"description": "合同编号", "type": "string", "example": "0"}, "item_info": {"description": "商品信息", "allOf": [{"$ref": "#/definitions/define.GetAdminResaleOrderDetailData"}]}, "payment_at": {"description": "支付时间", "type": "string"}, "seller_id": {"description": "出售用户ID", "type": "string"}, "seller_nickname": {"description": "出售用户昵称", "type": "string"}, "status": {"description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "type": "integer"}, "terminal": {"description": "终端", "type": "string"}, "transfer_at": {"description": "转移时间", "type": "string"}}}, "define.GetAdminResaleOrderListData": {"type": "object", "properties": {"buyer_id": {"description": "购买用户ID", "type": "string"}, "buyer_nickname": {"description": "购买用户昵称", "type": "string"}, "buyer_phone": {"description": "购买用户手机号码", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "id": {"description": "合同编号", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "pay_amount": {"description": "支付金额", "type": "integer"}, "quantity": {"description": "购买数量", "type": "integer"}, "resale_listings_id": {"description": "出售单号", "type": "string", "example": "0"}, "sale_price": {"description": "售价", "type": "integer"}, "seller_id": {"description": "出售用户ID", "type": "string"}, "seller_nickname": {"description": "出售用户昵称", "type": "string"}, "seller_phone": {"description": "出售用户手机号码", "type": "string"}, "sku_id": {"description": "商品sku_id", "type": "string"}, "status": {"description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "type": "integer"}, "terminal": {"description": "终端", "type": "string"}, "total_amount": {"description": "总金额", "type": "integer"}, "total_fee": {"description": "手续费", "type": "integer"}}}, "define.GetAdminResaleOrderListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetAdminResaleOrderListData"}}, "total": {"type": "integer"}}}, "define.GetAdminTradeOrderData": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "freight_amount": {"description": "运费", "type": "integer"}, "id": {"description": "订单ID", "type": "string", "example": "0"}, "order_items": {"description": "订单子单列表", "type": "array", "items": {"$ref": "#/definitions/define.GetAdminTradeOrderItemData"}}, "order_status": {"description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）", "type": "integer"}, "pay_amount": {"description": "支付金额", "type": "integer"}, "total_amount": {"description": "总金额", "type": "integer"}, "user_id": {"description": "用户ID", "type": "string"}, "user_nickname": {"description": "用户昵称", "type": "string"}}}, "define.GetAdminTradeOrderDetailFreight": {"type": "object", "properties": {"delivered_at": {"description": "发货时间", "type": "string"}, "delivery_company": {"description": "快递公司", "type": "string"}, "delivery_number": {"description": "快递单号", "type": "string"}, "order_item_id": {"description": "订单子单ID", "type": "string", "example": "0"}, "records": {"description": "物流信息", "type": "array", "items": {"type": "integer"}}}}, "define.GetAdminTradeOrderDetailResp": {"type": "object", "properties": {"cancel_at": {"description": "取消时间", "type": "string"}, "cancel_type": {"description": "取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）", "type": "integer"}, "consignee_address": {"description": "收货地址", "type": "array", "items": {"type": "integer"}}, "consignee_name": {"description": "收货人姓名", "type": "string"}, "consignee_phone": {"description": "收货人电话", "type": "string"}, "created_at": {"description": "下单时间", "type": "string"}, "delivered_at": {"description": "发货时间", "type": "string"}, "discount_amount": {"description": "折扣金额", "type": "integer"}, "finished_at": {"description": "完成时间", "type": "string"}, "freight_amount": {"description": "运费", "type": "integer"}, "freight_infos": {"description": "物流信息", "type": "array", "items": {"$ref": "#/definitions/define.GetAdminTradeOrderDetailFreight"}}, "id": {"description": "订单ID", "type": "string", "example": "0"}, "order_items": {"description": "物品列表", "type": "array", "items": {"$ref": "#/definitions/define.GetAdminTradeOrderItemData"}}, "order_status": {"description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）", "type": "integer"}, "order_type": {"description": "订单类型（1=直购订单）", "type": "integer"}, "pay_amount": {"description": "支付金额", "type": "integer"}, "pay_order_id": {"description": "支付订单ID", "type": "string"}, "payment_method": {"description": "支付方式", "type": "string"}, "payment_status": {"description": "支付状态（0=未支付，1=已支付，2=退款，3=全额退款）", "type": "integer"}, "payment_time": {"description": "支付时间", "type": "string"}, "shipping_status": {"description": "发货状态（0=未发货，1=已发货，2=已签收）", "type": "integer"}, "supply_chain_order_id": {"description": "供应链ID", "type": "string", "example": ""}, "terminal": {"description": "订单渠道", "type": "string"}, "total_amount": {"description": "总金额", "type": "integer"}, "user_id": {"description": "用户ID", "type": "string"}, "user_nickname": {"description": "用户昵称", "type": "string"}, "user_remark": {"description": "用户备注", "type": "string"}}}, "define.GetAdminTradeOrderItemData": {"type": "object", "properties": {"category_id": {"description": "商品分类id", "type": "string"}, "category_name": {"description": "商品分类名称", "type": "string"}, "discount_amount": {"description": "商品享受的优惠金额", "type": "integer"}, "freight_amount": {"description": "运费", "type": "integer"}, "id": {"description": "直购商品ID", "type": "string", "example": "0"}, "ip_id": {"description": "商品 ip_id", "type": "string"}, "ip_name": {"description": "商品IP名称", "type": "string"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品属性", "type": "string"}, "quantity": {"description": "购买数量", "type": "integer"}, "sale_price": {"description": "单价", "type": "integer"}, "sku_id": {"description": "商品 sku_id", "type": "string"}, "trademark_id": {"description": "商品品牌id", "type": "string"}, "trademark_name": {"description": "商品品牌名称", "type": "string"}}}, "define.GetAdminTradeOrderListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetAdminTradeOrderData"}}, "total": {"type": "integer"}}}, "define.GetConfigDetailResp": {"type": "object", "properties": {"config_key": {"description": "config_key", "type": "string"}, "id": {"description": "主键", "type": "string", "example": "0"}, "remark": {"description": "备注", "type": "string"}, "value": {"description": "value"}}}, "define.GetConfigListResp": {"type": "object", "properties": {"config_list": {"type": "array", "items": {"$ref": "#/definitions/define.Config"}}}}, "define.GetTradeOrderDetailResp": {"type": "object", "properties": {"freight_amount": {"description": "运费金额(单位:分)", "type": "integer"}, "pay_amount": {"description": "支付金额(单位:分)", "type": "integer"}, "payment_status": {"description": "支付状态（0=未支付，1=已支付，2=退款，3=全额退款）", "type": "integer"}}}, "define.GetWebPublishedResaleListingsListData": {"type": "object", "properties": {"id": {"description": "主键", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "min_price": {"description": "最低在售", "type": "integer"}, "sell_price": {"description": "出售单价（分）", "type": "integer"}, "stock_quantity": {"description": "预卖出数量", "type": "integer"}}}, "define.GetWebPublishedResaleListingsListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebPublishedResaleListingsListData"}}, "total": {"type": "integer"}}}, "define.GetWebResaleListingsItemOnSaleListData": {"type": "object", "properties": {"id": {"description": "主键", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "on_sale_qty": {"description": "在售数量", "type": "integer"}, "sell_price": {"description": "出售单价（分）", "type": "integer"}, "seller_avatar": {"description": "卖家头像", "type": "string"}, "seller_id": {"description": "卖家 id", "type": "string"}, "seller_nickname": {"description": "卖家昵称", "type": "string"}, "seller_total_sold_qty": {"description": "卖家总共售出数量（不限当前商品）", "type": "integer"}}}, "define.GetWebResaleListingsItemOnSaleListResp": {"type": "object", "properties": {"list": {"description": "列表数据", "type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleListingsItemOnSaleListData"}}, "total": {"description": "列表总数", "type": "integer"}, "total_on_sale_qty": {"description": "当前商品所有在售数量", "type": "integer"}}}, "define.GetWebResaleListingsItemSettlementListInfo": {"type": "object", "properties": {"on_sale_qty": {"description": "在售数量", "type": "integer"}, "sale_price": {"description": "出售单价（分）", "type": "integer"}, "selected_qty": {"description": "选择中数量", "type": "integer"}, "selected_resale_listings_list": {"description": "选中的挂单列表", "type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleListingsItemSettlementListSelectedItem"}}, "seller_avatar": {"description": "卖家头像", "type": "string"}, "seller_id": {"description": "卖家 id", "type": "string"}, "seller_nickname": {"description": "卖家昵称", "type": "string"}}}, "define.GetWebResaleListingsItemSettlementListResp": {"type": "object", "properties": {"list": {"description": "列表数据", "type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleListingsItemSettlementListInfo"}}, "total_amount": {"description": "合计价格（分）", "type": "integer"}}}, "define.GetWebResaleListingsItemSettlementListSelectedItem": {"type": "object", "properties": {"resale_listings_id": {"description": "转卖挂单主单 id", "type": "string", "example": "0"}, "resale_listings_item_ids": {"description": "挂单物品 id 列表", "type": "array", "items": {"type": "string"}}}}, "define.GetWebResaleOrderBuyDetailData": {"type": "object", "properties": {"item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "pay_amount": {"description": "支付金额", "type": "integer"}, "quantity": {"description": "购买数量", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "sold_out": {"description": "商品转卖和直购都下架状态 0=正常，1=下架", "type": "integer"}, "total_amount": {"description": "总金额", "type": "integer"}}}, "define.GetWebResaleOrderBuyDetailResp": {"type": "object", "properties": {"buyer_id": {"description": "购买用户ID", "type": "string"}, "buyer_nickname": {"description": "购买用户昵称", "type": "string"}, "buyer_real_name": {"description": "购买用户真实名称", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "finished_at": {"description": "交易完成时间", "type": "string"}, "id": {"description": "合同编号", "type": "string", "example": "0"}, "item_info": {"description": "商品信息", "allOf": [{"$ref": "#/definitions/define.GetWebResaleOrderBuyDetailData"}]}, "item_name": {"description": "商品名称", "type": "string"}, "payment_at": {"description": "支付时间", "type": "string"}, "quantity": {"description": "购买数量", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "seller_id": {"description": "出售用户ID", "type": "string"}, "seller_nickname": {"description": "出售用户昵称", "type": "string"}, "seller_real_name": {"description": "出售用户真实名称", "type": "string"}, "status": {"description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "type": "integer"}, "total_amount": {"description": "总金额", "type": "integer"}, "total_fee": {"description": "手续费", "type": "integer"}}}, "define.GetWebResaleOrderBuyListData": {"type": "object", "properties": {"id": {"description": "主键", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "quantity": {"description": "购买数量", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "seller_avatar": {"description": "出售用户头像", "type": "string"}, "seller_nickname": {"description": "出售用户昵称", "type": "string"}, "sold_out": {"description": "商品转卖和直购都下架状态 0=正常，1=下架", "type": "integer"}, "status": {"description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "type": "integer"}, "total_amount": {"description": "总金额", "type": "integer"}}}, "define.GetWebResaleOrderBuyListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleOrderBuyListData"}}, "total": {"type": "integer"}}}, "define.GetWebResaleOrderRecentListData": {"type": "object", "properties": {"buyer_avatar": {"description": "购买用户头像", "type": "string"}, "buyer_nickname": {"description": "购买用户昵称", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "quantity": {"description": "购买数量", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}}}, "define.GetWebResaleOrderRecentListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleOrderRecentListData"}}, "total": {"type": "integer"}}}, "define.GetWebResaleOrderSaleDetailData": {"type": "object", "properties": {"item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "quantity": {"description": "购买数量", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "seller_income_amount": {"description": "卖方收入", "type": "integer"}, "sold_out": {"description": "下架状态 0=正常，1=下架", "type": "integer"}, "total_amount": {"description": "总金额", "type": "integer"}}}, "define.GetWebResaleOrderSaleDetailResp": {"type": "object", "properties": {"buyer_id": {"description": "购买用户ID", "type": "string"}, "buyer_nickname": {"description": "购买用户昵称", "type": "string"}, "buyer_real_name": {"description": "购买用户真实名称", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "finished_at": {"description": "交易完成时间", "type": "string"}, "id": {"description": "合同编号", "type": "string", "example": "0"}, "item_info": {"description": "商品信息", "allOf": [{"$ref": "#/definitions/define.GetWebResaleOrderSaleDetailData"}]}, "item_name": {"description": "商品名称", "type": "string"}, "payment_at": {"description": "支付时间", "type": "string"}, "quantity": {"description": "购买数量", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "seller_id": {"description": "出售用户ID", "type": "string"}, "seller_income_amount": {"description": "卖方收入", "type": "integer"}, "seller_nickname": {"description": "出售用户昵称", "type": "string"}, "seller_real_name": {"description": "出售用户真实名称", "type": "string"}, "status": {"description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "type": "integer"}, "total_amount": {"description": "总金额", "type": "integer"}, "total_fee": {"description": "手续费", "type": "integer"}}}, "define.GetWebResaleOrderSaleListData": {"type": "object", "properties": {"buyer_avatar": {"description": "购买用户头像", "type": "string"}, "buyer_nickname": {"description": "购买用户昵称", "type": "string"}, "id": {"description": "主键", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "quantity": {"description": "购买数量", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "sold_out": {"description": "商品转卖和直购都下架状态 0=正常，1=下架", "type": "integer"}, "status": {"description": "订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）", "type": "integer"}, "total_amount": {"description": "总金额", "type": "integer"}}}, "define.GetWebResaleOrderSaleListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleOrderSaleListData"}}, "total": {"type": "integer"}}}, "define.GetWebResaleUserItemListByItemData": {"type": "object", "properties": {"hold_qty": {"description": "持有数量", "type": "integer"}, "id": {"description": "主键", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品 id", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "resale_status": {"description": "转卖状态（0-关闭, 1-开启）", "type": "integer"}, "sold_out": {"description": "商品转卖和直购都下架状态 0=正常，1=下架", "type": "integer"}}}, "define.GetWebResaleUserItemListByItemResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleUserItemListByItemData"}}, "total": {"type": "integer"}}}, "define.GetWebResaleUserItemListByUserItemData": {"type": "object", "properties": {"buy_price": {"description": "买入价格（分），为 0 则展示：外部存入", "type": "integer"}, "buy_time": {"description": "买入时间/物品获得时间", "type": "string"}, "countdown_second": {"description": "倒计时秒数", "type": "integer"}, "delivery_time": {"description": "提货时间", "type": "string"}, "id": {"description": "持仓 id", "type": "string"}, "interval_minutes": {"description": "转卖时间间隔（单位：分钟）", "type": "integer"}, "item_icon_url": {"description": "商品图片", "type": "string"}, "item_id": {"description": "商品 id", "type": "string"}, "pre_sale": {"description": "是否预售", "type": "boolean"}, "resale_listings_item_id": {"description": "转卖挂单商品 id", "type": "string", "example": "0"}, "resale_status": {"description": "转卖状态（0-关闭, 1-开启）", "type": "integer"}, "sale_price": {"description": "出售单价（分），状态为出售中时展示", "type": "integer"}, "status": {"description": "状态，1：持有中，100：出售中", "type": "integer"}}}, "define.GetWebResaleUserItemListByUserItemResp": {"type": "object", "properties": {"has_more": {"type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebResaleUserItemListByUserItemData"}}}}, "define.GetWebResaleUserItemTradeInfoResp": {"type": "object", "properties": {"available_qty": {"description": "当前用户可出售的物品数量", "type": "integer"}, "fee_rate": {"description": "服务费百分比，比如 3% 就返回 3", "type": "integer"}, "item_icon_url": {"description": "商品图片", "type": "string"}, "item_id": {"description": "商品 id", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "latest_sale_price": {"description": "最新成交价（分），为 -1 表示暂无挂单", "type": "integer"}, "max_limit_price": {"description": "最高限价（分）", "type": "integer"}, "min_limit_price": {"description": "最低限价（分）", "type": "integer"}, "min_sale_price": {"description": "最低在售价（分），为 -1 表示暂无挂单", "type": "integer"}}}, "define.GetWebTradeOrderData": {"type": "object", "properties": {"countdown_second": {"description": "倒计时秒数", "type": "integer"}, "freight_info": {"description": "物流信息", "type": "array", "items": {"$ref": "#/definitions/define.GetWebTradeOrderDetailFreight"}}, "id": {"description": "id", "type": "string", "example": "0"}, "order_items": {"description": "物品列表", "type": "array", "items": {"$ref": "#/definitions/define.GetWebTradeOrderItemData"}}, "order_status": {"description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）", "type": "integer"}, "pay_amount": {"description": "支付金额", "type": "integer"}, "payment_status": {"description": "支付状态（0=未支付，1=已支付，2=退款，3=全额退款）", "type": "integer"}, "shipping_status": {"description": "发货状态（0=未发货，1=已发货，2=已签收）", "type": "integer"}, "total_amount": {"description": "总金额", "type": "integer"}}}, "define.GetWebTradeOrderDetailFreight": {"type": "object", "properties": {"delivery_company": {"description": "快递公司", "type": "string"}, "delivery_number": {"description": "快递单号", "type": "string"}}}, "define.GetWebTradeOrderDetailResp": {"type": "object", "properties": {"cancel_type": {"description": "取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）", "type": "integer"}, "consignee_address": {"description": "收货地址", "type": "array", "items": {"type": "integer"}}, "consignee_name": {"description": "收货人姓名", "type": "string"}, "consignee_phone": {"description": "收货人电话", "type": "string"}, "countdown_second": {"description": "倒计时秒数", "type": "integer"}, "created_at": {"description": "下单时间", "type": "string"}, "discount_amount": {"description": "折扣金额", "type": "integer"}, "finished_second": {"description": "订单完成倒计时秒数", "type": "integer"}, "freight_amount": {"description": "运费", "type": "integer"}, "freight_info": {"description": "物流信息", "type": "array", "items": {"$ref": "#/definitions/define.GetWebTradeOrderDetailFreight"}}, "id": {"description": "id", "type": "string", "example": "0"}, "order_items": {"description": "物品列表", "type": "array", "items": {"$ref": "#/definitions/define.GetWebTradeOrderItemData"}}, "order_status": {"description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）", "type": "integer"}, "pay_amount": {"description": "支付金额", "type": "integer"}, "payment_method": {"description": "支付方式", "type": "string"}, "payment_status": {"description": "支付状态（0=未支付，1=已支付，2=退款，3=全额退款）", "type": "integer"}, "payment_time": {"description": "支付时间", "type": "string"}, "shipping_status": {"description": "发货状态（0=未发货，1=已发货，2=已签收）", "type": "integer"}, "total_amount": {"description": "总金额", "type": "integer"}, "user_remark": {"description": "用户备注", "type": "string"}}}, "define.GetWebTradeOrderFreightInfoResp": {"type": "object", "properties": {"checked": {"description": "0未签收 1已签收", "type": "integer"}, "delivery_company": {"description": "快递公司", "type": "string"}, "delivery_number": {"description": "快递单号", "type": "string"}, "records": {"type": "array", "items": {"type": "integer"}}, "status": {"type": "integer"}}}, "define.GetWebTradeOrderItemData": {"type": "object", "properties": {"id": {"description": "直购商品ID", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品属性", "type": "string"}, "quantity": {"description": "购买数量", "type": "integer"}, "sale_price": {"description": "单价", "type": "integer"}}}, "define.GetWebTradeOrderListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebTradeOrderData"}}, "total": {"type": "integer"}}}, "define.GetWebTradeOrderStatusStatData": {"type": "object", "properties": {"count": {"type": "integer"}, "order_status": {"description": "订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）", "type": "integer"}}}, "define.GetWebTradeOrderStatusStatResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/define.GetWebTradeOrderStatusStatData"}}}}, "define.LockedHandlerReq": {"type": "object", "properties": {"id": {"description": "id", "type": "string", "example": "0"}}}, "define.LockedHandlerResp": {"type": "object"}, "define.MallInstruction": {"type": "object", "properties": {"banner_url": {"type": "string"}, "content": {"type": "string"}}}, "define.MallItemDetailResp": {"type": "object", "properties": {"available_stock": {"description": "直购商品可用库存", "type": "integer"}, "category_id": {"description": "商品分类id", "type": "string"}, "created_at": {"description": "创建时间", "type": "string"}, "daily_limit": {"description": "每日限购数量", "type": "integer"}, "discount": {"description": "折扣", "type": "integer"}, "freight": {"description": "运费(分)", "type": "integer"}, "id": {"description": "主键", "type": "string", "example": "0"}, "ipid": {"description": "商品 ip_id", "type": "string"}, "item_id": {"description": "商品 id", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "priority": {"description": "优先级", "type": "integer"}, "purchase_price": {"description": "进价", "type": "integer"}, "sale_price": {"description": "售价(分)", "type": "integer"}, "sell_listings": {"description": "商品库存", "type": "integer"}, "sku_id": {"description": "商品 sku_id", "type": "string"}, "spu_id": {"description": "商品 spu_id", "type": "string"}, "start_time": {"description": "开始展示时间", "type": "string"}, "status": {"description": "状态（0=待上架，1=已上架，2=已下架）", "type": "integer"}, "stock": {"description": "限定库存量", "type": "integer"}, "stock_type": {"description": "库存类型（1=限定库存，2=同步商品库存）", "type": "integer"}, "total_limit": {"description": "总限购数量", "type": "integer"}, "trademark_id": {"description": "商品品牌id", "type": "string"}}}, "define.MallItemPageData": {"type": "object", "properties": {"available_stock": {"description": "剩余库存", "type": "integer"}, "category_id": {"description": "商品分类ID", "type": "string"}, "category_name": {"description": "商品分类名称", "type": "string"}, "discount": {"description": "折扣", "type": "integer"}, "discount_price": {"description": "折扣价", "type": "integer"}, "freight": {"description": "运费", "type": "integer"}, "id": {"description": "主键", "type": "string", "example": "0"}, "ip_id": {"description": "IP ID", "type": "string"}, "ip_name": {"description": "IP名称", "type": "string"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品 ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "规格", "type": "string"}, "priority": {"description": "优先级", "type": "integer"}, "purchase_price": {"description": "进价", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "sku_id": {"description": "SKU ID", "type": "string"}, "spu_id": {"description": "SPU ID", "type": "string"}, "start_time": {"description": "开始展示时间", "type": "string"}, "status": {"description": "状态（0=待上架，1=已上架，2=已下架）", "type": "integer"}, "stock": {"description": "销售库存", "type": "integer"}, "trademark_id": {"description": "商品品牌ID", "type": "string"}, "trademark_name": {"description": "商品品牌名称", "type": "string"}, "updated_by": {"description": "最后操作人", "type": "string"}}}, "define.MallItemPageResp": {"type": "object", "properties": {"list": {"description": "列表", "type": "array", "items": {"$ref": "#/definitions/define.MallItemPageData"}}, "total": {"description": "总数", "type": "integer"}}}, "define.OpenSyncViewCountToDBReq": {"type": "object"}, "define.OpenSyncViewCountToDBResp": {"type": "object"}, "define.OpenTriggerDisplayByStartTimeReq": {"type": "object"}, "define.OpenUpdateMallItemNotifyReq": {"type": "object", "required": ["item_ids"], "properties": {"item_ids": {"type": "array", "items": {"type": "string"}}}}, "define.RefreshHomeFeedReq": {"type": "object", "properties": {"item_ids": {"description": "商品ID", "type": "array", "items": {"type": "string"}}}}, "define.RefreshHomeFeedResp": {"type": "object"}, "define.ReleaseUserItemsReq": {"type": "object", "properties": {"id": {"description": "id", "type": "string", "example": "0"}}}, "define.ReleaseUserItemsResp": {"type": "object"}, "define.ResaleInstruction": {"type": "object", "properties": {"banner_url": {"type": "string"}, "content": {"type": "string"}}}, "define.ResaleItemBuyReq": {"type": "object", "required": ["password", "quantity", "resale_listings_item_info", "total_amount"], "properties": {"password": {"description": "密码", "type": "string"}, "quantity": {"description": "购买数量", "type": "integer", "minimum": 1}, "resale_listings_item_info": {"description": "挂单列表信息", "type": "array", "items": {"$ref": "#/definitions/define.ResaleListingsItemInfo"}}, "total_amount": {"description": "总金额", "type": "integer", "minimum": 1}}}, "define.ResaleItemBuyResp": {"type": "object", "properties": {"pay_success_order_ids": {"type": "array", "items": {"type": "integer"}}}}, "define.ResaleItemDetailResp": {"type": "object", "properties": {"category_id": {"description": "商品分类ID", "type": "string"}, "category_name": {"description": "商品分类名称", "type": "string"}, "detail_h5": {"description": "商品详情", "type": "string"}, "id": {"description": "转卖商品ID", "type": "string", "example": "0"}, "image_infos": {"description": "商品详情图片", "type": "array", "items": {"type": "string"}}, "ip_id": {"description": "IP ID", "type": "string"}, "ip_name": {"description": "IP名称", "type": "string"}, "is_custom_price_limit": {"description": "是否使用特定限价（0-关闭, 1-开启）", "type": "integer"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品 ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "规格", "type": "string"}, "market_price": {"description": "市场公允价（分）", "type": "integer"}, "max_resale_ratio": {"description": "最高售价比例（基于历史最高成交价）", "type": "integer"}, "min_resale_ratio": {"description": "最低售价百分比（基于市场公允进价）", "type": "integer"}, "resale_status": {"description": "转卖状态（0-关闭, 1-开启）", "type": "integer"}, "sell_listings": {"description": "商品库存", "type": "integer"}, "sku_id": {"description": "SKU ID", "type": "string"}, "trade_frequency_type": {"description": "交易频率类型（1-同步全局标准, 2-特定交易频次）", "type": "integer"}, "trade_interval": {"description": "交易间隔（分）", "type": "integer"}, "trademark_id": {"description": "商品品牌ID", "type": "string"}, "trademark_name": {"description": "商品品牌名称", "type": "string"}}}, "define.ResaleItemPageData": {"type": "object", "properties": {"category_id": {"description": "商品分类ID", "type": "string"}, "category_name": {"description": "商品分类名称", "type": "string"}, "id": {"description": "主键", "type": "string", "example": "0"}, "ip_id": {"description": "商品IP ID", "type": "string"}, "ip_name": {"description": "商品IP名称", "type": "string"}, "is_custom_price_limit": {"description": "是否使用特定限价（0-关闭, 1-开启）", "type": "integer"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品 ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "market_price": {"description": "市场公允价（分）", "type": "integer"}, "max_limit_price": {"description": "最高限价（分）", "type": "integer"}, "min_limit_price": {"description": "最低限价（分）", "type": "integer"}, "priority": {"description": "优先级", "type": "integer"}, "resale_status": {"description": "转卖状态（0-关闭, 1-开启）", "type": "integer"}, "sku_id": {"description": "商品 sku_id", "type": "string"}, "status": {"description": "状态（0=待上架，1=已上架，2=已下架）", "type": "integer"}, "trade_frequency_type": {"description": "交易频次类型（1-同步全局标准, 2-特定交易频次）", "type": "integer"}, "trademark_id": {"description": "商品品牌ID", "type": "string"}, "trademark_name": {"description": "商品品牌名称", "type": "string"}, "updated_at": {"description": "最后操作时间", "type": "string"}, "updated_by": {"description": "最后操作人", "type": "string"}}}, "define.ResaleItemPageResp": {"type": "object", "properties": {"list": {"description": "列表", "type": "array", "items": {"$ref": "#/definitions/define.ResaleItemPageData"}}, "total": {"description": "总数", "type": "integer"}}}, "define.ResaleItemTransferHandlerReq": {"type": "object", "properties": {"id": {"description": "id", "type": "string", "example": "0"}}}, "define.ResaleItemTransferHandlerResp": {"type": "object"}, "define.ResaleListingsItemInfo": {"type": "object", "properties": {"resale_listings_id": {"description": "转卖挂单id列表", "type": "string"}, "resale_listings_item_ids": {"description": "转卖挂单物品id列表", "type": "array", "items": {"type": "string"}}}}, "define.SupplyChainNoticeReq": {"type": "object", "required": ["type"], "properties": {"data": {"type": "array", "items": {"type": "object", "additionalProperties": true}}, "type": {"type": "integer"}}}, "define.SupplyChainNoticeResp": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "properties": {"failed": {"description": "失败的消息ID（请求参数中的\"mid\"）", "type": "array", "items": {"type": "integer"}}}}, "desc": {"type": "string"}}}, "define.SyncAdminTradeOrderFreightReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "订单详情ID", "type": "string", "example": "0"}}}, "define.SyncAdminTradeOrderFreightResp": {"type": "object"}, "define.TakeDownResaleListingsFromWebReq": {"type": "object", "required": ["id"], "properties": {"id": {"description": "主键", "type": "string", "example": "0"}}}, "define.TakeDownResaleListingsFromWebResp": {"type": "object", "properties": {"down_time": {"description": "下架时间", "type": "string"}, "id": {"description": "主键", "type": "string", "example": "0"}}}, "define.TradeOrderFailedRetryUploadToSupplyReq": {"type": "object"}, "define.TradeOrderFinishReq": {"type": "object"}, "define.TradeOrderFinishResp": {"type": "object"}, "define.TradeOrderPaySuccessReq": {"type": "object", "required": ["id", "recharge_order_id"], "properties": {"id": {"type": "string", "example": "0"}, "pay_method": {"type": "string"}, "pay_time": {"type": "string"}, "recharge_order_id": {"type": "string"}}}, "define.TradeOrderPaySuccessResp": {"type": "object"}, "define.TradeOrderRefundReq": {"type": "object", "required": ["amount", "recharge_order_id"], "properties": {"amount": {"type": "integer"}, "recharge_order_id": {"type": "string"}}}, "define.TradeOrderRefundResp": {"type": "object", "properties": {"amount": {"type": "integer"}}}, "define.TradeOrderTimeoutCloseReq": {"type": "object"}, "define.UpdateResaleListingsStatusResp": {"type": "object", "properties": {"id": {"type": "string", "example": "0"}, "status": {"type": "integer"}, "updated_at": {"type": "string"}}}, "define.UpdateTradeOrderAddressReq": {"type": "object", "required": ["address_id", "id"], "properties": {"address_id": {"description": "地址ID", "type": "string"}, "id": {"description": "订单id", "type": "string", "example": "0"}}}, "define.UpdateTradeOrderAddressResp": {"type": "object"}, "define.WebAddHomeFeedViewCountReq": {"type": "object", "required": ["item_id"], "properties": {"item_id": {"description": "商品ID", "type": "string"}}}, "define.WebAddHomeFeedViewCountResp": {"type": "object"}, "define.WebBuyReq": {"type": "object", "required": ["address_id", "mall_item_id", "pay_amount", "quantity"], "properties": {"address_id": {"description": "地址ID", "type": "string"}, "mall_item_id": {"description": "直购商品ID", "type": "string", "example": "0"}, "pay_amount": {"description": "支付金额", "type": "integer"}, "quantity": {"description": "购买数量", "type": "integer"}, "remark": {"description": "备注", "type": "string"}}}, "define.WebBuyResp": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "is_new_order": {"description": "是否是新订单", "type": "boolean"}, "order_id": {"description": "直购订单ID", "type": "string", "example": "0"}, "pay_amount": {"description": "应支付金额", "type": "integer"}}}, "define.WebGetHomeFeedDetailResp": {"type": "object", "properties": {"detail": {"description": "商品详情", "type": "string"}, "id": {"description": "首页主键", "type": "string", "example": "0"}, "image_infos": {"description": "商品图片", "type": "array", "items": {"type": "string"}}, "ip_icon_url": {"description": "商品IP图片", "type": "string"}, "ip_id": {"description": "商品 ip_id", "type": "string"}, "ip_name": {"description": "商品IP名称", "type": "string"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_mall_status": {"description": "商品直购状态", "type": "integer"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "resale_count": {"description": "转卖数量", "type": "integer"}, "resale_instruction": {"description": "转卖说明配置", "allOf": [{"$ref": "#/definitions/define.ResaleInstruction"}]}, "resale_status": {"description": "商品转卖状态", "type": "integer"}, "sale_price": {"description": "出售价格", "type": "integer"}, "sales_volume": {"description": "销量", "type": "integer"}, "status": {"description": "状态", "type": "integer"}, "trademark_id": {"description": "商品品牌id", "type": "string"}, "trademark_name": {"description": "商品品牌名称", "type": "string"}, "user_wishlist_status": {"description": "用户想要状态", "type": "integer"}, "view_count": {"description": "浏览数量", "type": "integer"}, "wish_count": {"description": "想要数量", "type": "integer"}}}, "define.WebGetHomeFeedListResp": {"type": "object", "properties": {"has_more": {"description": "判断当前页是否为最后一页", "type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/define.WebHomeFeedListData"}}}}, "define.WebGetMallItemDetailResp": {"type": "object", "properties": {"available_stock": {"description": "剩余库存", "type": "integer"}, "category_id": {"description": "商品分类id", "type": "string"}, "category_name": {"description": "商品分类名称", "type": "string"}, "detail": {"description": "商品详情", "type": "string"}, "discount": {"description": "折扣", "type": "integer"}, "discount_price": {"description": "折扣价", "type": "integer"}, "freight": {"description": "运费", "type": "integer"}, "id": {"description": "直购商品ID", "type": "string", "example": "0"}, "image_infos": {"description": "商品图片", "type": "array", "items": {"type": "string"}}, "ip_id": {"description": "商品 ip_id", "type": "string"}, "ip_name": {"description": "商品IP名称", "type": "string"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "mall_instruction": {"description": "直购说明配置", "allOf": [{"$ref": "#/definitions/define.MallInstruction"}]}, "sale_price": {"description": "售价", "type": "integer"}, "specs": {"description": "商品规格", "type": "string"}, "status": {"description": "直购商品状态（0=待上架，1=已上架，2=已下架）", "type": "integer"}, "stock": {"description": "总库存", "type": "integer"}, "trademark_id": {"description": "商品品牌id", "type": "string"}, "trademark_name": {"description": "商品品牌名称", "type": "string"}}}, "define.WebGetMallItemListResp": {"type": "object", "properties": {"has_more": {"description": "判断当前页是否为最后一页", "type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/define.WebMallItemListData"}}}}, "define.WebHomeFeedListData": {"type": "object", "properties": {"id": {"description": "首页主键", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_mall_status": {"description": "商品直购状态", "type": "integer"}, "item_name": {"description": "商品名称", "type": "string"}, "item_specs": {"description": "商品规格", "type": "string"}, "original_price": {"description": "闪购原价", "type": "integer"}, "priority": {"description": "优先级", "type": "integer"}, "resale_count": {"description": "转卖数量", "type": "integer"}, "resale_status": {"description": "商品转卖状态", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "sales_volume": {"description": "销量", "type": "integer"}, "status": {"description": "状态", "type": "integer"}, "wish_count": {"description": "想要数量", "type": "integer"}}}, "define.WebMallItemListData": {"type": "object", "properties": {"discount": {"description": "折扣", "type": "integer"}, "discount_price": {"description": "折扣价", "type": "integer"}, "id": {"description": "直购商品ID", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_name": {"description": "商品名称", "type": "string"}, "priority": {"description": "优先级", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "sales": {"description": "销量", "type": "integer"}, "wishlist_count": {"description": "想要数量", "type": "integer"}}}, "define.WebWishlistListData": {"type": "object", "properties": {"home_feed_id": {"description": "首页ID", "type": "integer"}, "home_feed_status": {"description": "首页状态", "type": "integer"}, "id": {"description": "用户想要ID", "type": "string", "example": "0"}, "item_icon_url": {"description": "商品主图", "type": "string"}, "item_id": {"description": "商品ID", "type": "string"}, "item_mall_status": {"description": "商品直购状态", "type": "integer"}, "item_name": {"description": "商品名称", "type": "string"}, "item_resale_status": {"description": "商品转卖状态", "type": "integer"}, "item_specs": {"description": "商品规格", "type": "string"}, "original_price": {"description": "闪购原价", "type": "integer"}, "priority": {"description": "优先级", "type": "integer"}, "resale_count": {"description": "转卖数量", "type": "integer"}, "sale_price": {"description": "售价", "type": "integer"}, "sales_volume": {"description": "销量", "type": "integer"}, "wish_count": {"description": "想要数量", "type": "integer"}}}, "define.WebWishlistListResp": {"type": "object", "properties": {"has_more": {"description": "判断当前页是否为最后一页", "type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/define.WebWishlistListData"}}}}, "define.WebWishlistReq": {"type": "object", "required": ["item_id"], "properties": {"item_id": {"type": "string"}}}, "response.Data": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "desc": {"type": "string"}, "trace_id": {"description": "链路id", "type": "string"}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}