package kafka_util

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"marketplace_service/global"
	"marketplace_service/pkg/utils"
)

func SendMsg(ctx context.Context, topic string, data any) error {
	msg := &broker.Message{
		Body: []byte(utils.Obj2JsonStr(data)),
	}
	err := global.BROKER.Publish(topic, msg)
	if err != nil {
		log.Ctx(ctx).Errorf("Kafka Publish topic[%s], data[%s] err: %v", topic, utils.Obj2JsonStr(data), err)
		return err
	}
	return nil
}
