package utils

import (
	"github.com/duke-git/lancet/v2/datetime"
	"time"
)

const (
	DateTimeMsFormat = "2006-01-02 15:04:05.000"
	DateTimeFormat   = "2006-01-02 15:04:05"
	DateFormat       = "2006-01-02"
)

func GetNowDate() string {
	return GetDateFormatStr(time.Now())
}

func GetDateFormatStr(date time.Time) string {
	location, _ := time.LoadLocation("Asia/Shanghai")
	return date.In(location).Format(DateFormat)
}

func GetDateTimeFormatStr(date time.Time) string {
	location, _ := time.LoadLocation("Asia/Shanghai")
	return date.In(location).Format(DateTimeFormat)
}
func GetDateTimeMsFormatStr(date time.Time) string {
	location, _ := time.LoadLocation("Asia/Shanghai")
	return date.In(location).Format(DateTimeMsFormat)
}

func GetDateTime(date time.Time) time.Time {
	location, _ := time.LoadLocation("Asia/Shanghai")
	return date.In(location)
}

func Now() time.Time {
	location, _ := time.LoadLocation("Asia/Shanghai")
	return time.Now().In(location)
}

// GetDateFormat 时间格式化
func GetDateFormat(date string) time.Time {
	var timeTemplates = []string{
		"2006-01-02 15:04:05",
		"2006/01/02 15:04:05",
		"2006-01-02",
		"2006/01/02",
		"20060102",
	}
	for i := range timeTemplates {
		t, err := time.ParseInLocation(timeTemplates[i], date, time.Local)
		if nil == err && !t.IsZero() {
			return t
		}
	}
	return time.Time{}
}

// CalculateNaturalDaysDifference 计算给定日期与今天之间的自然日天数差异
func CalculateNaturalDaysDifference(givenDate time.Time) int32 {
	// 获取当前时间的日期（忽略时间）
	location, _ := time.LoadLocation("Asia/Shanghai")
	today := datetime.BeginOfDay(time.Now().In(location))

	// 将给定日期也转换为当天的开始时间
	givenDate = datetime.BeginOfDay(givenDate.In(location))
	// 计算两个日期之间的差异
	duration := today.Sub(givenDate)

	// 将时间差转换为天数，结果始终向下取整
	days := int32(duration.Hours() / 24)

	return days
}

// GetStartOfDay returns the start time of the current day
func GetStartOfDay(now time.Time) time.Time {
	year, month, day := now.Date()
	location := now.Location()
	startOfDay := time.Date(year, month, day, 0, 0, 0, 0, location)
	return startOfDay
}

// GetEndOfDay returns the end time of the current day
func GetEndOfDay(now time.Time) time.Time {
	year, month, day := now.Date()
	location := now.Location()
	endOfDay := time.Date(year, month, day, 23, 59, 59, int(time.Second-time.Nanosecond), location)
	return endOfDay
}

// TimeIsBetween 判断给定时间是否在某个时间区间内（闭区间）
func TimeIsBetween(t, start, end time.Time) bool {
	return (t.Equal(start) || t.After(start)) && (t.Equal(end) || t.Before(end))
}

// OffsetDay 获取指定日期偏移天数后的时间 @param now 当前时间 @param day 偏移天数(正数表示向后偏移，负数表示向前偏移) @return 偏移后的时间
func OffsetDay(now time.Time, day int32) time.Time {
	return now.AddDate(0, 0, int(day))
}

// TimeToStr 时间格式化
func TimeToStr(now time.Time, temp string) string {
	if temp == "" {
		temp = "2006-01-02 15:04:05"
	}
	loc, _ := time.LoadLocation("Asia/Shanghai")
	// 解析时间戳
	t := now.In(loc)
	// 格式化
	return t.Format(temp)
}

// TimeToUtcStr 时间UTC格式化
func TimeToUtcStr(now time.Time, temp string) string {
	if temp == "" {
		temp = "2006-01-02 15:04:05"
	}
	loc, _ := time.LoadLocation("UTC")
	// 解析时间戳
	t := now.In(loc)
	// 格式化
	return t.Format(temp)
}

func GetBeijingStartOfDay(dateTime *time.Time) (date *time.Time) {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	timeUTC8 := dateTime.In(loc)
	year, month, day := timeUTC8.Date()
	startOfDay := time.Date(year, month, day, 0, 0, 0, 0, loc)
	return &startOfDay
}

// GetSecondsUntilEndOfDay 返回当前时间到当天结束的剩余秒数
func GetSecondsUntilEndOfDay() (int64, error) {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return 0, err
	}
	now := time.Now().In(loc)
	todayEndTime := datetime.EndOfDay(now)
	keyExpireSec := todayEndTime.Unix() - now.Unix()
	return keyExpireSec, nil
}
