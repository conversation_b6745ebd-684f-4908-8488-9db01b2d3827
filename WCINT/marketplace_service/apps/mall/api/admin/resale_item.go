package admin

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// AddResaleItem 创建转卖商品
// @Summary 创建转卖商品
// @Description 管理端创建转卖商品
// @Tags 管理端-转卖商品管理
// @x-apifox-folder "管理端/转卖商品管理"
// @Param data body define.AddResaleItemReq true "商品信息"
// @Success 200 {object} response.Data{data=define.AddResaleItemResp}
// @Router /admin/v1/resale_item/add [post]
// @Security Bearer
// @produce  json
func AddResaleItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddResaleItemReq{}, s.AddResaleItem)
}

// EditResaleItem 更新转卖商品
// @Summary 更新转卖商品
// @Description 管理端更新商品信息
// @Tags 管理端-转卖商品管理
// @x-apifox-folder "管理端/转卖商品管理"
// @Param data body define.EditResaleItemReq true "更新参数"
// @Success 200 {object} response.Data{data=define.EditResaleItemResp}
// @Router /admin/v1/resale_item/edit [post]
// @Security Bearer
// @produce  json
func EditResaleItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditResaleItemReq{}, s.EditResaleItem)
}

// EditResaleItemPriority 更新转卖商品优先级
// @Summary 更新转卖商品优先级
// @Description 管理端更新转卖商品优先级
// @Tags 管理端-转卖商品管理
// @x-apifox-folder "管理端/转卖商品管理"
// @Param data body define.EditResaleItemPriorityReq true "更新参数"
// @Success 200 {object} response.Data{data=define.EditResaleItemPriorityResp}
// @Router /admin/v1/resale_item/edit_priority [post]
// @Security Bearer
// @produce  json
func EditResaleItemPriority(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditResaleItemPriorityReq{}, s.EditResaleItemPriority)
}

// EditResaleItemStatus 更新转卖商品状态
// @Summary 更新转卖商品状态
// @Description 管理端更新转卖商品状态
// @Tags 管理端-转卖商品管理
// @x-apifox-folder "管理端/转卖商品管理"
// @Param data body define.EditResaleItemStatusReq true "更新参数"
// @Success 200 {object} response.Data{data=define.EditResaleItemStatusResp}
// @Router /admin/v1/resale_item/edit_status [post]
// @Security Bearer
// @produce  json
func EditResaleItemStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditResaleItemStatusReq{}, s.EditResaleItemStatus)
}

// GetResaleItemList 获取转卖商品列表
// @Summary 获取转卖商品列表
// @Description 管理端获取转卖商品分页列表
// @Tags 管理端-转卖商品管理
// @x-apifox-folder "管理端/转卖商品管理"
// @Param data query define.ResaleItemPageReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.ResaleItemPageResp}
// @Router /admin/v1/resale_item/list [get]
// @Security Bearer
// @produce  json
func GetResaleItemList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ResaleItemPageReq{}, s.GetResaleItemList)
}

// GetResaleItemDetail 获取转卖商品详情
// @Summary 获取转卖商品详情
// @Description 管理端获取转卖商品详细信息
// @Tags 管理端-转卖商品管理
// @x-apifox-folder "管理端/转卖商品管理"
// @Param data query define.ResaleItemDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.ResaleItemDetailResp}
// @Router /admin/v1/resale_item/detail [get]
// @Security Bearer
// @produce  json
func GetResaleItemDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ResaleItemDetailReq{}, s.GetResaleItemDetail)
}
