package admin

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	_ "e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetMallItemList 获取商品列表
// @Summary 获取商品列表
// @Description 管理端获取商品分页列表
// @Tags 管理端-直购商品管理
// @x-apifox-folder "管理端/直购商品管理"
// @Param data query define.MallItemPageReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.MallItemPageResp}
// @Router /admin/v1/mall_item/list [get]
// @Security Bearer
// @produce  json
func GetMallItemList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.MallItemPageReq{}, s.GetMallItemList)
}

// GetMallItemDetail 获取商品详情
// @Summary 获取商品详情
// @Description 管理端获取商品详细信息
// @Tags 管理端-直购商品管理
// @x-apifox-folder "管理端/直购商品管理"
// @Param data query define.MallItemDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.MallItemDetailResp}
// @Router /admin/v1/mall_item/detail [get]
// @Security Bearer
// @produce  json
func GetMallItemDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.MallItemDetailReq{}, s.GetMallItemDetail)
}

// AddMallItem 创建商品
// @Summary 创建商品
// @Description 管理端创建新商品
// @Tags 管理端-直购商品管理
// @x-apifox-folder "管理端/直购商品管理"
// @Param data body define.AddMallItemReq true "商品信息"
// @Success 200 {object} response.Data{data=define.AddMallItemResp}
// @Router /admin/v1/mall_item/add [post]
// @Security Bearer
// @produce  json
func AddMallItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddMallItemReq{}, s.AddMallItem)
}

// EditMallItem 更新商品
// @Summary 更新商品
// @Description 管理端更新商品信息
// @Tags 管理端-直购商品管理
// @x-apifox-folder "管理端/直购商品管理"
// @Param data body define.EditMallItemReq true "更新参数"
// @Success 200 {object} response.Data{data=define.EditMallItemResp}
// @Router /admin/v1/mall_item/edit [post]
// @Security Bearer
// @produce  json
func EditMallItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditMallItemReq{}, s.EditMallItem)
}

// EditMallItemPriority 更新商品优先级
// @Summary 更新商品优先级
// @Description 管理端更新商品优先级
// @Tags 管理端-直购商品管理
// @x-apifox-folder "管理端/直购商品管理"
// @Param data body define.EditMallItemPriorityReq true "更新参数"
// @Success 200 {object} response.Data{data=define.EditMallItemPriorityResp}
// @Router /admin/v1/mall_item/edit_priority [post]
// @Security Bearer
// @produce  json
func EditMallItemPriority(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditMallItemPriorityReq{}, s.EditMallItemPriority)
}

// EditMallItemStatus 更新商品状态
// @Summary 更新商品状态
// @Description 管理端更新商品信息
// @Tags 管理端-直购商品管理
// @x-apifox-folder "管理端/直购商品管理"
// @Param data body define.EditMallItemStatusReq true "更新参数"
// @Success 200 {object} response.Data{data=define.EditMallItemStatusResp}
// @Router /admin/v1/mall_item/edit_status [post]
// @Security Bearer
// @produce  json
func EditMallItemStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditMallItemStatusReq{}, s.EditMallItemStatus)
}
