package admin

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// TradeOrderRefund
// @Summary 订单发起退款
// @Description 订单发起退款
// @Tags 管理端-直购订单管理
// @x-apifox-folder "管理端/直购订单管理"
// @Param data body define.TradeOrderRefundReq true "请求参数"
// @Success 200 {object} response.Data{data=define.TradeOrderRefundResp}
// @Router /admin/v1/trade_order/refund [post]
// @Security Bearer
// @produce  json
func TradeOrderRefund(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.TradeOrderRefundReq{}, s.TradeOrderRefund)
}

// GetTradeOrderList 获取订单列表
// @Summary 获取订单列表
// @Description 管理端获取订单分页列表
// @Tags 管理端-直购订单管理
// @x-apifox-folder "管理端/直购订单管理"
// @Param data query define.GetAdminTradeOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetAdminTradeOrderListResp}
// @Router /admin/v1/trade_order/list [get]
// @Security Bearer
// @produce  json
func GetTradeOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAdminTradeOrderListReq{}, s.GetAdminTradeOrderList)
}

// GetTradeOrderDetail 获取订单详情
// @Summary 获取订单详情
// @Description 管理端获取订单详细信息
// @Tags 管理端-直购订单管理
// @x-apifox-folder "管理端/直购订单管理"
// @Param data query define.GetAdminTradeOrderDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAdminTradeOrderDetailResp}
// @Router /admin/v1/trade_order/detail [get]
// @Security Bearer
// @produce  json
func GetTradeOrderDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAdminTradeOrderDetailReq{}, s.GetAdminTradeOrderDetail)
}

// CancelAdminTradeOrder
// @Summary 取消订单
// @Description 取消订单
// @Tags 管理端-直购订单管理
// @x-apifox-folder "管理端/直购订单管理"
// @Param data body define.CancelAdminTradeOrderReq true "请求参数"
// @Success 200 {object} response.Data{data=define.CancelAdminTradeOrderResp}
// @Router /admin/v1/trade_order/cancel [post]
// @Security Bearer
// @produce  json
func CancelAdminTradeOrder(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.CancelAdminTradeOrderReq{}, s.CancelAdminTradeOrder)
}

// SyncAdminTradeOrderFreight
// @Summary 同步订单物流信息
// @Description 同步订单物流信息
// @Tags 管理端-直购订单管理
// @x-apifox-folder "管理端/直购订单管理"
// @Param data body define.SyncAdminTradeOrderFreightReq true "请求参数"
// @Success 200 {object} response.Data{data=define.SyncAdminTradeOrderFreightResp}
// @Router /admin/v1/trade_order/sync_freight [post]
// @Security Bearer
// @produce  json
func SyncAdminTradeOrderFreight(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.SyncAdminTradeOrderFreightReq{}, s.SyncAdminTradeOrderFreight)
}

// GetOrderPrivateInfo 获取订单隐私详情
// @Summary 获取订单隐私详情
// @Description 管理端获取订单隐私详情
// @Tags 管理端-直购订单管理
// @x-apifox-folder "管理端/直购订单管理"
// @Param data query define.AdminGetOrderPrivateInfoReq true "查询参数"
// @Success 200 {object} response.Data{data=define.AdminGetOrderPrivateInfoResp}
// @Router /admin/v1/trade_order/private_info [get]
// @Security Bearer
// @produce  json
func GetOrderPrivateInfo(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AdminGetOrderPrivateInfoReq{}, s.GetOrderPrivateInfo)
}
