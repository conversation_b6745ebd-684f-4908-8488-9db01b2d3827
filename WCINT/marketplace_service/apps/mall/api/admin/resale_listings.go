package admin

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"e.coding.net/g-dtay0385/common/go-util/response/g"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetAdminResaleListingsList 获取转卖挂单列表
// @Summary 获取转卖挂单列表
// @Description 管理端获取转卖挂单分页列表
// @Tags 管理端-转卖挂单管理
// @x-apifox-folder "管理端/转卖挂单管理"
// @Param data query define.GetAdminResaleListingsListReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetAdminResaleListingsListResp}
// @Router /admin/v1/resale_listings/list [get]
// @Security Bearer
// @produce  json
func GetAdminResaleListingsList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAdminResaleListingsListReq{}, s.GetAdminResaleListingsList)
}

// GetAdminResaleListingsDetail 获取转卖挂单详情
// @Summary 获取转卖挂单详情
// @Description 管理端获取转卖挂单详细信息
// @Tags 管理端-转卖挂单管理
// @x-apifox-folder "管理端/转卖挂单管理"
// @Param data query define.GetAdminResaleListingsDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAdminResaleListingsDetailResp}
// @Router /admin/v1/resale_listings/detail [get]
// @Security Bearer
// @produce  json
func GetAdminResaleListingsDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAdminResaleListingsDetailReq{}, s.GetAdminResaleListingsDetail)
}

// ExportResaleListingsList
// @Summary 导出转卖挂单列表
// @Description 导出转卖挂单列表，下载文件
// @Tags 管理端-转卖挂单管理
// @x-apifox-folder "管理端/转卖挂单管理"
// @Param data query define.GetAdminResaleListingsListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAdminResaleListingsListResp}
// @Router  /admin/v1/resale_listings/export [get]
// @Security Bearer
// @produce  json
func ExportResaleListingsList(ctx *gin.Context) {
	s := service.New(ctx)
	req := &define.GetAdminResaleListingsListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		g.Fail(ctx, response.ParamErr.SetMsg(err.Error()))
		return
	}
	err := s.ExportResaleListingsList(req)
	if err != nil {
		g.Fail(ctx, err)
		return
	}
}

// UpdateResaleListingsStatus 更新转卖挂单状态
// @Summary 更新转卖挂单状态
// @Description 管理端更新转卖挂单状态，比如下架
// @Tags 管理端-转卖挂单管理
// @x-apifox-folder "管理端/转卖挂单管理"
// @Param data query define.UpdateResaleListingsStatusReq true "查询参数"
// @Success 200 {object} response.Data{data=define.UpdateResaleListingsStatusResp}
// @Router /admin/v1/resale_listings/update_status [post]
// @Security Bearer
// @produce  json
func UpdateResaleListingsStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdateResaleListingsStatusReq{}, s.UpdateResaleListingsStatus)
}
