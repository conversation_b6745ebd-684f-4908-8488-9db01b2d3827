package web

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetWebResaleUserItemListByItem
// @Summary 查询转卖商品持仓商品列表
// @Description 查询转卖商品持仓商品列表
// @Tags 用户端-转卖商品持仓
// @x-apifox-folder "用户端/转卖商品持仓"
// @Param data query define.GetWebResaleUserItemListByItemReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebResaleUserItemListByItemResp}
// @Router  /web/v1/resale_user_item/item_list [get]
// @Security Bearer
// @produce  json
func GetWebResaleUserItemListByItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleUserItemListByItemReq{}, s.GetWebResaleUserItemListByItem)
}

// GetWebResaleUserItemListByUserItem
// @Summary 获取某个转卖商品的持仓列表
// @Description 获取某个转卖商品的持仓列表
// @Tags 用户端-转卖商品持仓
// @x-apifox-folder "用户端/转卖商品持仓"
// @Param data query define.GetWebResaleUserItemListByUserItemReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebResaleUserItemListByUserItemResp}
// @Router  /web/v1/resale_user_item/user_item_list [get]
// @Security Bearer
// @produce  json
func GetWebResaleUserItemListByUserItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleUserItemListByUserItemReq{}, s.GetWebResaleUserItemListByUserItem)
}

// CancelResaleUserItemFromWeb
// @Summary 取消出售
// @Description 取消出售
// @Tags 用户端-转卖商品持仓
// @x-apifox-folder "用户端/转卖商品持仓"
// @Param data body define.CancelResaleUserItemFromWebReq true "查询参数"
// @Success 200 {object} response.Data{data=define.CancelResaleUserItemFromWebResp}
// @Router  /web/v1/resale_user_item/cancel [post]
// @Security Bearer
// @produce  json
func CancelResaleUserItemFromWeb(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.CancelResaleUserItemFromWebReq{}, s.CancelResaleUserItemFromWeb)
}

// GetWebResaleUserItemTradeInfo
// @Summary 获取持仓商品交易信息
// @Description 获取持仓商品交易信息
// @Tags 用户端-转卖商品持仓
// @x-apifox-folder "用户端/转卖商品持仓"
// @Param data query define.GetWebResaleUserItemTradeInfoReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebResaleUserItemTradeInfoResp}
// @Router  /web/v1/resale_user_item/trade_info [get]
// @Security Bearer
// @produce  json
func GetWebResaleUserItemTradeInfo(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleUserItemTradeInfoReq{}, s.GetWebResaleUserItemTradeInfo)
}
