package web

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetWebResaleOrderBuyList
// @Summary 查询我购买的转卖订单列表
// @Description 查询我购买的转卖订单列表
// @Tags 用户端-转卖订单
// @x-apifox-folder "用户端/转卖订单"
// @Param data query define.GetWebResaleOrderBuyListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebResaleOrderBuyListResp}
// @Router  /web/v1/resale_order/buy/list [get]
// @Security Bearer
// @produce  json
func GetWebResaleOrderBuyList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleOrderBuyListReq{}, s.GetWebResaleOrderBuyList)
}

// GetWebResaleOrderSaleList
// @Summary 查询我出售的转卖订单列表
// @Description 查询我出售的转卖订单列表
// @Tags 用户端-转卖订单
// @x-apifox-folder "用户端/转卖订单"
// @Param data query define.GetWebResaleOrderSaleListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebResaleOrderSaleListResp}
// @Router  /web/v1/resale_order/sale/list [get]
// @Security Bearer
// @produce  json
func GetWebResaleOrderSaleList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleOrderSaleListReq{}, s.GetWebResaleOrderSaleList)
}

// GetWebResaleOrderBuyDetail
// @Summary 查询我购买的转卖订单详情
// @Description 查询我购买的转卖订单详情
// @Tags 用户端-转卖订单
// @x-apifox-folder "用户端/转卖订单"
// @Param data query define.GetWebResaleOrderBuyDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetWebResaleOrderBuyDetailResp}
// @Router /web/v1/resale_order/buy/detail [get]
// @Security Bearer
// @produce  json
func GetWebResaleOrderBuyDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleOrderBuyDetailReq{}, s.GetWebResaleOrderBuyDetail)
}

// GetWebResaleOrderSaleDetail
// @Summary 查询我出售的转卖订单详情
// @Description 查询我出售的转卖订单详情
// @Tags 用户端-转卖订单
// @x-apifox-folder "用户端/转卖订单"
// @Param data query define.GetWebResaleOrderSaleDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetWebResaleOrderSaleDetailResp}
// @Router /web/v1/resale_order/sale/detail [get]
// @Security Bearer
// @produce  json
func GetWebResaleOrderSaleDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleOrderSaleDetailReq{}, s.GetWebResaleOrderSaleDetail)
}

// GetWebResaleOrderRecentList
// @Summary 查询最近成交记录
// @Description 查询最近成交记录
// @Tags 用户端-转卖订单
// @x-apifox-folder "用户端/转卖订单"
// @Param data query define.GetWebResaleOrderRecentListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebResaleOrderRecentListResp}
// @Router  /web/v1/resale_order/recent_list [get]
// @Security Bearer
// @produce  json
func GetWebResaleOrderRecentList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleOrderRecentListReq{}, s.GetWebResaleOrderRecentList)
}
