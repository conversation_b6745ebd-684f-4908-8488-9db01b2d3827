package web

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetHomeFeedList
// @Summary 获取首页列表
// @Description 获取首页列表
// @Tags 用户端-首页管理
// @x-apifox-folder "用户端/首页管理"
// @Param data query define.WebGetHomeFeedListReq true "获取参数"
// @Success 200 {object} response.Data{data=define.WebGetHomeFeedListResp}
// @Router  /web/v1/home_feed/list [GET]
// @Security Bearer
// @produce  json
func GetHomeFeedList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.WebGetHomeFeedListReq{}, s.GetHomeFeedList)
}

// GetHomeFeedDetail
// @Summary 获取首页详情
// @Description 获取首页详情
// @Tags 用户端-首页管理
// @x-apifox-folder "用户端/首页管理"
// @Param data query define.WebGetHomeFeedDetailReq true "获取参数"
// @Success 200 {object} response.Data{data=define.WebGetHomeFeedDetailResp}
// @Router  /web/v1/home_feed/detail [GET]
// @Security Bearer
// @produce  json
func GetHomeFeedDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.WebGetHomeFeedDetailReq{}, s.GetHomeFeedDetail)
}

// AddHomeFeedViewCount
// @Summary 增加浏览数量
// @Description 增加浏览数量
// @Tags 用户端-首页管理
// @x-apifox-folder "用户端/首页管理"
// @Param data body define.WebAddHomeFeedViewCountReq true "获取参数"
// @Success 200 {object} response.Data{data=define.WebAddHomeFeedViewCountResp}
// @Router  /web/v1/home_feed/add_view_count [POST]
// @Security Bearer
// @produce  json
func AddHomeFeedViewCount(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.WebAddHomeFeedViewCountReq{}, s.AddViewCount)
}
