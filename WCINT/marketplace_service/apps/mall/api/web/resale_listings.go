package web

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetWebPublishedResaleListingsList
// @Summary 查询我发布的转卖挂单列表
// @Description 查询我发布的转卖挂单列表
// @Tags 用户端-转卖挂单
// @x-apifox-folder "用户端/转卖挂单"
// @Param data query define.GetWebPublishedResaleListingsListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebPublishedResaleListingsListResp}
// @Router  /web/v1/resale_listings/published_list [get]
// @Security Bearer
// @produce  json
func GetWebPublishedResaleListingsList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebPublishedResaleListingsListReq{}, s.GetWebPublishedResaleListingsList)
}

// TakeDownResaleListingsFromWeb
// @Summary 下架转卖挂单
// @Description 下架转卖挂单
// @Tags 用户端-转卖挂单
// @x-apifox-folder "用户端/转卖挂单"
// @Param data body define.TakeDownResaleListingsFromWebReq true "查询参数"
// @Success 200 {object} response.Data{data=define.TakeDownResaleListingsFromWebResp}
// @Router  /web/v1/resale_listings/take_down [post]
// @Security Bearer
// @produce  json
func TakeDownResaleListingsFromWeb(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.TakeDownResaleListingsFromWebReq{}, s.TakeDownResaleListingsFromWeb)
}

// GetWebResaleListingsItemOnSaleList
// @Summary 查询某个挂单商品在售列表
// @Description 查询某个挂单商品在售列表
// @Tags 用户端-转卖挂单
// @x-apifox-folder "用户端/转卖挂单"
// @Param data query define.GetWebResaleListingsItemOnSaleListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebResaleListingsItemOnSaleListResp}
// @Router  /web/v1/resale_listings_item/on_sale_list [get]
// @Security Bearer
// @produce  json
func GetWebResaleListingsItemOnSaleList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleListingsItemOnSaleListReq{}, s.GetWebResaleListingsItemOnSaleList)
}

// GetWebResaleListingsItemSettlementList
// @Summary 查询某个挂单商品购买结算列表
// @Description 查询某个挂单商品购买结算列表
// @Tags 用户端-转卖挂单管理
// @x-apifox-folder "用户端/转卖挂单"
// @Param data query define.GetWebResaleListingsItemSettlementListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebResaleListingsItemSettlementListResp}
// @Router  /web/v1/resale_listings_item/settlement_list [get]
// @Security Bearer
// @produce  json
func GetWebResaleListingsItemSettlementList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebResaleListingsItemSettlementListReq{}, s.GetWebResaleListingsItemSettlementList)
}

// AddResaleListings
// @Summary 挂单出售
// @Description 挂单出售
// @Tags 用户端-转卖挂单管理
// @x-apifox-folder "用户端/转卖挂单"
// @Param data body define.AddResaleListingsReq true "参数"
// @Success 200 {object} response.Data{data=define.AddResaleListingsResp}
// @Router  /web/v1/resale_listings/add [post]
// @Security Bearer
// @produce  json
func AddResaleListings(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddResaleListingsReq{}, s.AddResaleListings)
}
