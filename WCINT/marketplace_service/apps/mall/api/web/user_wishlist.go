package web

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetWishlistList 用户想要列表
// @Summary 用户想要列表
// @Description 用户想要列表
// @Tags 用户端-商品想要
// @x-apifox-folder "用户端/商品想要"
// @Param data query define.WebWishlistListReq true "请求参数"
// @Success 200 {object} response.Data{data=define.WebWishlistListResp}
// @Router /web/v1/user_wishlist/list [get]
// @Security Bearer
// @produce  json
func GetWishlistList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.WebWishlistListReq{}, s.GetWishlistList)
}

// AddWishlist 添加想要
// @Summary 添加想要
// @Description 添加想要
// @Tags 用户端-商品想要
// @x-apifox-folder "用户端/商品想要"
// @Param data body define.WebWishlistReq true "请求参数"
// @Success 200 {object} response.Data{}
// @Router /web/v1/user_wishlist/add [post]
// @Security Bearer
// @produce  json
func AddWishlist(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.WebWishlistReq{}, s.AddToWishlist)
}

// RemoveWishlist 移除想要
// @Summary 移除想要
// @Description 移除想要
// @Tags 用户端-商品想要
// @x-apifox-folder "用户端/商品想要"
// @Param data body define.WebWishlistReq true "请求参数"
// @Success 200 {object} response.Data{}
// @Router /web/v1/user_wishlist/remove [post]
// @Security Bearer
// @produce  json
func RemoveWishlist(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.WebWishlistReq{}, s.RemoveFromWishlist)
}
