package open

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetTradeOrderDetail
// @Summary 获取订单详情
// @Description 获取订单详情
// @Tags open端-直购订单
// @x-apifox-folder "open端/直购订单"
// @Param data query define.GetTradeOrderDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetTradeOrderDetailResp}
// @Router /open/v1/trade_order/detail [get]
// @Security Bearer
// @produce  json
func GetTradeOrderDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetTradeOrderDetailReq{}, s.GetTradeOrderDetail)
}

// TradeOrderPaySuccess
// @Summary 订单支付成功回调
// @Description 订单支付成功回调
// @Tags open端-直购订单
// @x-apifox-folder "open端/直购订单"
// @Param data body define.TradeOrderPaySuccessReq true "请求参数"
// @Success 200 {object} response.Data{data=define.TradeOrderPaySuccessResp}
// @Router /open/v1/trade_order/pay_success [post]
// @Security Bearer
// @produce  json
func TradeOrderPaySuccess(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.TradeOrderPaySuccessReq{}, s.TradeOrderPaySuccess)
}

// TradeOrderTimeoutClose
// @Summary 订单支付超时关闭
// @Description 订单支付超时关闭
// @Tags open端-直购订单
// @x-apifox-folder "open端/直购订单"
// @Param data body define.TradeOrderTimeoutCloseReq true "请求参数"
// @Success 200 {object} response.Data{}
// @Router /open/v1/trade_order/timeout_close [post]
// @Security Bearer
// @produce  json
func TradeOrderTimeoutClose(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.TradeOrderTimeoutCloseReq{}, s.TradeOrderTimeoutClose)
}

// TradeOrderFailedRetryUploadToSupply
// @Summary 重新上传失败的订单到供应链
// @Description 重新上传失败的订单到供应链
// @Tags open端-直购订单
// @x-apifox-folder "open端/直购订单"
// @Param data body define.TradeOrderFailedRetryUploadToSupplyReq true "请求参数"
// @Success 200 {object} response.Data{}
// @Router /open/v1/trade_order/retry_upload_to_supply [post]
// @Security Bearer
// @produce  json
func TradeOrderFailedRetryUploadToSupply(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.TradeOrderFailedRetryUploadToSupplyReq{}, s.TradeOrderFailedRetryUploadToSupply)
}

// TradeOrderFinish
// @Summary 订单支付自动收货
// @Description 订单支付自动收货
// @Tags open端-直购订单
// @x-apifox-folder "open端/直购订单"
// @Param data body define.TradeOrderFinishReq true "请求参数"
// @Success 200 {object} response.Data{data=define.TradeOrderFinishResp}
// @Router /open/v1/trade_order/finish [post]
// @Security Bearer
// @produce  json
func TradeOrderFinish(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.TradeOrderFinishReq{}, s.TradeOrderFinish)
}
