package open

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// TriggerDisplayByStartTime
// @Summary 处理开始展示的直购商品
// @Description 处理开始展示的直购商品
// @Tags open端-直购商品
// @x-apifox-folder "open端/直购商品"
// @Param data body define.OpenTriggerDisplayByStartTimeReq true "请求参数"
// @Success 200 {object} response.Data{}
// @Router /open/v1/mall_item/trigger_display [post]
// @Security Bearer
// @produce  json
func TriggerDisplayByStartTime(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.OpenTriggerDisplayByStartTimeReq{}, s.OpenTriggerDisplayByStartTime)
}

// UpdateMallItemNotify 商品信息变动通知
// @Summary 商品信息变动通知
// @Description 商品信息变动通知
// @Tags open端-直购商品
// @x-apifox-folder "open端/直购商品"
// @Param data body define.OpenUpdateMallItemNotifyReq true "请求参数"
// @Success 200 {object} response.Data{}
// @Router /open/v1/mall_item/update_notify [post]
// @Security Bearer
// @produce  json
func UpdateMallItemNotify(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.OpenUpdateMallItemNotifyReq{}, s.OpenUpdateMallItemNotify)
}
