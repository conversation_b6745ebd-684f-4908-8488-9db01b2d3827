package open

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// SyncViewCountToDB
// @Summary 同步首页浏览量到数据库
// @Description 同步首页浏览量到数据库
// @Tags open端-首页管理
// @x-apifox-folder "open端/首页管理"
// @Param data body define.OpenSyncViewCountToDBReq true "获取参数"
// @Success 200 {object} response.Data{data=define.OpenSyncViewCountToDBResp}
// @Router  /open/v1/home_feed/sync_view_count [post]
// @Security Bearer
// @produce  json
func SyncViewCountToDB(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.OpenSyncViewCountToDBReq{}, s.SyncViewCountToDB)
}
