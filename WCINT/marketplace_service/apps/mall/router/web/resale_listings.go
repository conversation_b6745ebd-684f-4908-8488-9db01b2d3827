package web

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/web"
)

func ResaleListings(router *gin.RouterGroup) {
	{
		group := router.Group("/resale_listings")
		// 查询我发布的转卖挂单列表
		group.GET("/published_list", web.GetWebPublishedResaleListingsList)
		// 下架转卖挂单
		group.POST("/take_down", web.TakeDownResaleListingsFromWeb)
		// 挂单出售
		group.POST("/add", web.AddResaleListings)
	}
	{
		itemGroup := router.Group("/resale_listings_item")
		// 查询某个商品在售挂单列表
		itemGroup.GET("/on_sale_list", web.GetWebResaleListingsItemOnSaleList)
		// 查询某个挂单商品购买结算列表
		itemGroup.GET("/settlement_list", web.GetWebResaleListingsItemSettlementList)
	}
}
