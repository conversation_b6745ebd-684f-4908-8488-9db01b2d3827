package admin

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/admin"
)

// ResaleOrder 转卖订单管理端相关
func ResaleOrder(router *gin.RouterGroup) {
	group := router.Group("/resale_order")
	{
		// 获取转卖订单列表
		group.GET("/list", admin.GetResaleOrderList)
		// 获取转卖订单详情
		group.GET("/detail", admin.GetResaleOrderDetail)
		// 导出转卖订单列表
		group.GET("/export", admin.ExportResaleOrderList)
	}
}
