package admin

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/admin"
)

// ResaleItem 转卖商品相关路由
func ResaleItem(router *gin.RouterGroup) {
	group := router.Group("/resale_item")
	{
		// 添加转卖商品
		group.POST("/add", admin.AddResaleItem)
		// 获取转卖商品列表
		group.GET("/list", admin.GetResaleItemList)
		// 获取转卖商品详情
		group.GET("/detail", admin.GetResaleItemDetail)
		// 编辑转卖商品
		group.POST("/edit", admin.EditResaleItem)
		// 编辑转卖商品优先级
		group.POST("/edit_priority", admin.EditResaleItemPriority)
		// 编辑转卖商品状态
		group.POST("/edit_status", admin.EditResaleItemStatus)
	}
}
