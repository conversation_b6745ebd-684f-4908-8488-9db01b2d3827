package router

import (
	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
	"marketplace_service/apps/mall/router/admin"
	"marketplace_service/apps/mall/router/open"
	"marketplace_service/apps/mall/router/web"
	"marketplace_service/global"
	"marketplace_service/pkg/middleware"
	"marketplace_service/pkg/middleware/g/auth"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}
func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	// 管理端路由
	adminRoute(r)
	// 用户端路由
	webRote(r)
	// 客户端路由
	openRote(r)
}

// 管理端路由
func adminRoute(router *gin.Engine) {
	w := router.Group("/admin/v1", middleware.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Admin{
		NoAuthUrl: []string{},
	}))

	admin.MallItem(w)

	admin.TradeOrder(w)

	admin.ResaleItem(w)

	admin.ResaleOrder(w)

	admin.ResaleListings(w)

	admin.CommonConfig(w)

	admin.HomeFeed(w)
}

// 用户端路由
func webRote(router *gin.Engine) {
	r := router.Group("/web/v1", middleware.ParseHead)
	r.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			"/web/v1/mall_item/list",
			"/web/v1/mall_item/detail",
			"/web/v1/resale_order/recent_list",
			"/web/v1/home_feed/list",
			"/web/v1/home_feed/detail",
			"/web/v1/home_feed/add_view_count",
			"/web/v1/resale_listings_item/on_sale_list",
		},
	}))
	// 心愿单相关路由
	web.UserWishlist(r)
	// 直购商品相关路由
	web.MallItem(r)
	// 直购订单相关路由
	web.TradeOrder(r)
	// 转卖订单相关路由
	web.ResaleOrder(r)
	// 转卖商品相关路由
	web.ResaleItem(r)
	// 转卖挂单相关路由
	web.ResaleListings(r)
	// 转卖持仓相关路由
	web.ResaleUserItem(r)
	// 商城首页相关路由
	web.HomeFeed(r)
}

// 开放路由
func openRote(router *gin.Engine) {
	w := router.Group("/open/v1", middleware.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Open{
		Token: global.GlobalConfig.Service.Token,
		NoAuthUrl: []string{
			"/open/v1/notice/supply_chain",
		},
	}))
	open.TradeOrder(w)
	open.MallItem(w)
	open.Notice(w)
	open.ResaleOrder(w)
	open.HomeFeed(w)
}
