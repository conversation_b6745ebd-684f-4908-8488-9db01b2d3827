package enums

// Terminal 终端
type Terminal string

func (r Terminal) Val() string {
	return string(r)
}

const (
	// Android 安卓
	Android Terminal = "android"
	// IOS 苹果
	IOS Terminal = "ios"
	// H5 浏览器
	H5 Terminal = "h5"
)

var TerminalMap = map[string]string{
	Android.Val(): Android.Val(),
	IOS.Val():     IOS.Val(),
	H5.Val():      H5.Val(),
}

type ConfSwitchTypeEnum int32

func (r ConfSwitchTypeEnum) Val() int32 {
	return int32(r)
}

const (
	ConfSwitchOn  ConfSwitchTypeEnum = 1 // 开关开启
	ConfSwitchOff ConfSwitchTypeEnum = 0 // 开关关闭
)
