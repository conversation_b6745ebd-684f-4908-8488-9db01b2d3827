package enums

// ResaleListingsStatus 转卖挂单状态
type ResaleListingsStatus int32

func (r ResaleListingsStatus) Val() int32 {
	return int32(r)
}

const (
	// ResaleListingsStatusDown 已下架
	ResaleListingsStatusDown ResaleListingsStatus = 0
	// ResaleListingsStatusOnSale 出售中
	ResaleListingsStatusOnSale ResaleListingsStatus = 1
	// ResaleListingsStatusSoldOut 已出售
	ResaleListingsStatusSoldOut ResaleListingsStatus = 2
	// ResaleListingsStatusCanceled 已取消
	ResaleListingsStatusCanceled ResaleListingsStatus = 3
)

var ResaleListingsStatusMap = map[int32]string{
	ResaleListingsStatusDown.Val():     "已下架",
	ResaleListingsStatusOnSale.Val():   "出售中",
	ResaleListingsStatusSoldOut.Val():  "已出售",
	ResaleListingsStatusCanceled.Val(): "已取消",
}

// ResaleListingsItemStatus 转卖挂单商品状态
type ResaleListingsItemStatus int32

func (r ResaleListingsItemStatus) Val() int32 {
	return int32(r)
}

const (
	// ResaleListingsItemStatusDown 已下架
	ResaleListingsItemStatusDown ResaleListingsItemStatus = 0
	// ResaleListingsItemStatusOnSale 出售中
	ResaleListingsItemStatusOnSale ResaleListingsItemStatus = 1
	// ResaleListingsItemStatusTrading 交易中
	ResaleListingsItemStatusTrading ResaleListingsItemStatus = 2
	// ResaleListingsItemStatusSold 已出售
	ResaleListingsItemStatusSold ResaleListingsItemStatus = 3
)

var ResaleListingsItemStatusMap = map[int32]string{
	ResaleListingsItemStatusDown.Val():    "已下架",
	ResaleListingsItemStatusOnSale.Val():  "出售中",
	ResaleListingsItemStatusTrading.Val(): "交易中",
	ResaleListingsItemStatusSold.Val():    "已出售",
}
