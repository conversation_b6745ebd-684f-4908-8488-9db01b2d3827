package define

import "marketplace_service/pkg/pagination"

type (
	WebWishlistListReq struct {
		pagination.Pagination
	}
	WebWishlistListResp struct {
		List    []*WebWishlistListData `json:"list"`
		HasMore bool                   `json:"has_more"` // 判断当前页是否为最后一页
	}
	WebWishlistListData struct {
		ID             int64  `json:"id,string"`          // 用户想要ID
		HomeFeedID     int64  `json:"home_feed_id"`       // 首页ID
		HomeFeedStatus int32  `json:"home_feed_status"`   // 首页状态
		ResaleStatus   int32  `json:"item_resale_status"` // 商品转卖状态
		ItemMallStatus int32  `json:"item_mall_status"`   // 商品直购状态
		ItemID         string `json:"item_id"`            // 商品ID
		ItemName       string `json:"item_name"`          // 商品名称
		ItemSpecs      string `json:"item_specs"`         // 商品规格
		ItemIconURL    string `json:"item_icon_url"`      // 商品主图
		Priority       int32  `json:"priority"`           // 优先级
		SalePrice      int64  `json:"sale_price"`         // 售价
		OriginalPrice  int64  `json:"original_price"`     // 闪购原价
		ResaleCount    int32  `json:"resale_count"`       // 转卖数量
		SalesVolume    int32  `json:"sales_volume"`       // 销量
		WishCount      int32  `json:"wish_count"`         // 想要数量
	}
)
type (
	WebWishlistReq struct {
		ItemID string `form:"item_id" json:"item_id" binding:"required"`
	}
)
