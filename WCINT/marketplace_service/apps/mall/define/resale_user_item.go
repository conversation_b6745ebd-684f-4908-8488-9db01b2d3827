package define

import (
	"marketplace_service/pkg/pagination"
	"time"
)

type (
	GetWebResaleUserItemListByItemReq struct {
		pagination.Pagination
		ItemName string `form:"item_name" json:"item_name"` // 商品名称
	}
	GetWebResaleUserItemListByItemData struct {
		ID           int64  `json:"id,string"`     // 主键
		ItemId       string `json:"item_id"`       // 商品 id
		ItemName     string `json:"item_name"`     // 商品名称
		ItemSpecs    string `json:"item_specs"`    // 商品规格
		ItemIconURL  string `json:"item_icon_url"` // 商品主图
		HoldQty      int64  `json:"hold_qty"`      // 持有数量
		SoldOut      int32  `json:"sold_out"`      // 商品转卖和直购都下架状态 0=正常，1=下架
		ResaleStatus int32  `json:"resale_status"` // 转卖状态（0-关闭, 1-开启）
	}
	GetWebResaleUserItemListByItemResp struct {
		List  []*GetWebResaleUserItemListByItemData `json:"list"`
		Total int64                                 `json:"total"`
	}
)

type (
	GetWebResaleUserItemListByUserItemReq struct {
		pagination.Pagination
		ItemID string `form:"item_id" json:"item_id" binding:"required"` // 商品 id
	}
	GetWebResaleUserItemListByUserItemData struct {
		ID                   string     `json:"id"`                             // 持仓 id
		ItemID               string     `json:"item_id"`                        // 商品 id
		ItemIconURL          string     `json:"item_icon_url"`                  // 商品图片
		ResaleListingsItemID int64      `json:"resale_listings_item_id,string"` // 转卖挂单商品 id
		BuyTime              time.Time  `json:"buy_time"`                       // 买入时间/物品获得时间
		BuyPrice             int32      `json:"buy_price"`                      // 买入价格（分），为 0 则展示：外部存入
		Status               int32      `json:"status"`                         // 状态，1：持有中，100：出售中
		CountdownSecond      int32      `json:"countdown_second"`               // 倒计时秒数
		SalePrice            int64      `json:"sale_price"`                     // 出售单价（分），状态为出售中时展示
		PreSale              bool       `json:"pre_sale"`                       // 是否预售
		DeliveryTime         *time.Time `json:"delivery_time"`                  // 提货时间
		ResaleStatus         int32      `json:"resale_status"`                  // 转卖状态（0-关闭, 1-开启）
		IntervalMinutes      int32      `json:"interval_minutes"`               // 转卖时间间隔（单位：分钟）
	}
	GetWebResaleUserItemListByUserItemResp struct {
		List    []*GetWebResaleUserItemListByUserItemData `json:"list"`
		HasMore bool                                      `json:"has_more"`
	}
)

type (
	CancelResaleUserItemFromWebReq struct {
		ID string `json:"id"`
	}
	CancelResaleUserItemFromWebResp struct {
		ID         string    `json:"id"`
		CanceledAt time.Time `json:"canceled_at"`
	}
)

type (
	GetWebResaleUserItemTradeInfoReq struct {
		ItemID string `form:"item_id" json:"item_id" binding:"required"` // 商品 id
	}
	GetWebResaleUserItemTradeInfoResp struct {
		ItemID          string `json:"item_id"`           // 商品 id
		ItemName        string `json:"item_name"`         // 商品名称
		ItemIconURL     string `json:"item_icon_url"`     // 商品图片
		ItemSpecs       string `json:"item_specs"`        // 商品规格
		MinSalePrice    int64  `json:"min_sale_price"`    // 最低在售价（分），为 -1 表示暂无挂单
		LatestSalePrice int64  `json:"latest_sale_price"` // 最新成交价（分），为 -1 表示暂无挂单
		FeeRate         int32  `json:"fee_rate"`          // 服务费百分比，比如 3% 就返回 3
		MinLimitPrice   int64  `json:"min_limit_price"`   // 最低限价（分）
		MaxLimitPrice   int64  `json:"max_limit_price"`   // 最高限价（分）
		AvailableQty    int64  `json:"available_qty"`     // 当前用户可出售的物品数量
	}
)
