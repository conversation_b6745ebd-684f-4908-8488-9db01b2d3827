package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	MS200001Err = response.NewError(200001, "商品不存在")
	MS200002Err = response.NewError(200002, "当前用户购买此商品次数已达上限")
	MS200003Err = response.NewError(200003, "支付金额有误")
	MS200004Err = response.NewError(200004, "库存不足")
	MS200005Err = response.NewError(200005, "云仓商品不存在")
	MS200006Err = response.NewError(200006, "云仓商品未开启供应")
	MS200007Err = response.NewError(200007, "云仓商品库存不足")
	MS200008Err = response.NewError(200008, "商品上架中不允许更换SKU商品")
	MS200009Err = response.NewError(200009, "同一SKU仅允许一个直购商品处于上架状态")
	MS200010Err = response.NewError(200010, "每日限购数量不能超过总限购数量")
	MS200011Err = response.NewError(200011, "别心急，未到商品开始时间~")
	MS200012Err = response.NewError(200012, "嗷哦，商品下架了~")
	MS200013Err = response.NewError(200013, "限购 %d 件，不能购买更多了哦~")
	MS200014Err = response.NewError(200014, "每日限购 %d 件哦~")
	MS200015Err = response.NewError(200015, "订单不存在")
	MS200016Err = response.NewError(200016, "转卖商品已存在")
	MS200017Err = response.NewError(200017, "最多显示%d条哦")
	MS200018Err = response.NewError(200018, "请输入出售密码")
	MS200019Err = response.NewError(200019, "出售密码错误")
	MS200020Err = response.NewError(200020, "当前不可转卖出售")
	MS200021Err = response.NewError(200021, "转卖价格严重偏离市场，不得低于 XX 元")
	MS200022Err = response.NewError(200022, "转卖价格严重偏离市场，不得高于 XX 元")
	MS200023Err = response.NewError(200023, "该挂单不可下架喔")
	MS200024Err = response.NewError(200024, "该商品无转卖挂单")
	MS200025Err = response.NewError(200025, "密码错误")
	MS200026Err = response.NewError(200026, "余额不足")
	MS200027Err = response.NewError(200027, "商品价格有变动，请重新核对下单。\n选定后请尽快支付，避免被他人抢先一步！")
	MS200028Err = response.NewError(200028, "商品未上架")
	MS200029Err = response.NewError(200029, "全局转卖开关已关闭，不允许开启转售")
	MS200030Err = response.NewError(200030, "不能购买自己出售的物品喔~")
	MS200031Err = response.NewError(200031, "暂无匹配的卖单哦~")
	MS200032Err = response.NewError(200032, "暂无 ta 人转卖")
)
