package define

type (
	GetConfigListReq struct {
		ConfigKeys string `form:"config_keys" json:"config_keys"` //逗号分隔
	}

	GetConfigListResp struct {
		ConfigList []*Config `json:"config_list"`
	}

	Config struct {
		ID        int64  `json:"id,string"`  // 主键
		ConfigKey string `json:"config_key"` // config_key
		Value     any    `json:"value"`      // value
		Remark    string `json:"remark"`     // 备注
	}
)

type (
	GetConfigDetailReq struct {
		ID        int64  `form:"id,string" json:"id,string"`   // 主键
		ConfigKey string `form:"config_key" json:"config_key"` //config_key
	}

	GetConfigDetailResp struct {
		ID        int64  `json:"id,string"`  // 主键
		ConfigKey string `json:"config_key"` // config_key
		Value     any    `json:"value"`      // value
		Remark    string `json:"remark"`     // 备注
	}
)

type (
	EditConfigReq struct {
		ConfigKey string `json:"config_key" binding:"required"` //config_key
		Value     any    `json:"value" binding:"required"`      //value
	}

	EditConfigResp struct {
		ID int64 `json:"id,string"`
	}
)

type (
	// ResaleStandardConfig 转卖标准配置
	ResaleStandardConfig struct {
		ResaleStatus    int32 `json:"resale_status"`    // 转卖状态
		IntervalMinutes int32 `json:"trade_interval"`   // 交易间隔（分）
		MinPriceRatio   int32 `json:"min_resale_ratio"` // 最低售价百分比（基于市场公允进价）
		MaxPriceRatio   int32 `json:"max_resale_ratio"` // 最高售价比例（基于历史最高成交价）
	}
	// ResaleInstructionConfig 转卖说明
	ResaleInstructionConfig struct {
		BannerUrl string `json:"banner_url"`
		Content   string `json:"content"`
	}
	// MallInstructionConfig 闪购说明
	MallInstructionConfig struct {
		BannerUrl string `json:"banner_url"`
		Content   string `json:"content"`
	}
)
