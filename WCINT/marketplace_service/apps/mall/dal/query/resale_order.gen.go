// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newResaleOrder(db *gorm.DB, opts ...gen.DOOption) resaleOrder {
	_resaleOrder := resaleOrder{}

	_resaleOrder.resaleOrderDo.UseDB(db, opts...)
	_resaleOrder.resaleOrderDo.UseModel(&model.ResaleOrder{})

	tableName := _resaleOrder.resaleOrderDo.TableName()
	_resaleOrder.ALL = field.NewAsterisk(tableName)
	_resaleOrder.ID = field.NewInt64(tableName, "id")
	_resaleOrder.Status = field.NewInt32(tableName, "status")
	_resaleOrder.BuyerID = field.NewString(tableName, "buyer_id")
	_resaleOrder.BuyerPhone = field.NewString(tableName, "buyer_phone")
	_resaleOrder.SellerID = field.NewString(tableName, "seller_id")
	_resaleOrder.SellerPhone = field.NewString(tableName, "seller_phone")
	_resaleOrder.SkuID = field.NewString(tableName, "sku_id")
	_resaleOrder.ItemID = field.NewString(tableName, "item_id")
	_resaleOrder.ItemName = field.NewString(tableName, "item_name")
	_resaleOrder.ItemIconURL = field.NewString(tableName, "item_icon_url")
	_resaleOrder.ItemSpecs = field.NewString(tableName, "item_specs")
	_resaleOrder.ResaleListingsID = field.NewInt64(tableName, "resale_listings_id")
	_resaleOrder.PaymentStatus = field.NewInt32(tableName, "payment_status")
	_resaleOrder.TotalAmount = field.NewInt64(tableName, "total_amount")
	_resaleOrder.TotalFee = field.NewInt64(tableName, "total_fee")
	_resaleOrder.PayAmount = field.NewInt64(tableName, "pay_amount")
	_resaleOrder.SalePrice = field.NewInt64(tableName, "sale_price")
	_resaleOrder.Quantity = field.NewInt32(tableName, "quantity")
	_resaleOrder.PaymentMethod = field.NewString(tableName, "payment_method")
	_resaleOrder.PaymentAt = field.NewTime(tableName, "payment_at")
	_resaleOrder.TransferAt = field.NewTime(tableName, "transfer_at")
	_resaleOrder.FinishedAt = field.NewTime(tableName, "finished_at")
	_resaleOrder.Terminal = field.NewString(tableName, "terminal")
	_resaleOrder.AppVersion = field.NewString(tableName, "app_version")
	_resaleOrder.BatchID = field.NewInt64(tableName, "batch_id")
	_resaleOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_resaleOrder.UpdatedAt = field.NewTime(tableName, "updated_at")
	_resaleOrder.IsDel = field.NewField(tableName, "is_del")
	_resaleOrder.ResaleOrderItem = resaleOrderHasManyResaleOrderItem{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ResaleOrderItem", "model.ResaleOrderItem"),
	}

	_resaleOrder.fillFieldMap()

	return _resaleOrder
}

// resaleOrder 转卖订单表
type resaleOrder struct {
	resaleOrderDo

	ALL              field.Asterisk
	ID               field.Int64  // 主键
	Status           field.Int32  // 状态（0=待支付，1=物品锁定成功，10=已支付，20=转移中，30=已完成，40=已取消）
	BuyerID          field.String // 用户ID
	BuyerPhone       field.String // 用户手机号码
	SellerID         field.String // 出售用户ID
	SellerPhone      field.String // 出售用户手机号码
	SkuID            field.String // sku ID
	ItemID           field.String // 商品ID
	ItemName         field.String // 商品名称
	ItemIconURL      field.String // 商品主图
	ItemSpecs        field.String // 商品规格
	ResaleListingsID field.Int64  // 出售单ID
	PaymentStatus    field.Int32  // 支付状态（0=未支付，1=已支付）
	TotalAmount      field.Int64  // 总金额
	TotalFee         field.Int64  // 手续费
	PayAmount        field.Int64  // 支付金额
	SalePrice        field.Int64  // 售价
	Quantity         field.Int32  // 购买数量
	PaymentMethod    field.String // 支付方式
	PaymentAt        field.Time   // 支付时间
	TransferAt       field.Time   // 物品转移时间
	FinishedAt       field.Time   // 订单完成时间
	Terminal         field.String // 终端
	AppVersion       field.String // 版本号
	BatchID          field.Int64  // 批量购买ID
	CreatedAt        field.Time   // 创建时间
	UpdatedAt        field.Time   // 更新时间
	IsDel            field.Field  // 是否删除【0->未删除; 1->删除】
	ResaleOrderItem  resaleOrderHasManyResaleOrderItem

	fieldMap map[string]field.Expr
}

func (r resaleOrder) Table(newTableName string) *resaleOrder {
	r.resaleOrderDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r resaleOrder) As(alias string) *resaleOrder {
	r.resaleOrderDo.DO = *(r.resaleOrderDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *resaleOrder) updateTableName(table string) *resaleOrder {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt64(table, "id")
	r.Status = field.NewInt32(table, "status")
	r.BuyerID = field.NewString(table, "buyer_id")
	r.BuyerPhone = field.NewString(table, "buyer_phone")
	r.SellerID = field.NewString(table, "seller_id")
	r.SellerPhone = field.NewString(table, "seller_phone")
	r.SkuID = field.NewString(table, "sku_id")
	r.ItemID = field.NewString(table, "item_id")
	r.ItemName = field.NewString(table, "item_name")
	r.ItemIconURL = field.NewString(table, "item_icon_url")
	r.ItemSpecs = field.NewString(table, "item_specs")
	r.ResaleListingsID = field.NewInt64(table, "resale_listings_id")
	r.PaymentStatus = field.NewInt32(table, "payment_status")
	r.TotalAmount = field.NewInt64(table, "total_amount")
	r.TotalFee = field.NewInt64(table, "total_fee")
	r.PayAmount = field.NewInt64(table, "pay_amount")
	r.SalePrice = field.NewInt64(table, "sale_price")
	r.Quantity = field.NewInt32(table, "quantity")
	r.PaymentMethod = field.NewString(table, "payment_method")
	r.PaymentAt = field.NewTime(table, "payment_at")
	r.TransferAt = field.NewTime(table, "transfer_at")
	r.FinishedAt = field.NewTime(table, "finished_at")
	r.Terminal = field.NewString(table, "terminal")
	r.AppVersion = field.NewString(table, "app_version")
	r.BatchID = field.NewInt64(table, "batch_id")
	r.CreatedAt = field.NewTime(table, "created_at")
	r.UpdatedAt = field.NewTime(table, "updated_at")
	r.IsDel = field.NewField(table, "is_del")

	r.fillFieldMap()

	return r
}

func (r *resaleOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *resaleOrder) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 29)
	r.fieldMap["id"] = r.ID
	r.fieldMap["status"] = r.Status
	r.fieldMap["buyer_id"] = r.BuyerID
	r.fieldMap["buyer_phone"] = r.BuyerPhone
	r.fieldMap["seller_id"] = r.SellerID
	r.fieldMap["seller_phone"] = r.SellerPhone
	r.fieldMap["sku_id"] = r.SkuID
	r.fieldMap["item_id"] = r.ItemID
	r.fieldMap["item_name"] = r.ItemName
	r.fieldMap["item_icon_url"] = r.ItemIconURL
	r.fieldMap["item_specs"] = r.ItemSpecs
	r.fieldMap["resale_listings_id"] = r.ResaleListingsID
	r.fieldMap["payment_status"] = r.PaymentStatus
	r.fieldMap["total_amount"] = r.TotalAmount
	r.fieldMap["total_fee"] = r.TotalFee
	r.fieldMap["pay_amount"] = r.PayAmount
	r.fieldMap["sale_price"] = r.SalePrice
	r.fieldMap["quantity"] = r.Quantity
	r.fieldMap["payment_method"] = r.PaymentMethod
	r.fieldMap["payment_at"] = r.PaymentAt
	r.fieldMap["transfer_at"] = r.TransferAt
	r.fieldMap["finished_at"] = r.FinishedAt
	r.fieldMap["terminal"] = r.Terminal
	r.fieldMap["app_version"] = r.AppVersion
	r.fieldMap["batch_id"] = r.BatchID
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
	r.fieldMap["is_del"] = r.IsDel

}

func (r resaleOrder) clone(db *gorm.DB) resaleOrder {
	r.resaleOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r resaleOrder) replaceDB(db *gorm.DB) resaleOrder {
	r.resaleOrderDo.ReplaceDB(db)
	return r
}

type resaleOrderHasManyResaleOrderItem struct {
	db *gorm.DB

	field.RelationField
}

func (a resaleOrderHasManyResaleOrderItem) Where(conds ...field.Expr) *resaleOrderHasManyResaleOrderItem {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a resaleOrderHasManyResaleOrderItem) WithContext(ctx context.Context) *resaleOrderHasManyResaleOrderItem {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a resaleOrderHasManyResaleOrderItem) Session(session *gorm.Session) *resaleOrderHasManyResaleOrderItem {
	a.db = a.db.Session(session)
	return &a
}

func (a resaleOrderHasManyResaleOrderItem) Model(m *model.ResaleOrder) *resaleOrderHasManyResaleOrderItemTx {
	return &resaleOrderHasManyResaleOrderItemTx{a.db.Model(m).Association(a.Name())}
}

type resaleOrderHasManyResaleOrderItemTx struct{ tx *gorm.Association }

func (a resaleOrderHasManyResaleOrderItemTx) Find() (result []*model.ResaleOrderItem, err error) {
	return result, a.tx.Find(&result)
}

func (a resaleOrderHasManyResaleOrderItemTx) Append(values ...*model.ResaleOrderItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a resaleOrderHasManyResaleOrderItemTx) Replace(values ...*model.ResaleOrderItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a resaleOrderHasManyResaleOrderItemTx) Delete(values ...*model.ResaleOrderItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a resaleOrderHasManyResaleOrderItemTx) Clear() error {
	return a.tx.Clear()
}

func (a resaleOrderHasManyResaleOrderItemTx) Count() int64 {
	return a.tx.Count()
}

type resaleOrderDo struct{ gen.DO }

type IResaleOrderDo interface {
	gen.SubQuery
	Debug() IResaleOrderDo
	WithContext(ctx context.Context) IResaleOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IResaleOrderDo
	WriteDB() IResaleOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IResaleOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IResaleOrderDo
	Not(conds ...gen.Condition) IResaleOrderDo
	Or(conds ...gen.Condition) IResaleOrderDo
	Select(conds ...field.Expr) IResaleOrderDo
	Where(conds ...gen.Condition) IResaleOrderDo
	Order(conds ...field.Expr) IResaleOrderDo
	Distinct(cols ...field.Expr) IResaleOrderDo
	Omit(cols ...field.Expr) IResaleOrderDo
	Join(table schema.Tabler, on ...field.Expr) IResaleOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IResaleOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IResaleOrderDo
	Group(cols ...field.Expr) IResaleOrderDo
	Having(conds ...gen.Condition) IResaleOrderDo
	Limit(limit int) IResaleOrderDo
	Offset(offset int) IResaleOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleOrderDo
	Unscoped() IResaleOrderDo
	Create(values ...*model.ResaleOrder) error
	CreateInBatches(values []*model.ResaleOrder, batchSize int) error
	Save(values ...*model.ResaleOrder) error
	First() (*model.ResaleOrder, error)
	Take() (*model.ResaleOrder, error)
	Last() (*model.ResaleOrder, error)
	Find() ([]*model.ResaleOrder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleOrder, err error)
	FindInBatches(result *[]*model.ResaleOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ResaleOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IResaleOrderDo
	Assign(attrs ...field.AssignExpr) IResaleOrderDo
	Joins(fields ...field.RelationField) IResaleOrderDo
	Preload(fields ...field.RelationField) IResaleOrderDo
	FirstOrInit() (*model.ResaleOrder, error)
	FirstOrCreate() (*model.ResaleOrder, error)
	FindByPage(offset int, limit int) (result []*model.ResaleOrder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IResaleOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (r resaleOrderDo) Debug() IResaleOrderDo {
	return r.withDO(r.DO.Debug())
}

func (r resaleOrderDo) WithContext(ctx context.Context) IResaleOrderDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r resaleOrderDo) ReadDB() IResaleOrderDo {
	return r.Clauses(dbresolver.Read)
}

func (r resaleOrderDo) WriteDB() IResaleOrderDo {
	return r.Clauses(dbresolver.Write)
}

func (r resaleOrderDo) Session(config *gorm.Session) IResaleOrderDo {
	return r.withDO(r.DO.Session(config))
}

func (r resaleOrderDo) Clauses(conds ...clause.Expression) IResaleOrderDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r resaleOrderDo) Returning(value interface{}, columns ...string) IResaleOrderDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r resaleOrderDo) Not(conds ...gen.Condition) IResaleOrderDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r resaleOrderDo) Or(conds ...gen.Condition) IResaleOrderDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r resaleOrderDo) Select(conds ...field.Expr) IResaleOrderDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r resaleOrderDo) Where(conds ...gen.Condition) IResaleOrderDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r resaleOrderDo) Order(conds ...field.Expr) IResaleOrderDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r resaleOrderDo) Distinct(cols ...field.Expr) IResaleOrderDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r resaleOrderDo) Omit(cols ...field.Expr) IResaleOrderDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r resaleOrderDo) Join(table schema.Tabler, on ...field.Expr) IResaleOrderDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r resaleOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IResaleOrderDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r resaleOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IResaleOrderDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r resaleOrderDo) Group(cols ...field.Expr) IResaleOrderDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r resaleOrderDo) Having(conds ...gen.Condition) IResaleOrderDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r resaleOrderDo) Limit(limit int) IResaleOrderDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r resaleOrderDo) Offset(offset int) IResaleOrderDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r resaleOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleOrderDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r resaleOrderDo) Unscoped() IResaleOrderDo {
	return r.withDO(r.DO.Unscoped())
}

func (r resaleOrderDo) Create(values ...*model.ResaleOrder) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r resaleOrderDo) CreateInBatches(values []*model.ResaleOrder, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r resaleOrderDo) Save(values ...*model.ResaleOrder) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r resaleOrderDo) First() (*model.ResaleOrder, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrder), nil
	}
}

func (r resaleOrderDo) Take() (*model.ResaleOrder, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrder), nil
	}
}

func (r resaleOrderDo) Last() (*model.ResaleOrder, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrder), nil
	}
}

func (r resaleOrderDo) Find() ([]*model.ResaleOrder, error) {
	result, err := r.DO.Find()
	return result.([]*model.ResaleOrder), err
}

func (r resaleOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleOrder, err error) {
	buf := make([]*model.ResaleOrder, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r resaleOrderDo) FindInBatches(result *[]*model.ResaleOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r resaleOrderDo) Attrs(attrs ...field.AssignExpr) IResaleOrderDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r resaleOrderDo) Assign(attrs ...field.AssignExpr) IResaleOrderDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r resaleOrderDo) Joins(fields ...field.RelationField) IResaleOrderDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r resaleOrderDo) Preload(fields ...field.RelationField) IResaleOrderDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r resaleOrderDo) FirstOrInit() (*model.ResaleOrder, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrder), nil
	}
}

func (r resaleOrderDo) FirstOrCreate() (*model.ResaleOrder, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrder), nil
	}
}

func (r resaleOrderDo) FindByPage(offset int, limit int) (result []*model.ResaleOrder, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r resaleOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r resaleOrderDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r resaleOrderDo) Delete(models ...*model.ResaleOrder) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *resaleOrderDo) withDO(do gen.Dao) *resaleOrderDo {
	r.DO = *do.(*gen.DO)
	return r
}
