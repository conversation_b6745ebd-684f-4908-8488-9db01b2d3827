// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newResaleItem(db *gorm.DB, opts ...gen.DOOption) resaleItem {
	_resaleItem := resaleItem{}

	_resaleItem.resaleItemDo.UseDB(db, opts...)
	_resaleItem.resaleItemDo.UseModel(&model.ResaleItem{})

	tableName := _resaleItem.resaleItemDo.TableName()
	_resaleItem.ALL = field.NewAsterisk(tableName)
	_resaleItem.ID = field.NewInt64(tableName, "id")
	_resaleItem.Status = field.NewInt32(tableName, "status")
	_resaleItem.ItemID = field.NewString(tableName, "item_id")
	_resaleItem.SkuID = field.NewString(tableName, "sku_id")
	_resaleItem.ItemName = field.NewString(tableName, "item_name")
	_resaleItem.ItemSpecs = field.NewString(tableName, "item_specs")
	_resaleItem.ItemIconURL = field.NewString(tableName, "item_icon_url")
	_resaleItem.IPID = field.NewString(tableName, "ip_id")
	_resaleItem.TrademarkID = field.NewString(tableName, "trademark_id")
	_resaleItem.CategoryID = field.NewString(tableName, "category_id")
	_resaleItem.MarketPrice = field.NewInt64(tableName, "market_price")
	_resaleItem.ResaleStatus = field.NewInt32(tableName, "resale_status")
	_resaleItem.TradeFrequencyType = field.NewInt32(tableName, "trade_frequency_type")
	_resaleItem.IntervalMinutes = field.NewInt32(tableName, "interval_minutes")
	_resaleItem.IsCustomPriceLimit = field.NewInt32(tableName, "is_custom_price_limit")
	_resaleItem.MinPriceRatio = field.NewInt32(tableName, "min_price_ratio")
	_resaleItem.MaxPriceRatio = field.NewInt32(tableName, "max_price_ratio")
	_resaleItem.ShelfTime = field.NewTime(tableName, "shelf_time")
	_resaleItem.Priority = field.NewInt32(tableName, "priority")
	_resaleItem.CreatedBy = field.NewString(tableName, "created_by")
	_resaleItem.CreatedAt = field.NewTime(tableName, "created_at")
	_resaleItem.UpdatedBy = field.NewString(tableName, "updated_by")
	_resaleItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_resaleItem.IsDel = field.NewField(tableName, "is_del")

	_resaleItem.fillFieldMap()

	return _resaleItem
}

// resaleItem 转卖商品表
type resaleItem struct {
	resaleItemDo

	ALL                field.Asterisk
	ID                 field.Int64  // 主键
	Status             field.Int32  // 状态（0-待上架，1-已上架，2-已下架）
	ItemID             field.String // 商品ID
	SkuID              field.String // 商品 sku_id
	ItemName           field.String // 商品名称
	ItemSpecs          field.String // 商品规格
	ItemIconURL        field.String // 商品主图
	IPID               field.String // 商品 ip_id
	TrademarkID        field.String // 商品品牌id
	CategoryID         field.String // 商品分类id
	MarketPrice        field.Int64  // 市场公允价（分）
	ResaleStatus       field.Int32  // 转卖状态（0-关闭, 1-开启）
	TradeFrequencyType field.Int32  // 交易频次类型（1-同步全局标准, 2-特定交易频次）
	IntervalMinutes    field.Int32  // 交易间隔（分）
	IsCustomPriceLimit field.Int32  // 是否使用特定限价（0-关闭, 1-开启）
	MinPriceRatio      field.Int32  // 最低售价比例（基于市场公允进价）
	MaxPriceRatio      field.Int32  // 最高售价比例（基于历史最高成交价）
	ShelfTime          field.Time   // 上架时间
	Priority           field.Int32  // 优先级
	CreatedBy          field.String // 创建人
	CreatedAt          field.Time   // 创建时间
	UpdatedBy          field.String // 更新人
	UpdatedAt          field.Time   // 更新时间
	IsDel              field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (r resaleItem) Table(newTableName string) *resaleItem {
	r.resaleItemDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r resaleItem) As(alias string) *resaleItem {
	r.resaleItemDo.DO = *(r.resaleItemDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *resaleItem) updateTableName(table string) *resaleItem {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt64(table, "id")
	r.Status = field.NewInt32(table, "status")
	r.ItemID = field.NewString(table, "item_id")
	r.SkuID = field.NewString(table, "sku_id")
	r.ItemName = field.NewString(table, "item_name")
	r.ItemSpecs = field.NewString(table, "item_specs")
	r.ItemIconURL = field.NewString(table, "item_icon_url")
	r.IPID = field.NewString(table, "ip_id")
	r.TrademarkID = field.NewString(table, "trademark_id")
	r.CategoryID = field.NewString(table, "category_id")
	r.MarketPrice = field.NewInt64(table, "market_price")
	r.ResaleStatus = field.NewInt32(table, "resale_status")
	r.TradeFrequencyType = field.NewInt32(table, "trade_frequency_type")
	r.IntervalMinutes = field.NewInt32(table, "interval_minutes")
	r.IsCustomPriceLimit = field.NewInt32(table, "is_custom_price_limit")
	r.MinPriceRatio = field.NewInt32(table, "min_price_ratio")
	r.MaxPriceRatio = field.NewInt32(table, "max_price_ratio")
	r.ShelfTime = field.NewTime(table, "shelf_time")
	r.Priority = field.NewInt32(table, "priority")
	r.CreatedBy = field.NewString(table, "created_by")
	r.CreatedAt = field.NewTime(table, "created_at")
	r.UpdatedBy = field.NewString(table, "updated_by")
	r.UpdatedAt = field.NewTime(table, "updated_at")
	r.IsDel = field.NewField(table, "is_del")

	r.fillFieldMap()

	return r
}

func (r *resaleItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *resaleItem) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 24)
	r.fieldMap["id"] = r.ID
	r.fieldMap["status"] = r.Status
	r.fieldMap["item_id"] = r.ItemID
	r.fieldMap["sku_id"] = r.SkuID
	r.fieldMap["item_name"] = r.ItemName
	r.fieldMap["item_specs"] = r.ItemSpecs
	r.fieldMap["item_icon_url"] = r.ItemIconURL
	r.fieldMap["ip_id"] = r.IPID
	r.fieldMap["trademark_id"] = r.TrademarkID
	r.fieldMap["category_id"] = r.CategoryID
	r.fieldMap["market_price"] = r.MarketPrice
	r.fieldMap["resale_status"] = r.ResaleStatus
	r.fieldMap["trade_frequency_type"] = r.TradeFrequencyType
	r.fieldMap["interval_minutes"] = r.IntervalMinutes
	r.fieldMap["is_custom_price_limit"] = r.IsCustomPriceLimit
	r.fieldMap["min_price_ratio"] = r.MinPriceRatio
	r.fieldMap["max_price_ratio"] = r.MaxPriceRatio
	r.fieldMap["shelf_time"] = r.ShelfTime
	r.fieldMap["priority"] = r.Priority
	r.fieldMap["created_by"] = r.CreatedBy
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_by"] = r.UpdatedBy
	r.fieldMap["updated_at"] = r.UpdatedAt
	r.fieldMap["is_del"] = r.IsDel
}

func (r resaleItem) clone(db *gorm.DB) resaleItem {
	r.resaleItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r resaleItem) replaceDB(db *gorm.DB) resaleItem {
	r.resaleItemDo.ReplaceDB(db)
	return r
}

type resaleItemDo struct{ gen.DO }

type IResaleItemDo interface {
	gen.SubQuery
	Debug() IResaleItemDo
	WithContext(ctx context.Context) IResaleItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IResaleItemDo
	WriteDB() IResaleItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IResaleItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IResaleItemDo
	Not(conds ...gen.Condition) IResaleItemDo
	Or(conds ...gen.Condition) IResaleItemDo
	Select(conds ...field.Expr) IResaleItemDo
	Where(conds ...gen.Condition) IResaleItemDo
	Order(conds ...field.Expr) IResaleItemDo
	Distinct(cols ...field.Expr) IResaleItemDo
	Omit(cols ...field.Expr) IResaleItemDo
	Join(table schema.Tabler, on ...field.Expr) IResaleItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IResaleItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IResaleItemDo
	Group(cols ...field.Expr) IResaleItemDo
	Having(conds ...gen.Condition) IResaleItemDo
	Limit(limit int) IResaleItemDo
	Offset(offset int) IResaleItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleItemDo
	Unscoped() IResaleItemDo
	Create(values ...*model.ResaleItem) error
	CreateInBatches(values []*model.ResaleItem, batchSize int) error
	Save(values ...*model.ResaleItem) error
	First() (*model.ResaleItem, error)
	Take() (*model.ResaleItem, error)
	Last() (*model.ResaleItem, error)
	Find() ([]*model.ResaleItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleItem, err error)
	FindInBatches(result *[]*model.ResaleItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ResaleItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IResaleItemDo
	Assign(attrs ...field.AssignExpr) IResaleItemDo
	Joins(fields ...field.RelationField) IResaleItemDo
	Preload(fields ...field.RelationField) IResaleItemDo
	FirstOrInit() (*model.ResaleItem, error)
	FirstOrCreate() (*model.ResaleItem, error)
	FindByPage(offset int, limit int) (result []*model.ResaleItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IResaleItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (r resaleItemDo) Debug() IResaleItemDo {
	return r.withDO(r.DO.Debug())
}

func (r resaleItemDo) WithContext(ctx context.Context) IResaleItemDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r resaleItemDo) ReadDB() IResaleItemDo {
	return r.Clauses(dbresolver.Read)
}

func (r resaleItemDo) WriteDB() IResaleItemDo {
	return r.Clauses(dbresolver.Write)
}

func (r resaleItemDo) Session(config *gorm.Session) IResaleItemDo {
	return r.withDO(r.DO.Session(config))
}

func (r resaleItemDo) Clauses(conds ...clause.Expression) IResaleItemDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r resaleItemDo) Returning(value interface{}, columns ...string) IResaleItemDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r resaleItemDo) Not(conds ...gen.Condition) IResaleItemDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r resaleItemDo) Or(conds ...gen.Condition) IResaleItemDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r resaleItemDo) Select(conds ...field.Expr) IResaleItemDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r resaleItemDo) Where(conds ...gen.Condition) IResaleItemDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r resaleItemDo) Order(conds ...field.Expr) IResaleItemDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r resaleItemDo) Distinct(cols ...field.Expr) IResaleItemDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r resaleItemDo) Omit(cols ...field.Expr) IResaleItemDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r resaleItemDo) Join(table schema.Tabler, on ...field.Expr) IResaleItemDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r resaleItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IResaleItemDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r resaleItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IResaleItemDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r resaleItemDo) Group(cols ...field.Expr) IResaleItemDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r resaleItemDo) Having(conds ...gen.Condition) IResaleItemDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r resaleItemDo) Limit(limit int) IResaleItemDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r resaleItemDo) Offset(offset int) IResaleItemDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r resaleItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleItemDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r resaleItemDo) Unscoped() IResaleItemDo {
	return r.withDO(r.DO.Unscoped())
}

func (r resaleItemDo) Create(values ...*model.ResaleItem) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r resaleItemDo) CreateInBatches(values []*model.ResaleItem, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r resaleItemDo) Save(values ...*model.ResaleItem) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r resaleItemDo) First() (*model.ResaleItem, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleItem), nil
	}
}

func (r resaleItemDo) Take() (*model.ResaleItem, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleItem), nil
	}
}

func (r resaleItemDo) Last() (*model.ResaleItem, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleItem), nil
	}
}

func (r resaleItemDo) Find() ([]*model.ResaleItem, error) {
	result, err := r.DO.Find()
	return result.([]*model.ResaleItem), err
}

func (r resaleItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleItem, err error) {
	buf := make([]*model.ResaleItem, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r resaleItemDo) FindInBatches(result *[]*model.ResaleItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r resaleItemDo) Attrs(attrs ...field.AssignExpr) IResaleItemDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r resaleItemDo) Assign(attrs ...field.AssignExpr) IResaleItemDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r resaleItemDo) Joins(fields ...field.RelationField) IResaleItemDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r resaleItemDo) Preload(fields ...field.RelationField) IResaleItemDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r resaleItemDo) FirstOrInit() (*model.ResaleItem, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleItem), nil
	}
}

func (r resaleItemDo) FirstOrCreate() (*model.ResaleItem, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleItem), nil
	}
}

func (r resaleItemDo) FindByPage(offset int, limit int) (result []*model.ResaleItem, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r resaleItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r resaleItemDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r resaleItemDo) Delete(models ...*model.ResaleItem) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *resaleItemDo) withDO(do gen.Dao) *resaleItemDo {
	r.DO = *do.(*gen.DO)
	return r
}
