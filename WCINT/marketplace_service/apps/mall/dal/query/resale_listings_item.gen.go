// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newResaleListingsItem(db *gorm.DB, opts ...gen.DOOption) resaleListingsItem {
	_resaleListingsItem := resaleListingsItem{}

	_resaleListingsItem.resaleListingsItemDo.UseDB(db, opts...)
	_resaleListingsItem.resaleListingsItemDo.UseModel(&model.ResaleListingsItem{})

	tableName := _resaleListingsItem.resaleListingsItemDo.TableName()
	_resaleListingsItem.ALL = field.NewAsterisk(tableName)
	_resaleListingsItem.ID = field.NewInt64(tableName, "id")
	_resaleListingsItem.SellerID = field.NewString(tableName, "seller_id")
	_resaleListingsItem.ResaleListingsID = field.NewInt64(tableName, "resale_listings_id")
	_resaleListingsItem.Status = field.NewInt32(tableName, "status")
	_resaleListingsItem.UserItemID = field.NewString(tableName, "user_item_id")
	_resaleListingsItem.TradeInfo = field.NewField(tableName, "trade_info")
	_resaleListingsItem.SalePrice = field.NewInt64(tableName, "sale_price")
	_resaleListingsItem.ItemID = field.NewString(tableName, "item_id")
	_resaleListingsItem.BuyerID = field.NewString(tableName, "buyer_id")
	_resaleListingsItem.ResaleOrderID = field.NewInt64(tableName, "resale_order_id")
	_resaleListingsItem.ResaleOrderItemID = field.NewInt64(tableName, "resale_order_item_id")
	_resaleListingsItem.Fee = field.NewInt64(tableName, "fee")
	_resaleListingsItem.TradeAt = field.NewTime(tableName, "trade_at")
	_resaleListingsItem.CreatedAt = field.NewTime(tableName, "created_at")
	_resaleListingsItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_resaleListingsItem.IsDel = field.NewField(tableName, "is_del")

	_resaleListingsItem.fillFieldMap()

	return _resaleListingsItem
}

// resaleListingsItem 转卖挂单物品表
type resaleListingsItem struct {
	resaleListingsItemDo

	ALL               field.Asterisk
	ID                field.Int64  // 主键
	SellerID          field.String // 卖家ID
	ResaleListingsID  field.Int64  // 挂单ID
	Status            field.Int32  // 状态（0-已下架，1-出售中，2-交易中，3-已出售）
	UserItemID        field.String // 出售物品ID
	TradeInfo         field.Field  // 交易信息（云仓 user_items.trade_info）
	SalePrice         field.Int64  // 单价(分)
	ItemID            field.String // 商品ID
	BuyerID           field.String // 购买用户ID
	ResaleOrderID     field.Int64  // 转卖订单ID
	ResaleOrderItemID field.Int64  // 转卖订单详情ID
	Fee               field.Int64  // 手续费
	TradeAt           field.Time   // 成交时间
	CreatedAt         field.Time   // 创建时间
	UpdatedAt         field.Time   // 更新时间
	IsDel             field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (r resaleListingsItem) Table(newTableName string) *resaleListingsItem {
	r.resaleListingsItemDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r resaleListingsItem) As(alias string) *resaleListingsItem {
	r.resaleListingsItemDo.DO = *(r.resaleListingsItemDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *resaleListingsItem) updateTableName(table string) *resaleListingsItem {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt64(table, "id")
	r.SellerID = field.NewString(table, "seller_id")
	r.ResaleListingsID = field.NewInt64(table, "resale_listings_id")
	r.Status = field.NewInt32(table, "status")
	r.UserItemID = field.NewString(table, "user_item_id")
	r.TradeInfo = field.NewField(table, "trade_info")
	r.SalePrice = field.NewInt64(table, "sale_price")
	r.ItemID = field.NewString(table, "item_id")
	r.BuyerID = field.NewString(table, "buyer_id")
	r.ResaleOrderID = field.NewInt64(table, "resale_order_id")
	r.ResaleOrderItemID = field.NewInt64(table, "resale_order_item_id")
	r.Fee = field.NewInt64(table, "fee")
	r.TradeAt = field.NewTime(table, "trade_at")
	r.CreatedAt = field.NewTime(table, "created_at")
	r.UpdatedAt = field.NewTime(table, "updated_at")
	r.IsDel = field.NewField(table, "is_del")

	r.fillFieldMap()

	return r
}

func (r *resaleListingsItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *resaleListingsItem) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 16)
	r.fieldMap["id"] = r.ID
	r.fieldMap["seller_id"] = r.SellerID
	r.fieldMap["resale_listings_id"] = r.ResaleListingsID
	r.fieldMap["status"] = r.Status
	r.fieldMap["user_item_id"] = r.UserItemID
	r.fieldMap["trade_info"] = r.TradeInfo
	r.fieldMap["sale_price"] = r.SalePrice
	r.fieldMap["item_id"] = r.ItemID
	r.fieldMap["buyer_id"] = r.BuyerID
	r.fieldMap["resale_order_id"] = r.ResaleOrderID
	r.fieldMap["resale_order_item_id"] = r.ResaleOrderItemID
	r.fieldMap["fee"] = r.Fee
	r.fieldMap["trade_at"] = r.TradeAt
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
	r.fieldMap["is_del"] = r.IsDel
}

func (r resaleListingsItem) clone(db *gorm.DB) resaleListingsItem {
	r.resaleListingsItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r resaleListingsItem) replaceDB(db *gorm.DB) resaleListingsItem {
	r.resaleListingsItemDo.ReplaceDB(db)
	return r
}

type resaleListingsItemDo struct{ gen.DO }

type IResaleListingsItemDo interface {
	gen.SubQuery
	Debug() IResaleListingsItemDo
	WithContext(ctx context.Context) IResaleListingsItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IResaleListingsItemDo
	WriteDB() IResaleListingsItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IResaleListingsItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IResaleListingsItemDo
	Not(conds ...gen.Condition) IResaleListingsItemDo
	Or(conds ...gen.Condition) IResaleListingsItemDo
	Select(conds ...field.Expr) IResaleListingsItemDo
	Where(conds ...gen.Condition) IResaleListingsItemDo
	Order(conds ...field.Expr) IResaleListingsItemDo
	Distinct(cols ...field.Expr) IResaleListingsItemDo
	Omit(cols ...field.Expr) IResaleListingsItemDo
	Join(table schema.Tabler, on ...field.Expr) IResaleListingsItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IResaleListingsItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IResaleListingsItemDo
	Group(cols ...field.Expr) IResaleListingsItemDo
	Having(conds ...gen.Condition) IResaleListingsItemDo
	Limit(limit int) IResaleListingsItemDo
	Offset(offset int) IResaleListingsItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleListingsItemDo
	Unscoped() IResaleListingsItemDo
	Create(values ...*model.ResaleListingsItem) error
	CreateInBatches(values []*model.ResaleListingsItem, batchSize int) error
	Save(values ...*model.ResaleListingsItem) error
	First() (*model.ResaleListingsItem, error)
	Take() (*model.ResaleListingsItem, error)
	Last() (*model.ResaleListingsItem, error)
	Find() ([]*model.ResaleListingsItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleListingsItem, err error)
	FindInBatches(result *[]*model.ResaleListingsItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ResaleListingsItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IResaleListingsItemDo
	Assign(attrs ...field.AssignExpr) IResaleListingsItemDo
	Joins(fields ...field.RelationField) IResaleListingsItemDo
	Preload(fields ...field.RelationField) IResaleListingsItemDo
	FirstOrInit() (*model.ResaleListingsItem, error)
	FirstOrCreate() (*model.ResaleListingsItem, error)
	FindByPage(offset int, limit int) (result []*model.ResaleListingsItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IResaleListingsItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (r resaleListingsItemDo) Debug() IResaleListingsItemDo {
	return r.withDO(r.DO.Debug())
}

func (r resaleListingsItemDo) WithContext(ctx context.Context) IResaleListingsItemDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r resaleListingsItemDo) ReadDB() IResaleListingsItemDo {
	return r.Clauses(dbresolver.Read)
}

func (r resaleListingsItemDo) WriteDB() IResaleListingsItemDo {
	return r.Clauses(dbresolver.Write)
}

func (r resaleListingsItemDo) Session(config *gorm.Session) IResaleListingsItemDo {
	return r.withDO(r.DO.Session(config))
}

func (r resaleListingsItemDo) Clauses(conds ...clause.Expression) IResaleListingsItemDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r resaleListingsItemDo) Returning(value interface{}, columns ...string) IResaleListingsItemDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r resaleListingsItemDo) Not(conds ...gen.Condition) IResaleListingsItemDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r resaleListingsItemDo) Or(conds ...gen.Condition) IResaleListingsItemDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r resaleListingsItemDo) Select(conds ...field.Expr) IResaleListingsItemDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r resaleListingsItemDo) Where(conds ...gen.Condition) IResaleListingsItemDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r resaleListingsItemDo) Order(conds ...field.Expr) IResaleListingsItemDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r resaleListingsItemDo) Distinct(cols ...field.Expr) IResaleListingsItemDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r resaleListingsItemDo) Omit(cols ...field.Expr) IResaleListingsItemDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r resaleListingsItemDo) Join(table schema.Tabler, on ...field.Expr) IResaleListingsItemDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r resaleListingsItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IResaleListingsItemDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r resaleListingsItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IResaleListingsItemDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r resaleListingsItemDo) Group(cols ...field.Expr) IResaleListingsItemDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r resaleListingsItemDo) Having(conds ...gen.Condition) IResaleListingsItemDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r resaleListingsItemDo) Limit(limit int) IResaleListingsItemDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r resaleListingsItemDo) Offset(offset int) IResaleListingsItemDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r resaleListingsItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleListingsItemDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r resaleListingsItemDo) Unscoped() IResaleListingsItemDo {
	return r.withDO(r.DO.Unscoped())
}

func (r resaleListingsItemDo) Create(values ...*model.ResaleListingsItem) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r resaleListingsItemDo) CreateInBatches(values []*model.ResaleListingsItem, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r resaleListingsItemDo) Save(values ...*model.ResaleListingsItem) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r resaleListingsItemDo) First() (*model.ResaleListingsItem, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListingsItem), nil
	}
}

func (r resaleListingsItemDo) Take() (*model.ResaleListingsItem, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListingsItem), nil
	}
}

func (r resaleListingsItemDo) Last() (*model.ResaleListingsItem, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListingsItem), nil
	}
}

func (r resaleListingsItemDo) Find() ([]*model.ResaleListingsItem, error) {
	result, err := r.DO.Find()
	return result.([]*model.ResaleListingsItem), err
}

func (r resaleListingsItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleListingsItem, err error) {
	buf := make([]*model.ResaleListingsItem, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r resaleListingsItemDo) FindInBatches(result *[]*model.ResaleListingsItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r resaleListingsItemDo) Attrs(attrs ...field.AssignExpr) IResaleListingsItemDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r resaleListingsItemDo) Assign(attrs ...field.AssignExpr) IResaleListingsItemDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r resaleListingsItemDo) Joins(fields ...field.RelationField) IResaleListingsItemDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r resaleListingsItemDo) Preload(fields ...field.RelationField) IResaleListingsItemDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r resaleListingsItemDo) FirstOrInit() (*model.ResaleListingsItem, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListingsItem), nil
	}
}

func (r resaleListingsItemDo) FirstOrCreate() (*model.ResaleListingsItem, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListingsItem), nil
	}
}

func (r resaleListingsItemDo) FindByPage(offset int, limit int) (result []*model.ResaleListingsItem, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r resaleListingsItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r resaleListingsItemDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r resaleListingsItemDo) Delete(models ...*model.ResaleListingsItem) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *resaleListingsItemDo) withDO(do gen.Dao) *resaleListingsItemDo {
	r.DO = *do.(*gen.DO)
	return r
}
