// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameHomeFeedStats = "home_feed_stats"

// HomeFeedStats 商城首页统计表
type HomeFeedStats struct {
	ID         int64     `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                   // 主键
	HomeFeedID int64     `gorm:"column:home_feed_id;type:bigint;not null;comment:首页ID" json:"home_feed_id"`                               // 首页ID
	ItemID     string    `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                    // 商品ID
	WishCount  int32     `gorm:"column:wish_count;type:int;not null;comment:想要数量" json:"wish_count"`                                      // 想要数量
	ViewCount  int32     `gorm:"column:view_count;type:int;not null;comment:浏览数量" json:"view_count"`                                      // 浏览数量
	UpdatedAt  time.Time `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName HomeFeedStats's table name
func (*HomeFeedStats) TableName() string {
	return TableNameHomeFeedStats
}
