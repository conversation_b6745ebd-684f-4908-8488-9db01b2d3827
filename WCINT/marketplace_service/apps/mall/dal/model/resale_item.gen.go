// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameResaleItem = "resale_item"

// ResaleItem 转卖商品表
type ResaleItem struct {
	ID                 int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                                      // 主键
	Status             int32                 `gorm:"column:status;type:tinyint;not null;comment:状态（0-待上架，1-已上架，2-已下架）" json:"status"`                                            // 状态（0-待上架，1-已上架，2-已下架）
	ItemID             string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                                       // 商品ID
	SkuID              string                `gorm:"column:sku_id;type:varchar(32);not null;comment:商品 sku_id" json:"sku_id"`                                                    // 商品 sku_id
	ItemName           string                `gorm:"column:item_name;type:varchar(255);not null;comment:商品名称" json:"item_name"`                                                  // 商品名称
	ItemSpecs          string                `gorm:"column:item_specs;type:varchar(255);not null;comment:商品规格" json:"item_specs"`                                                // 商品规格
	ItemIconURL        string                `gorm:"column:item_icon_url;type:varchar(255);comment:商品主图" json:"item_icon_url"`                                                   // 商品主图
	IPID               string                `gorm:"column:ip_id;type:varchar(32);comment:商品 ip_id" json:"ip_id"`                                                                // 商品 ip_id
	TrademarkID        string                `gorm:"column:trademark_id;type:varchar(32);comment:商品品牌id" json:"trademark_id"`                                                    // 商品品牌id
	CategoryID         string                `gorm:"column:category_id;type:varchar(32);comment:商品分类id" json:"category_id"`                                                      // 商品分类id
	MarketPrice        int64                 `gorm:"column:market_price;type:bigint;not null;comment:市场公允价（分）" json:"market_price"`                                              // 市场公允价（分）
	ResaleStatus       int32                 `gorm:"column:resale_status;type:tinyint;not null;default:1;comment:转卖状态（0-关闭, 1-开启）" json:"resale_status"`                         // 转卖状态（0-关闭, 1-开启）
	TradeFrequencyType int32                 `gorm:"column:trade_frequency_type;type:tinyint;not null;default:1;comment:交易频次类型（1-同步全局标准, 2-特定交易频次）" json:"trade_frequency_type"` // 交易频次类型（1-同步全局标准, 2-特定交易频次）
	IntervalMinutes    *int32                `gorm:"column:interval_minutes;type:int;comment:交易间隔（分）" json:"interval_minutes"`                                                   // 交易间隔（分）
	IsCustomPriceLimit int32                 `gorm:"column:is_custom_price_limit;type:tinyint;not null;comment:是否使用特定限价（0-关闭, 1-开启）" json:"is_custom_price_limit"`               // 是否使用特定限价（0-关闭, 1-开启）
	MinPriceRatio      *int32                `gorm:"column:min_price_ratio;type:tinyint;comment:最低售价比例（基于市场公允进价）" json:"min_price_ratio"`                                        // 最低售价比例（基于市场公允进价）
	MaxPriceRatio      *int32                `gorm:"column:max_price_ratio;type:tinyint;comment:最高售价比例（基于历史最高成交价）" json:"max_price_ratio"`                                       // 最高售价比例（基于历史最高成交价）
	ShelfTime          *time.Time            `gorm:"column:shelf_time;type:datetime(3);comment:上架时间" json:"shelf_time"`                                                          // 上架时间
	Priority           int32                 `gorm:"column:priority;type:int;not null;comment:优先级" json:"priority"`                                                              // 优先级
	CreatedBy          string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                                           // 创建人
	CreatedAt          time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`                    // 创建时间
	UpdatedBy          string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                                           // 更新人
	UpdatedAt          time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`                    // 更新时间
	IsDel              soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`                           // 是否删除【0->未删除; 1->删除】
}

// TableName ResaleItem's table name
func (*ResaleItem) TableName() string {
	return TableNameResaleItem
}
