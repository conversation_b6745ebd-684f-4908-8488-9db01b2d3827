// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameHomeFeed = "home_feed"

// HomeFeed 商城首页表
type HomeFeed struct {
	ID               int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                   // 主键
	Status           int32                 `gorm:"column:status;type:tinyint;not null;comment:状态" json:"status"`                                            // 状态
	ItemMallStatus   int32                 `gorm:"column:item_mall_status;type:tinyint;not null;comment:商品直购状态" json:"item_mall_status"`                    // 商品直购状态
	ItemResaleStatus int32                 `gorm:"column:item_resale_status;type:tinyint;not null;comment:商品转卖状态" json:"item_resale_status"`                // 商品转卖状态
	ItemID           string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                    // 商品ID
	SkuID            string                `gorm:"column:sku_id;type:varchar(32);not null;comment:商品 sku_id" json:"sku_id"`                                 // 商品 sku_id
	IPID             string                `gorm:"column:ip_id;type:varchar(32);not null;comment:商品 ip_id" json:"ip_id"`                                    // 商品 ip_id
	ItemName         string                `gorm:"column:item_name;type:varchar(255);not null;comment:商品名称" json:"item_name"`                               // 商品名称
	ItemSpecs        string                `gorm:"column:item_specs;type:varchar(255);not null;comment:商品属性" json:"item_specs"`                             // 商品属性
	ItemIconURL      string                `gorm:"column:item_icon_url;type:varchar(255);not null;comment:商品主图" json:"item_icon_url"`                       // 商品主图
	LastTradeTime    time.Time             `gorm:"column:last_trade_time;type:datetime(3);not null;comment:最后交易时间" json:"last_trade_time"`                  // 最后交易时间
	ResaleCount      int32                 `gorm:"column:resale_count;type:tinyint;not null;comment:转卖数量" json:"resale_count"`                              // 转卖数量
	SalePrice        int64                 `gorm:"column:sale_price;type:bigint;not null;comment:售价（分）" json:"sale_price"`                                  // 售价（分）
	Priority         int32                 `gorm:"column:priority;type:int;not null;comment:优先级" json:"priority"`                                           // 优先级
	SalesVolume      int32                 `gorm:"column:sales_volume;type:int;not null;comment:销量（直购+转售）" json:"sales_volume"`                             // 销量（直购+转售）
	CreatedAt        time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt        time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel            soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
	HomeFeedStats    HomeFeedStats         `gorm:"foreignKey:home_feed_id" json:"home_feed_stats"`
}

// TableName HomeFeed's table name
func (*HomeFeed) TableName() string {
	return TableNameHomeFeed
}
