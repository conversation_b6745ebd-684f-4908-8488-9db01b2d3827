package constant

import "fmt"

const (
	// IpInfo ip信息
	IpInfo = "marketplace_service:ip:%v"
	// ItemClassifyInfo 商品分类信息
	ItemClassifyInfo = "marketplace_service:item_classify:%v"
	// TrademarksInfo 商品品牌信息
	TrademarksInfo = "marketplace_service:trademarks:%v"
	// SteamItemInfo 商品信息
	SteamItemInfo = "marketplace_service:steam_item_info:%v"
	// UserDetail 用户详情
	UserDetail = "marketplace_service:user_detail:%v"
	// MallItemListKey 直购商品列表
	MallItemListKey = "marketplace_service:mall:mall_item_list"
	// MallItemStockKey 直购商品库存Key
	MallItemStockKey = "marketplace_service:mall:item_stock:%v"
	// UserBuyCountKey 用户购买次数Key
	UserBuyCountKey = "marketplace_service:mall:user_buy_count:%v:%v"
	// HomeFeedListKey 首页商品列表Key
	HomeFeedListKey = "marketplace_service:home_feed:home_feed_list"
	// HomeFeedViewCountKey 暂存商品浏览次数Key
	HomeFeedViewCountKey = "marketplace_service:home_feed:view_count:%v"
	// CommonConfigKey 通用配置Key
	CommonConfigKey = "app_service:config:%v"
)

func GetSteamItemInfoKey(itemId string) string {
	return fmt.Sprintf(SteamItemInfo, itemId)
}

func GetUserDetailKey(userId string) string {
	return fmt.Sprintf(UserDetail, userId)
}

func GetMallItemListKey() string {
	return MallItemListKey
}

func GetMallItemStockKey(mallItemId int64) string {
	return fmt.Sprintf(MallItemStockKey, mallItemId)
}

func GetUserBuyCountKey(mallItemId int64, userId string) string {
	return fmt.Sprintf(UserBuyCountKey, mallItemId, userId)
}

func GetIpInfoKey(ipId string) string {
	return fmt.Sprintf(IpInfo, ipId)
}

func GetItemClassifyInfoKey(itemClassifyId string) string {
	return fmt.Sprintf(ItemClassifyInfo, itemClassifyId)
}

func GetTrademarkInfoKey(trademarkId string) string {
	return fmt.Sprintf(TrademarksInfo, trademarkId)
}

func GetHomeFeedListKey() string {
	return HomeFeedListKey
}

func GetItemViewCountKey(itemId string) string {
	return fmt.Sprintf(HomeFeedViewCountKey, itemId)
}

func GetConfigKey(key string) string {
	return fmt.Sprintf(CommonConfigKey, key)
}
