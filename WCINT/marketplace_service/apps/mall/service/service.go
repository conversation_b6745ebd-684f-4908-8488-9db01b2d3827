package service

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/pkg/errors"
	"go.opentelemetry.io/otel/trace"
	"marketplace_service/pkg/middleware/g/auth"
)

// Service 业务服务
// 传递上下文
type Service struct {
	ctx context.Context
}

func New(ctx context.Context) *Service {
	return &Service{
		ctx: ctx,
	}
}

func (s *Service) NewContextWithSpanContext(parent context.Context) context.Context {
	spanContext := trace.SpanContextFromContext(parent)
	return trace.ContextWithSpanContext(context.Background(), spanContext)
}

// GetAdminId 获取当前登录用户id
func (s *Service) GetAdminId() string {
	info, ok := auth.GetAdminFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return ""
	}
	return info.Id
}

// GetUserId 获取当前登录用户id
func (s *Service) GetUserId() string {
	if s.ctx.Value("Authorization").(string) == "" {
		return ""
	}
	info, ok := auth.GetUserFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return ""
	}
	return info.Id
}

// GetUserInfo 获取当前登录用户信息
func (s *Service) GetUserInfo() (*pat.CheckUserJwtUserInfo, error) {
	if s.ctx.Value("Authorization").(string) == "" {
		return nil, errors.New("用户未登录")
	}

	info, ok := auth.GetUserFromCtx(s.ctx)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	return info, nil
}
