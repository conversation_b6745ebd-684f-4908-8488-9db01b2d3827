package service

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/locker"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/apps/mall/service/logic/config"
	"marketplace_service/apps/mall/service/warn"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/tmt"
	"marketplace_service/third_party/yc_open"
	ycdefine "marketplace_service/third_party/yc_open/define"
	"strings"
	"time"
)

// GetWebResaleUserItemListByItem 获取转卖商品持仓商品列表
func (s *Service) GetWebResaleUserItemListByItem(req *define.GetWebResaleUserItemListByItemReq) (*define.GetWebResaleUserItemListByItemResp, error) {
	openUserInfo, err := tmt.QueryUserOpenById(s.ctx, s.GetUserId())
	if err != nil {
		return nil, err
	}
	if openUserInfo == nil || openUserInfo.OpenInfo == nil {
		return nil, errors.New("请先绑定云仓")
	}
	openUserID := openUserInfo.OpenInfo.OpenUserId
	ycReq := &yc_open.QueryResaleUserItemListByItemLevelReq{
		Pagination: req.Pagination,
		OpenUserID: openUserID,
		ItemName:   req.ItemName,
	}
	ycData, err := yc_open.QueryResaleUserItemListByItemLevel(s.ctx, ycReq)
	if err != nil {
		return nil, err
	}
	var itemIDs []string
	ycList := ycData.List
	for _, item := range ycList {
		itemIDs = append(itemIDs, item.ItemID)
	}
	list := make([]*define.GetWebResaleUserItemListByItemData, 0)
	if len(itemIDs) > 0 {
		riSchema := repo.GetQuery().ResaleItem
		qw := search.NewWrapper().Where(riSchema.ItemID.In(itemIDs...))
		resaleItems, err := repo.NewResaleItemRepo(riSchema.WithContext(s.ctx)).SelectList(qw)
		if err != nil {
			return nil, err
		}
		resaleItemMap := make(map[string]*model.ResaleItem)
		for _, item := range resaleItems {
			resaleItemMap[item.ItemID] = item
		}
		soldOutMap := logic.GetItemSoldOutMap(s.ctx, itemIDs)
		// 全局转卖配置
		resaleConfig, err := logic.GetResaleStandardConfig(s.ctx)
		if err != nil {
			return nil, err
		}
		for _, ycItem := range ycList {
			resaleItem, ok := resaleItemMap[ycItem.ItemID]
			if !ok {
				return nil, errors.New(fmt.Sprintf("转卖商品 %s 不存在", ycItem.ItemID))
			}
			resaleStatus := resaleItem.ResaleStatus
			if resaleConfig.ResaleStatus == enums.ConfSwitchOff.Val() {
				// 全局关闭优先级更高
				resaleStatus = resaleConfig.ResaleStatus
			}
			listItem := &define.GetWebResaleUserItemListByItemData{
				ID:           resaleItem.ID,
				ItemId:       resaleItem.ItemID,
				ItemName:     resaleItem.ItemName,
				ItemSpecs:    resaleItem.ItemSpecs,
				ItemIconURL:  resaleItem.ItemIconURL,
				SoldOut:      soldOutMap[resaleItem.ItemID],
				ResaleStatus: resaleStatus,
				HoldQty:      ycItem.Count,
			}
			list = append(list, listItem)
		}
	}

	return &define.GetWebResaleUserItemListByItemResp{
		List:  list,
		Total: ycData.Total,
	}, nil
}

// GetWebResaleUserItemListByUserItem 获取某个转卖商品的持仓列表
func (s *Service) GetWebResaleUserItemListByUserItem(req *define.GetWebResaleUserItemListByUserItemReq) (*define.GetWebResaleUserItemListByUserItemResp, error) {
	userID := s.GetUserId()
	if userID == "" {
		return nil, errors.New("请先登录账号")
	}
	riSchema := repo.GetQuery().ResaleItem
	wrapper := search.NewWrapper().Where(riSchema.ItemID.In(req.ItemID))
	resaleItem, err := repo.NewResaleItemRepo(riSchema.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if resaleItem == nil {
		// 该商品未开启转卖，返回空数据
		resp := &define.GetWebResaleUserItemListByUserItemResp{
			List: []*define.GetWebResaleUserItemListByUserItemData{},
		}
		return resp, nil
	}
	openUserInfo, err := tmt.QueryUserOpenById(s.ctx, s.GetUserId())
	if err != nil {
		return nil, err
	}
	if openUserInfo == nil || openUserInfo.OpenInfo == nil {
		return nil, errors.New("请先绑定云仓")
	}
	openUserID := openUserInfo.OpenInfo.OpenUserId
	ycReq := &yc_open.QueryResaleUserItemListByUserItemLevelReq{
		Pagination: req.Pagination,
		OpenUserID: openUserID,
		ItemID:     req.ItemID,
	}
	ycUserItemResult, err := yc_open.QueryResaleUserItemListByUserItemLevel(s.ctx, ycReq)
	if err != nil {
		return nil, err
	}
	ycUserItemList := ycUserItemResult.List
	var ycUserItemIDs []string
	for _, item := range ycUserItemList {
		ycUserItemIDs = append(ycUserItemIDs, item.ID)
	}
	resaleListingsItemMap := map[string]*model.ResaleListingsItem{}
	if len(ycUserItemIDs) > 0 {
		rliSchema := repo.GetQuery().ResaleListingsItem
		qw := search.NewWrapper().
			Where(rliSchema.SellerID.Eq(userID)).
			Where(rliSchema.UserItemID.In(ycUserItemIDs...)).
			Where(rliSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val()))
		resaleListingsItems, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(s.ctx)).SelectList(qw)
		if err != nil {
			return nil, err
		}
		for _, item := range resaleListingsItems {
			resaleListingsItemMap[item.UserItemID] = item
		}
	}

	// 全局配置
	resaleConfig, err := logic.GetResaleStandardConfig(s.ctx)
	if err != nil {
		return nil, err
	}
	// 转卖开关配置
	resaleStatus := resaleItem.ResaleStatus
	if resaleConfig.ResaleStatus == enums.ConfSwitchOff.Val() {
		// 全局关闭，优先取全局的配置
		resaleStatus = resaleConfig.ResaleStatus
	}

	// 获取交易频次配置
	var intervalMinutes int32
	if resaleItem.TradeFrequencyType == enums.ResaleItemTradeFrequencyTypeSpecific.Val() {
		// 商品自身的交易频次配置
		if resaleItem.IntervalMinutes != nil {
			intervalMinutes = *resaleItem.IntervalMinutes
		}
	} else {
		intervalMinutes = resaleConfig.IntervalMinutes
	}

	list := make([]*define.GetWebResaleUserItemListByUserItemData, 0)
	statusMismatchMsgList := make([]string, 0)
	statusMismatchCacheKeyPrefix := "marketplace_service:resale:user_item_status_mismatch:"
	for _, item := range ycUserItemList {
		var countdownSecond int32
		if item.Extends.ReceiveType == ycdefine.UserItemReceiveTypeWcReSell.Val() {
			// 转卖商品买入的才有交易频次限制
			countdownSecond = logic.GetResaleItemCountDownSecond(item.CreatedAt, intervalMinutes)
		}
		buyTime := item.CreatedAt
		if item.TradeInfo.BuyTime != nil {
			buyTime = *item.TradeInfo.BuyTime
		}
		listItem := &define.GetWebResaleUserItemListByUserItemData{
			ID:              item.ID,
			ItemID:          item.ItemID,
			ItemIconURL:     resaleItem.ItemIconURL,
			BuyTime:         buyTime,
			BuyPrice:        item.TradeInfo.BuyPrice,
			Status:          item.Status,
			CountdownSecond: countdownSecond,
			PreSale:         item.TradeInfo.SaleMode == 2 && item.TradeInfo.DeliveryTime != nil,
			DeliveryTime:    item.TradeInfo.DeliveryTime,
			ResaleStatus:    resaleStatus,
			IntervalMinutes: intervalMinutes,
		}
		mismatchMsg := ""
		if rlItem, ok := resaleListingsItemMap[item.ID]; ok {
			listItem.ResaleListingsItemID = rlItem.ID
			listItem.SalePrice = rlItem.SalePrice
			if item.Status == ycdefine.UserItemStatusOwned.Val() {
				// 云仓物品状态为 持有中，转卖挂单物品为 出售中
				mismatchMsg = fmt.Sprintf("持仓物品 %s 状态不匹配，云仓物品状态为 持有中，转卖挂单物品为 出售中", item.ID)
			}
		} else if item.Status == ycdefine.UserItemStatusForSale.Val() {
			// 云仓物品状态为 出售中，转卖无挂单
			mismatchMsg = fmt.Sprintf("持仓物品 %s 状态不匹配，云仓物品状态为 出售中，转卖无挂单", item.ID)
		}
		list = append(list, listItem)

		if mismatchMsg != "" {
			cacheKey := statusMismatchCacheKeyPrefix + item.ID
			ex, _ := global.REDIS.Exists(s.ctx, cacheKey).Result()
			if ex != 1 {
				statusMismatchMsgList = append(statusMismatchMsgList, mismatchMsg)
				_ = global.REDIS.Set(s.ctx, cacheKey, 1, time.Hour).Err()
			}
		}
	}

	if len(statusMismatchMsgList) > 0 {
		msg := strings.Join(statusMismatchMsgList, "\n")
		warn.SendDefaultWarnMsgWithMentionOption(s.ctx, "转卖物品持仓状态不匹配", msg, &warn.MsgMentionOption{
			MentionedList: []string{"luoxiancheng"},
		})
	}

	return &define.GetWebResaleUserItemListByUserItemResp{
		List:    list,
		HasMore: ycUserItemResult.HasMore,
	}, nil
}

// CancelResaleUserItemFromWeb 取消出售
func (s *Service) CancelResaleUserItemFromWeb(req *define.CancelResaleUserItemFromWebReq) (*define.CancelResaleUserItemFromWebResp, error) {
	// 加锁
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewResaleListingsLock(req.ID, locker.ResaleListingsItemCancel)))
	if !l.Lock(s.ctx) {
		return nil, response.TooManyRequestErr
	}
	defer l.UnLock(s.ctx)

	logPrefix := "CancelResaleUserItemFromWeb"
	userID := s.GetUserId()
	if userID == "" {
		return nil, errors.New("请先登录账号")
	}
	rliSchema := repo.GetQuery().ResaleListingsItem
	wrapper := search.NewWrapper().
		Where(rliSchema.SellerID.Eq(userID)).
		Where(rliSchema.UserItemID.Eq(req.ID)).
		Where(rliSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val()))
	rlItem, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if rlItem == nil {
		return nil, define.MS200024Err
	}

	err = repo.ExecGenTx(s.ctx, func(ctx context.Context) error {
		// 本地改状态
		rliTxSchema := repo.Query(ctx).ResaleListingsItem
		updateParams := map[string]interface{}{
			"status": enums.ResaleListingsItemStatusDown.Val(), // 下架
		}
		updateWrapper := search.NewWrapper().Where(rliTxSchema.ID.Eq(rlItem.ID))
		err = repo.NewResaleListingsItemRepo(rliTxSchema.WithContext(ctx)).UpdateField(updateParams, updateWrapper)
		if err != nil {
			return err
		}
		// 获取主单数据
		statusList := []int32{enums.ResaleListingsItemStatusOnSale.Val(), enums.ResaleListingsItemStatusTrading.Val()}
		countWrapper := search.NewWrapper().
			Where(rliTxSchema.ResaleListingsID.Eq(rlItem.ResaleListingsID)).
			Where(rliTxSchema.Status.In(statusList...)).
			Where(rliTxSchema.ID.Neq(rlItem.ID))
		itemCount, err := repo.NewResaleListingsItemRepo(rliTxSchema.WithContext(s.ctx)).Count(countWrapper)
		if err != nil {
			return err
		}
		rlUpdateParams := map[string]interface{}{
			"listing_quantity": gorm.Expr("listing_quantity - ?", 1),
		}
		if itemCount == 0 {
			// 没有其他有效物品了，主单下架
			rlUpdateParams["status"] = enums.ResaleListingsStatusDown.Val()
		}
		rlTxSchema := repo.Query(ctx).ResaleListings
		rlWrapper := search.NewWrapper().Where(rlTxSchema.ID.Eq(rlItem.ResaleListingsID))
		err = repo.NewResaleListingsRepo(rlTxSchema.WithContext(ctx)).UpdateField(rlUpdateParams, rlWrapper)
		if err != nil {
			return err
		}

		// 更改云仓状态：出售中 -> 持有中
		userItemIDs := []string{req.ID}
		updateStatusReq := &yc_open.UpdateUserItemStatusReq{
			UserItemIDs:    userItemIDs,
			OriginalStatus: ycdefine.UserItemStatusForSale,
			UpdateStatus:   ycdefine.UserItemStatusOwned,
		}
		err = yc_open.UpdateUserItemStatus(s.ctx, updateStatusReq)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	// 更新统计数据
	err = logic.HandleResaleListingsCanceled(s.ctx, rlItem.ItemID, 1)
	if err != nil {
		log.Ctx(s.ctx).Errorf("%s HandleResaleListingsCanceled error: %v", logPrefix, err)
	}

	return &define.CancelResaleUserItemFromWebResp{
		ID:         req.ID,
		CanceledAt: utils.Now(),
	}, nil
}

// GetWebResaleUserItemTradeInfo // 获取持仓商品交易信息
func (s *Service) GetWebResaleUserItemTradeInfo(req *define.GetWebResaleUserItemTradeInfoReq) (*define.GetWebResaleUserItemTradeInfoResp, error) {
	itemID := req.ItemID
	riSchema := repo.GetQuery().ResaleItem
	wrapper := search.NewWrapper().Where(riSchema.ItemID.Eq(itemID))
	resaleItem, err := repo.NewResaleItemRepo(riSchema.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, err
	}
	// 最低在售
	var minSalePrice int64 = -1
	rliSchema := repo.GetQuery().ResaleListingsItem
	rliWrapper := search.NewWrapper().
		Where(rliSchema.ItemID.Eq(itemID)).
		Where(rliSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())).
		OrderBy(rliSchema.SalePrice.Asc())
	rlItem, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(s.ctx).Limit(1)).SelectList(rliWrapper)
	if err != nil {
		return nil, err
	}
	if len(rlItem) == 0 {
		// 查询闪购价格
		miSchema := repo.GetQuery().MallItem
		miWrapper := search.NewWrapper().
			Where(miSchema.ItemID.Eq(itemID)).
			Where(miSchema.Status.Eq(enums.MallItemStatusUp.Val())) // 已上架
		mallItems, err := repo.NewMallItemRepo(miSchema.WithContext(s.ctx).Limit(1)).SelectList(miWrapper)
		if err != nil {
			return nil, err
		}
		if len(mallItems) > 0 {
			minSalePrice = mallItems[0].SalePrice
		}
	} else {
		minSalePrice = rlItem[0].SalePrice
	}
	// 最新成交价
	var latestSalePrice int64 = -1
	roSchema := repo.GetQuery().ResaleOrder
	roWrapper := search.NewWrapper().
		Where(roSchema.ItemID.Eq(itemID)).
		Where(roSchema.Status.Eq(enums.ResaleOrderStatusCompleted.Val())).
		OrderBy(roSchema.CreatedAt.Desc())
	resaleOrders, err := repo.NewResaleOrderRepo(roSchema.WithContext(s.ctx).Limit(1)).SelectList(roWrapper)
	if err != nil {
		return nil, err
	}
	if len(resaleOrders) == 0 {
		// 查询闪购最后一笔订单
		toSchema := repo.GetQuery().TradeOrder
		toiSchema := repo.GetQuery().TradeOrderItem
		var toiList []*model.TradeOrderItem
		err = toiSchema.WithContext(s.ctx).
			Select(toiSchema.SalePrice).
			Where(toiSchema.ItemID.Eq(itemID)).
			RightJoin(toSchema, toiSchema.OrderID.EqCol(toSchema.ID), toSchema.OrderStatus.Eq(enums.OrderStatusCompleted.Val())).
			Order(toSchema.CreatedAt.Desc()).
			Limit(1).
			Scan(&toiList)
		if err != nil {
			return nil, err
		}
		if len(toiList) > 0 {
			latestSalePrice = toiList[0].SalePrice
		}
	} else {
		latestSalePrice = resaleOrders[0].SalePrice
	}

	// 转卖配置
	resaleConfig, err := logic.GetResaleItemTradeConfig(s.ctx, resaleItem)
	if err != nil {
		return nil, err
	}

	// 云仓持有计数
	openUserInfo, err := tmt.QueryUserOpenById(s.ctx, s.GetUserId())
	if err != nil {
		return nil, err
	}
	if openUserInfo == nil || openUserInfo.OpenInfo == nil {
		return nil, errors.New("请先绑定云仓")
	}
	openUserID := openUserInfo.OpenInfo.OpenUserId
	ycReq := &yc_open.CountResaleUserItemReq{
		OpenUserID: openUserID,
		ItemIDs:    []string{itemID},
		StatusList: []any{ycdefine.UserItemStatusOwned}, // 只统计持有中的
	}
	if resaleConfig.IntervalMinutes > 0 {
		ycReq.CreatedAtLt = utils.Now().Add(-time.Duration(resaleConfig.IntervalMinutes) * time.Minute).UTC().Format(time.RFC3339)
	}
	ycCountResults, err := yc_open.CountResaleUserItem(s.ctx, ycReq)
	if err != nil {
		return nil, err
	}
	var availableQty int64
	for _, ycCountResult := range ycCountResults {
		if ycCountResult.ItemID == itemID {
			availableQty += ycCountResult.Count
		}
	}

	feeRatio, _ := config.GetInt(s.ctx, constant.ResaleFee, 3)
	return &define.GetWebResaleUserItemTradeInfoResp{
		ItemID:          resaleItem.ItemID,
		ItemName:        resaleItem.ItemName,
		ItemIconURL:     resaleItem.ItemIconURL,
		ItemSpecs:       resaleItem.ItemSpecs,
		MinSalePrice:    minSalePrice,
		LatestSalePrice: latestSalePrice,
		FeeRate:         int32(feeRatio),
		MinLimitPrice:   resaleConfig.MinLimitPrice,
		MaxLimitPrice:   resaleConfig.MaxLimitPrice,
		AvailableQty:    availableQty,
	}, nil
}

// AddResaleUserItemLock 锁定用户物品
func (s *Service) AddResaleUserItemLock(listingsItems []*model.ResaleListingsItem) ([]*redis_locker.Locker, error) {
	userItemIds := make([]string, 0, len(listingsItems))
	for _, listingsItem := range listingsItems {
		userItemIds = append(userItemIds, listingsItem.UserItemID)
	}
	if len(userItemIds) == 0 {
		return nil, nil
	}
	var locks []*redis_locker.Locker
	for _, userItemId := range userItemIds {
		l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewResaleOrderLock(userItemId, locker.ResaleUserItem)))
		if !l.Lock(s.ctx) {
			return locks, response.TooManyRequestErr
		}
		locks = append(locks, l)
	}
	return locks, nil
}
