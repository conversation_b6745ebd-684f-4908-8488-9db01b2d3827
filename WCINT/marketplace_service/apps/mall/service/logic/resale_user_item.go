package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"gorm.io/datatypes"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/locker"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/yc_open"
)

func SyncResaleUserItemsByItem(ctx context.Context, itemID string) error {
	if itemID == "" {
		return nil
	}

	// 加锁
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewResaleListingsLock(itemID, locker.ResaleListingsItemSync)))
	if !l.Lock(ctx) {
		return response.TooManyRequestErr
	}
	defer l.UnLock(ctx)

	// 查询需要更新的物品
	rliSchema := repo.GetQuery().ResaleListingsItem
	rliWrapper := search.NewWrapper().
		Where(rliSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())).
		Where(rliSchema.ItemID.Eq(itemID))
	rlItems, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(ctx)).SelectList(rliWrapper)
	if err != nil {
		return err
	}
	userItemIDs := make([]string, 0)
	idMap := make(map[string]int64)
	for _, rlItem := range rlItems {
		userItemIDs = append(userItemIDs, rlItem.UserItemID)
		idMap[rlItem.UserItemID] = rlItem.ID
	}

	if len(userItemIDs) > 0 {
		chunks := utils.ChunkSlice(userItemIDs, 200)
		for _, chunk := range chunks {
			// 从头云仓获取数据
			ycUserItems, err := yc_open.QueryUserItemsByIDs(ctx, chunk)
			if err != nil {
				log.Ctx(ctx).Errorf("yc_open.QueryUserItemsByIDs failed, chunk: %v, err: %v", chunk, err)
				continue
			}
			for _, ycUserItem := range ycUserItems {
				// 更新数据
				tradeInfo := datatypes.JSON(utils.Obj2JsonStr(ycUserItem.TradeInfo))
				updateParams := map[string]interface{}{
					"trade_info": &tradeInfo,
				}
				updateWrapper := search.NewWrapper().Where(rliSchema.ID.Eq(idMap[ycUserItem.ID]))
				err = repo.NewResaleListingsItemRepo(rliSchema.WithContext(ctx)).UpdateField(updateParams, updateWrapper)
				if err != nil {
					log.Ctx(ctx).Errorf("update resale_listings_item err: %v", err)
					continue
				}
			}
		}
	}

	return nil
}
