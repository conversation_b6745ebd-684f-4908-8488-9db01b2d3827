package logic

import (
	"context"
	"database/sql"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"golang.org/x/sync/errgroup"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/warn"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils/snowflakeutl"
	"marketplace_service/third_party/tmt"
	"sync"
	"time"
)

func SetHomeFeedItemIPID(ctx context.Context, item *model.HomeFeed, itemInfo *tmt.SteamItemInfo) error {
	if len(itemInfo.IpInfo) > 0 {
		ipIDs := make([]string, len(itemInfo.IpInfo))
		for i, info := range itemInfo.IpInfo {
			ipIDs[i] = info.ID
		}
		infos, err := GetIpInfos(ctx, ipIDs...)
		if err != nil {
			return errors.Wrap(err, "获取商品IP信息失败")
		}
		var ipID string
		for _, info := range infos {
			if info.Level == constant.LevelTwo {
				ipID = info.Id
				break
			}
		}
		if ipID == "" {
			ipID = itemInfo.IpInfo[0].ID
		}
		item.IPID = ipID
	}
	return nil
}

// ConcurrentGetItems 并发获取直购商品和转卖商品信息
func ConcurrentGetItems(ctx context.Context, itemId string) (mallItem *model.MallItem, resaleItem *model.ResaleItem, err error) {
	var wg sync.WaitGroup
	var mallItemErr, resaleItemErr error

	wg.Add(2)
	go func() {
		defer wg.Done()
		mi := repo.GetQuery().MallItem
		miWrapper := search.NewWrapper().Where(mi.ItemID.Eq(itemId), mi.Status.Eq(enums.MallItemStatusUp.Val()))
		mallItem, mallItemErr = repo.NewMallItemRepo(mi.WithContext(ctx)).SelectOne(miWrapper)
		if mallItemErr != nil && !errors.Is(mallItemErr, gorm.ErrRecordNotFound) {
			mallItemErr = errors.Wrap(mallItemErr, "查询直购商品失败")
		}
	}()

	go func() {
		defer wg.Done()
		ri := repo.GetQuery().ResaleItem
		riWrapper := search.NewWrapper().Where(ri.ItemID.Eq(itemId))
		resaleItem, resaleItemErr = repo.NewResaleItemRepo(ri.WithContext(ctx)).SelectOne(riWrapper)
		if resaleItemErr != nil && !errors.Is(resaleItemErr, gorm.ErrRecordNotFound) {
			resaleItemErr = errors.Wrap(resaleItemErr, "查询转售商品失败")
		}
	}()

	wg.Wait()

	if mallItemErr != nil && !errors.Is(mallItemErr, gorm.ErrRecordNotFound) {
		return nil, nil, mallItemErr
	}
	if resaleItemErr != nil && !errors.Is(resaleItemErr, gorm.ErrRecordNotFound) {
		return nil, nil, resaleItemErr
	}

	return mallItem, resaleItem, nil
}

// AddHomeFeedCacheOnStatusChange 添加商城首页直购商品缓存
func AddHomeFeedCacheOnStatusChange(ctx context.Context, m *model.HomeFeed) error {
	key := constant.GetHomeFeedListKey()
	switch m.Status {
	case enums.HomeFeedStatusUp.Val():
		lastTradeTimeTimestamp := m.LastTradeTime.UnixMilli()
		score := buildZSetScore(m.Priority, lastTradeTimeTimestamp)
		zMember := &redis.Z{
			Score:  score,
			Member: m.ID,
		}
		if err := global.REDIS.ZAdd(ctx, key, zMember).Err(); err != nil {
			log.Ctx(ctx).Errorf("添加商城首页缓存失败 err:%+v", err)
			return err
		}

	case enums.HomeFeedStatusDown.Val():
		if err := global.REDIS.ZRem(ctx, key, m.ID).Err(); err != nil {
			log.Ctx(ctx).Errorf("删除商城首页缓存失败 err:%+v", err)
			return err
		}
	default:
		return nil
	}
	return nil
}

// CalculateSalePrice 计算价格
// 1、转卖有挂单，且闪购有上架：显示转卖挂单与闪购价中最低的一个，若是显示闪购价，且闪购价有折扣的，则需再显示出原价划线价。（比较时，闪购取折后价进行比较）
// 2、转卖无挂单，且闪购下架的：显示转卖最后成交价。 转卖历史无成交，则显示后台最低售价。
func CalculateSalePrice(ctx context.Context, homeFeed *model.HomeFeed, resaleItem *model.ResaleItem, mallItem *model.MallItem) (int64, error) {
	salePrice := int64(0)
	if mallItem != nil && mallItem.Status == enums.MallItemStatusUp.Val() && mallItem.StartTime.Before(time.Now()) {
		salePrice = CalculateDiscountedPrice(mallItem.SalePrice, mallItem.Discount)
	}
	if resaleItem != nil && resaleItem.Status == enums.ResaleItemStatusUp.Val() && resaleItem.ResaleStatus == enums.ResaleItemResaleStatusOpen.Val() {
		// 获取转卖挂单最小挂单价
		rl := repo.GetQuery().ResaleListings
		warp := search.NewWrapper().Select(rl.ItemID, rl.SalePrice).
			Where(rl.ItemID.Eq(homeFeed.ItemID), rl.Status.Eq(enums.ResaleListingsStatusOnSale.Val())).
			OrderBy(rl.SalePrice)
		listings, err := repo.NewResaleListingsRepo(rl.WithContext(ctx).Limit(1)).SelectOne(warp)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Ctx(ctx).Errorf("获取转卖挂单最小挂单价失败, itemId:%+v,err:%+v", homeFeed.ItemID, err)
			return 0, errors.Wrap(err, "获取转卖挂单最小挂单价失败")
		}
		if listings != nil && (salePrice == 0 || listings.SalePrice < salePrice) {
			salePrice = listings.SalePrice
		}
	}
	if salePrice == 0 {
		// 获取最后成交价
		ro := repo.GetQuery().ResaleOrder
		warp := search.NewWrapper().Select(ro.ItemID, ro.SalePrice).
			Where(ro.ItemID.Eq(homeFeed.ItemID), ro.Status.Eq(enums.ResaleOrderStatusCompleted.Val())).
			OrderBy(ro.FinishedAt.Desc())
		resaleOrder, err := repo.NewResaleOrderRepo(ro.WithContext(ctx).Limit(1)).SelectOne(warp)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Ctx(ctx).Errorf("获取转卖订单最后成交价失败, itemId:%+v,err:%+v", homeFeed.ItemID, err)
			return 0, errors.Wrap(err, "获取转卖订单最后成交价失败")
		}
		if resaleOrder != nil {
			salePrice = resaleOrder.SalePrice
		}
	}
	if salePrice == 0 && resaleItem != nil {
		minPriceRatio := resaleItem.MinPriceRatio
		if resaleItem.IsCustomPriceLimit == 0 {
			standardConfig, err := GetResaleStandardConfig(ctx)
			if err != nil {
				return 0, errors.Wrap(err, "获取转卖标准配置失败")
			}
			minPriceRatio = &standardConfig.MinPriceRatio
		}
		d100 := decimal.NewFromInt32(100)
		salePrice = decimal.NewFromInt(resaleItem.MarketPrice).
			Mul(decimal.NewFromInt32(*minPriceRatio)).
			Div(d100).Ceil().IntPart()
	}
	return salePrice, nil
}

// 将状态计算封装为独立函数
func calculateStatus(mallItem *model.MallItem, resaleItem *model.ResaleItem, resaleStandardConfig *define.ResaleStandardConfig) int32 {
	status := enums.HomeFeedStatusDown.Val()
	if mallItem != nil && mallItem.Status == enums.MallItemStatusUp.Val() && mallItem.StartTime.Before(time.Now()) {
		status = enums.HomeFeedStatusUp.Val()
	}
	if resaleItem != nil && resaleItem.Status == enums.ResaleItemStatusUp.Val() && resaleItem.ResaleStatus == enums.ResaleItemResaleStatusOpen.Val() {
		if resaleStandardConfig.ResaleStatus == enums.ResaleItemResaleStatusOpen.Val() {
			status = enums.HomeFeedStatusUp.Val()
		}
	}
	return status
}

// initSalePrice 初始化售价
func initSalePrice(ctx context.Context, mallItem *model.MallItem, resaleItem *model.ResaleItem) (int64, error) {
	var salePrice int64

	if mallItem != nil && mallItem.Status == enums.MallItemStatusUp.Val() && mallItem.StartTime.Before(time.Now()) {
		salePrice = CalculateDiscountedPrice(mallItem.SalePrice, mallItem.Discount)
	}
	if resaleItem != nil && resaleItem.Status == enums.ResaleItemStatusUp.Val() && salePrice == 0 {
		minPriceRatio := resaleItem.MinPriceRatio
		if resaleItem.IsCustomPriceLimit == 0 {
			standardConfig, err := GetResaleStandardConfig(ctx)
			if err != nil {
				return 0, errors.Wrap(err, "获取转卖标准配置失败")
			}
			minPriceRatio = &standardConfig.MinPriceRatio
		}
		salePrice = resaleItem.MarketPrice * int64(*minPriceRatio) / 100
	}
	return salePrice, nil
}

// initLatestTradeTime 初始化最新成交时间
func initLatestTradeTime(mallItem *model.MallItem, resaleItem *model.ResaleItem) *time.Time {
	var latestTime *time.Time
	if mallItem != nil && mallItem.ShelfTime != nil {
		latestTime = mallItem.ShelfTime
	}
	if resaleItem != nil && resaleItem.ShelfTime != nil && (latestTime == nil || resaleItem.ShelfTime.After(*latestTime)) {
		latestTime = resaleItem.ShelfTime
	}
	return latestTime
}

// setHomeFeedInfo 统一设置状态和优先级
func setHomeFeedInfo(homeFeed *model.HomeFeed, mallItem *model.MallItem, resaleItem *model.ResaleItem, resaleStandardConfig *define.ResaleStandardConfig) {
	homeFeed.ItemMallStatus = 0
	homeFeed.ItemResaleStatus = 0
	homeFeed.Priority = 0

	if mallItem != nil && mallItem.Status == enums.MallItemStatusUp.Val() && mallItem.StartTime.Before(time.Now()) {
		homeFeed.Priority = mallItem.Priority
		homeFeed.ItemMallStatus = 1
	}

	if resaleItem != nil && resaleItem.Status == enums.ResaleItemStatusUp.Val() &&
		resaleItem.ResaleStatus == enums.ResaleItemResaleStatusOpen.Val() &&
		resaleStandardConfig.ResaleStatus == enums.ResaleItemResaleStatusOpen.Val() {
		homeFeed.ItemResaleStatus = 1
		if homeFeed.Priority == 0 {
			homeFeed.Priority = resaleItem.Priority
		}
	} else {
		homeFeed.ResaleCount = 0
	}
}

// handleCreateHomeFeed 创建新的 HomeFeed
func handleCreateHomeFeed(
	tx context.Context,
	itemId string,
	itemInfo *tmt.SteamItemInfo,
	mallItem *model.MallItem,
	resaleItem *model.ResaleItem,
	standardConfig *define.ResaleStandardConfig,
) error {
	// 计算状态
	status := calculateStatus(mallItem, resaleItem, standardConfig)
	salePrice, err := initSalePrice(tx, mallItem, resaleItem)
	if err != nil {
		return errors.Wrap(err, "初始化售价失败")
	}
	lastTradeTime := initLatestTradeTime(mallItem, resaleItem)

	createHomeFeed := &model.HomeFeed{
		ID:            snowflakeutl.GenerateID(),
		ItemID:        itemId,
		SkuID:         itemInfo.SkuId,
		ItemName:      itemInfo.ItemName,
		ItemSpecs:     itemInfo.Specs,
		ItemIconURL:   itemInfo.IconUrl,
		Status:        status,
		SalePrice:     salePrice,
		LastTradeTime: *lastTradeTime,
		ResaleCount:   0,
		SalesVolume:   0,
		HomeFeedStats: model.HomeFeedStats{
			ID:        snowflakeutl.GenerateID(),
			ItemID:    itemId,
			WishCount: 0,
			ViewCount: 0,
		},
	}
	// 设置商品状态和优先级
	setHomeFeedInfo(createHomeFeed, mallItem, resaleItem, standardConfig)
	// 设置 IPID
	if err := SetHomeFeedItemIPID(tx, createHomeFeed, itemInfo); err != nil {
		return errors.Wrap(err, "设置 IPID 失败")
	}
	hf := repo.Query(tx).HomeFeed
	if err := repo.NewHomeFeedRepo(hf.WithContext(tx)).Save(createHomeFeed); err != nil {
		return errors.Wrap(err, "保存首页商品失败")
	}
	// 更新缓存
	if err := AddHomeFeedCacheOnStatusChange(tx, createHomeFeed); err != nil {
		return errors.Wrap(err, "更新首页商品缓存失败")
	}
	return nil
}

// handleUpdateHomeFeed 更新已有的 HomeFeed
func handleUpdateHomeFeed(
	tx context.Context,
	homeFeed *model.HomeFeed,
	itemInfo *tmt.SteamItemInfo,
	mallItem *model.MallItem,
	resaleItem *model.ResaleItem,
	standardConfig *define.ResaleStandardConfig,
) error {
	// 计算状态
	status := calculateStatus(mallItem, resaleItem, standardConfig)
	salePrice, err := CalculateSalePrice(tx, homeFeed, resaleItem, mallItem)
	if err != nil {
		return errors.Wrap(err, "计算出售价格失败")
	}
	homeFeed.Status = status
	homeFeed.SalePrice = salePrice
	homeFeed.ItemName = itemInfo.ItemName
	homeFeed.ItemIconURL = itemInfo.IconUrl
	homeFeed.ItemSpecs = itemInfo.Specs
	setHomeFeedInfo(homeFeed, mallItem, resaleItem, standardConfig)

	hf := repo.Query(tx).HomeFeed
	if err := repo.NewHomeFeedRepo(hf.WithContext(tx).Select(
		hf.ItemName, hf.ItemIconURL, hf.ItemSpecs, hf.Priority,
		hf.ItemMallStatus, hf.ItemResaleStatus, hf.Status,
		hf.SalePrice, hf.ResaleCount,
	)).UpdateById(homeFeed); err != nil {
		return errors.Wrap(err, "更新首页商品失败")
	}
	// 更新缓存
	if err := AddHomeFeedCacheOnStatusChange(tx, homeFeed); err != nil {
		return errors.Wrap(err, "更新首页商品缓存失败")
	}
	return nil
}

func UpdateHomeFeed(ctx context.Context, itemId string) error {
	// 获取商品信息
	itemInfo, err := GetItemInfo(ctx, itemId)
	if err != nil {
		return errors.Wrap(err, "获取商品信息失败")
	}
	mallItem, resaleItem, err := ConcurrentGetItems(ctx, itemId)
	if err != nil {
		return errors.Wrap(err, "获取商品信息失败")
	}
	standardConfig, err := GetResaleStandardConfig(ctx)
	if err != nil {
		return errors.Wrap(err, "获取转卖标准配置失败")
	}
	err = repo.ExecGenTx(ctx, func(tx context.Context) error {
		hf := repo.Query(tx).HomeFeed
		// 使用排他锁防止并发创建
		homeFeed, err := repo.NewHomeFeedRepo(hf.WithContext(tx).Clauses(clause.Locking{Strength: clause.LockingStrengthUpdate})).
			SelectOne(search.NewWrapper().Where(hf.ItemID.Eq(itemId)))
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.Wrap(err, "查询首页商品失败")
		}
		// 处理创建或更新逻辑
		if homeFeed == nil {
			return handleCreateHomeFeed(tx, itemId, itemInfo, mallItem, resaleItem, standardConfig)
		}
		return handleUpdateHomeFeed(tx, homeFeed, itemInfo, mallItem, resaleItem, standardConfig)
	})
	if err != nil {
		log.Ctx(ctx).Errorf("更新首页商品失败 itemId:%s, err:%+v", itemId, err)
		warn.SendDefaultWarnMsg(ctx, "【商品状态变化更新首页信息失败】", fmt.Sprintf("itemId: %v, 错误信息: %v", itemId, err))
		return errors.Wrap(err, "更新首页商品失败")
	}
	return nil
}

// HandleMallTradeCompleted 处理直购交易完成
func HandleMallTradeCompleted(ctx context.Context, itemId string, tradeCount int32, tradeTime time.Time) error {
	hf := repo.GetQuery().HomeFeed
	wrapper := search.NewWrapper().Where(hf.ItemID.Eq(itemId))
	homeFeed, err := repo.NewHomeFeedRepo(hf.WithContext(ctx)).SelectOne(wrapper)
	if err != nil {
		return errors.Wrap(err, "查询首页商品详情失败")
	}
	exprs := []field.AssignExpr{
		hf.LastTradeTime.Value(tradeTime), // 记录交易时间
		hf.SalesVolume.Add(tradeCount),    // 销量增加
	}
	var updateErrs []error
	_, err = hf.WithContext(ctx).Where(hf.ItemID.Eq(itemId)).UpdateSimple(exprs...)
	if err != nil {
		updateErrs = append(updateErrs, errors.Wrap(err, "更新首页商品交易信息失败"))
	} else {
		homeFeed.LastTradeTime = tradeTime
		if err = AddHomeFeedCacheOnStatusChange(ctx, homeFeed); err != nil {
			updateErrs = append(updateErrs, errors.Wrap(err, "更新首页商品缓存失败"))
		}
	}
	if len(updateErrs) > 0 {
		var combinedErr error
		for _, e := range updateErrs {
			if combinedErr == nil {
				combinedErr = e
			} else {
				combinedErr = fmt.Errorf("%w; %v", combinedErr, e)
			}
		}
		log.Ctx(ctx).Errorf("HandleMallTradeCompleted 部分操作失败: %v", combinedErr)
		warn.SendDefaultWarnMsg(ctx, "【直购交易完成更新首页信息失败】", fmt.Sprintf("itemId: %v, 错误信息: %v", itemId, combinedErr))
		return combinedErr
	}
	return nil
}

// HandleResaleTradeCompleted 处理转卖交易完成
func HandleResaleTradeCompleted(ctx context.Context, itemId string, tradeCount int32, tradeTime time.Time) error {
	hf := repo.GetQuery().HomeFeed
	wrapper := search.NewWrapper().Where(hf.ItemID.Eq(itemId))
	homeFeed, err := repo.NewHomeFeedRepo(hf.WithContext(ctx)).SelectOne(wrapper)
	if err != nil {
		return errors.Wrap(err, "查询首页商品详情失败")
	}
	mallItem, resaleItem, err := ConcurrentGetItems(ctx, itemId)
	if err != nil {
		return errors.Wrap(err, "获取商品信息失败")
	}
	// 获取价格
	salePrice, err := CalculateSalePrice(ctx, homeFeed, resaleItem, mallItem)
	if err != nil {
		return errors.Wrap(err, "计算首页商品价格失败")
	}
	var updateErrs []error
	exprs := []field.AssignExpr{
		hf.LastTradeTime.Value(tradeTime), // 记录交易时间
		hf.SalesVolume.Add(tradeCount),    // 销量
		hf.SalePrice.Value(salePrice),     // 价格
	}
	_, err = hf.WithContext(ctx).Where(hf.ItemID.Eq(itemId)).UpdateSimple(exprs...)
	if err != nil {
		updateErrs = append(updateErrs, errors.Wrap(err, "更新首页商品交易信息失败"))
	} else {
		// 更新首页缓存
		homeFeed.LastTradeTime = tradeTime
		if err = AddHomeFeedCacheOnStatusChange(ctx, homeFeed); err != nil {
			updateErrs = append(updateErrs, errors.Wrap(err, "更新首页商品缓存失败"))
		}
	}

	// 更新转卖数量
	_, err = hf.WithContext(ctx).Where(hf.ItemID.Eq(itemId), hf.ResaleCount.Gte(tradeCount)).
		UpdateSimple(hf.ResaleCount.Add(-tradeCount))
	if err != nil {
		updateErrs = append(updateErrs, errors.Wrap(err, "更新首页商品转卖数量失败"))
	}
	if len(updateErrs) > 0 {
		var combinedErr error
		for _, e := range updateErrs {
			if combinedErr == nil {
				combinedErr = e
			} else {
				combinedErr = fmt.Errorf("%w; %v", combinedErr, e)
			}
		}
		log.Ctx(ctx).Errorf("HandleResaleTradeCompleted 部分操作失败: %v", combinedErr)
		warn.SendDefaultWarnMsg(ctx, "【转卖交易完成更新首页信息失败】", fmt.Sprintf("itemId: %v, 错误信息: %v", itemId, combinedErr))
		return combinedErr
	}
	return nil
}

// HandleResaleListingsOnSale 处理上架挂单
func HandleResaleListingsOnSale(ctx context.Context, itemId string, count int32) error {
	hf := repo.GetQuery().HomeFeed
	wrapper := search.NewWrapper().Where(hf.ItemID.Eq(itemId))
	homeFeed, err := repo.NewHomeFeedRepo(hf.WithContext(ctx)).SelectOne(wrapper)
	if err != nil {
		return errors.Wrap(err, "查询首页商品详情失败")
	}
	mallItem, resaleItem, err := ConcurrentGetItems(ctx, itemId)
	if err != nil {
		return errors.Wrap(err, "获取商品信息失败")
	}
	// 获取价格
	salePrice, err := CalculateSalePrice(ctx, homeFeed, resaleItem, mallItem)
	if err != nil {
		return errors.Wrap(err, "计算首页商品价格失败")
	}
	exprs := []field.AssignExpr{
		hf.ResaleCount.Add(count),     // 转卖库存
		hf.SalePrice.Value(salePrice), // 价格
	}
	_, err = hf.WithContext(ctx).Where(hf.ItemID.Eq(itemId)).UpdateSimple(exprs...)
	if err != nil {
		log.Ctx(ctx).Errorf("HandleResaleListingsOnSale 部分操作失败: %v", err)
		warn.SendDefaultWarnMsg(ctx, "【挂单上架更新首页信息失败】", fmt.Sprintf("itemId: %v, 错误信息: %v", itemId, err))
		return errors.Wrap(err, "更新首页商品失败")
	}
	return nil

}

// HandleResaleListingsCanceled 处理挂单取消
func HandleResaleListingsCanceled(ctx context.Context, itemId string, count int32) error {
	hf := repo.GetQuery().HomeFeed
	wrapper := search.NewWrapper().Where(hf.ItemID.Eq(itemId))
	homeFeed, err := repo.NewHomeFeedRepo(hf.WithContext(ctx)).SelectOne(wrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.Wrap(err, "查询首页商品失败")
	}
	mallItem, resaleItem, err := ConcurrentGetItems(ctx, itemId)
	if err != nil {
		return errors.Wrap(err, "获取商品信息失败")
	}
	salePrice, err := CalculateSalePrice(ctx, homeFeed, resaleItem, mallItem)
	if err != nil {
		return errors.Wrap(err, "计算首页价格失败")
	}
	var updateErrs []error
	_, err = hf.WithContext(ctx).
		Where(hf.ItemID.Eq(itemId)).
		UpdateSimple(hf.SalePrice.Value(salePrice))
	if err != nil {
		updateErrs = append(updateErrs, errors.Wrap(err, "更新首页商品价格失败"))
	}
	// 转卖数量
	_, err = hf.WithContext(ctx).Where(hf.ItemID.Eq(itemId), hf.ResaleCount.Gte(count)).
		UpdateSimple(hf.ResaleCount.Add(-count))
	if err != nil {
		updateErrs = append(updateErrs, errors.Wrap(err, "更新首页商品转卖数量失败"))
	}
	if len(updateErrs) > 0 {
		var combinedErr error
		for _, e := range updateErrs {
			if combinedErr == nil {
				combinedErr = e
			} else {
				combinedErr = fmt.Errorf("%w; %v", combinedErr, e)
			}
		}
		log.Ctx(ctx).Errorf("HandleResaleListingsCanceled 部分操作失败: %v", combinedErr)
		warn.SendDefaultWarnMsg(ctx, "【挂单取消更新首页信息失败】", fmt.Sprintf("itemId: %v, 错误信息: %v", itemId, combinedErr))
		return combinedErr
	}
	return nil
}

// UpdateHomeFeedInfo 更新首页商品信息
func UpdateHomeFeedInfo(ctx context.Context, itemId string, itemInfo *tmt.SteamItemInfo) error {
	hf := repo.Query(ctx).HomeFeed
	wrapper := search.NewWrapper().Where(hf.ItemID.Eq(itemId))

	count, err := repo.NewHomeFeedRepo(hf.WithContext(ctx)).Count(wrapper)
	if err != nil {
		return errors.Wrap(err, "查询首页商品数量失败")
	}
	if count == 0 {
		return nil
	}

	updateModel := &model.HomeFeed{
		ItemName:    itemInfo.ItemName,
		ItemSpecs:   itemInfo.Specs,
		ItemIconURL: itemInfo.IconUrl,
	}
	if err := SetHomeFeedItemIPID(ctx, updateModel, itemInfo); err != nil {
		return errors.Wrap(err, "设置 HomeFeed IPID 失败")
	}
	if err := repo.NewHomeFeedRepo(hf.WithContext(ctx)).Update(updateModel, wrapper); err != nil {
		return errors.Wrap(err, "更新首页商品失败")
	}
	return nil
}

// 辅助函数处理 NULL int32
func getInt32(n sql.NullInt32) int32 {
	if n.Valid {
		return n.Int32
	}
	return 0
}

// 辅助函数处理 NULL time
func getTime(t sql.NullTime) time.Time {
	if t.Valid {
		return t.Time
	}
	return time.Time{} // 返回零值时间
}

func GetTradeVolumeAndTime(ctx context.Context, itemId string) (int32, time.Time, error) {
	g, ctx := errgroup.WithContext(ctx)

	// 定义专用结果结构体，处理可能的 NULL 值
	type queryResult struct {
		Volume sql.NullInt32
		Time   sql.NullTime
	}

	var (
		mallRes   queryResult
		resaleRes queryResult
	)

	// 并发查询直购数据
	g.Go(func() error {
		toi := repo.GetQuery().TradeOrderItem
		to := repo.GetQuery().TradeOrder
		return to.WithContext(ctx).
			Select(
				toi.Quantity.Sum().As("volume"),
				to.PaymentTime.Max().As("time"),
			).
			Join(toi, toi.OrderID.EqCol(to.ID)).
			Where(
				toi.ItemID.Eq(itemId),
				to.OrderStatus.In(
					enums.OrderStatusUnDelivered.Val(),
					enums.OrderStatusDelivered.Val(),
					enums.OrderStatusCompleted.Val(),
				),
			).
			Scan(&mallRes)
	})

	// 并发查询转卖数据
	g.Go(func() error {
		ro := repo.GetQuery().ResaleOrder
		return ro.WithContext(ctx).
			Select(
				ro.Quantity.Sum().As("volume"),
				ro.FinishedAt.Max().As("time"),
			).
			Where(
				ro.ItemID.Eq(itemId),
				ro.Status.Eq(enums.ResaleOrderStatusCompleted.Val()),
			).
			Scan(&resaleRes)
	})

	// 等待所有查询完成
	if err := g.Wait(); err != nil {
		return 0, time.Time{}, errors.Wrap(err, "查询商品交易数据失败")
	}

	// 处理 NULL 值并合并结果
	totalVolume := getInt32(mallRes.Volume) + getInt32(resaleRes.Volume)
	lastTime := getTime(mallRes.Time)
	if resaleTime := getTime(resaleRes.Time); resaleTime.After(lastTime) {
		lastTime = resaleTime
	}
	return totalVolume, lastTime, nil
}

func UpdateHomeFeedAndStats(ctx context.Context, itemId string, salesVolume int32, lastTradeTime time.Time, resaleCount int32, wishCount int32) error {
	hf := repo.GetQuery().HomeFeed
	hfi := repo.GetQuery().HomeFeedStats

	// 使用事务保证一致性
	return repo.ExecGenTx(ctx, func(tx context.Context) error {
		// 更新销量和交易时间
		updateFields := map[string]interface{}{}
		if salesVolume > 0 {
			updateFields["sales_volume"] = salesVolume
		}
		if !lastTradeTime.IsZero() {
			updateFields["last_trade_time"] = lastTradeTime
		}
		if resaleCount > 0 {
			updateFields["resale_count"] = resaleCount
		}

		if len(updateFields) > 0 {
			if err := repo.NewHomeFeedRepo(hf.WithContext(tx)).UpdateField(updateFields, search.NewWrapper().Where(hf.ItemID.Eq(itemId))); err != nil {
				return errors.Wrap(err, "更新首页商品销量失败")
			}
		}

		// 更新想购数量
		if wishCount > 0 {
			stats, err := repo.NewHomeFeedStatsRepo(hfi.WithContext(tx)).SelectOne(search.NewWrapper().Where(hfi.ItemID.Eq(itemId)))
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.Wrap(err, "查询首页统计信息失败")
			}

			if stats != nil {
				stats.WishCount = wishCount
				if err := repo.NewHomeFeedStatsRepo(hfi.WithContext(tx)).UpdateById(stats); err != nil {
					return errors.Wrap(err, "更新首页统计信息失败")
				}
			}
		}

		return nil
	})
}
