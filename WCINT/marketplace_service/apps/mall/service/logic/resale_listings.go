package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"fmt"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/locker"
	"marketplace_service/apps/mall/service/warn"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/yc_open"
	ycdefine "marketplace_service/third_party/yc_open/define"
	"sync"
	"time"
)

// ResaleListingsBuyVerify 转卖挂单购买校验
func ResaleListingsBuyVerify(ctx context.Context, req *define.ResaleItemBuyReq, userId string) (error, []*model.ResaleListingsItem) {
	var resaleListingsItemIds []int64
	listingsItemId2ListingsId := make(map[int64]int64)
	for _, item := range req.ResaleListingsItemInfo {
		listingsId, _ := utils.Str2Int64(item.ResaleListingsId)
		for _, itemIds := range item.ResaleListingsItemIds {
			itemId, _ := utils.Str2Int64(itemIds)
			resaleListingsItemIds = append(resaleListingsItemIds, itemId)
			listingsItemId2ListingsId[itemId] = listingsId
		}
	}
	if len(resaleListingsItemIds) == 0 {
		log.Ctx(ctx).Error("转卖挂单购买校验,resaleListingsItemIds为空")
		return define.MS200027Err, nil
	}
	if req.Quantity != int32(len(resaleListingsItemIds)) {
		log.Ctx(ctx).Error("转卖挂单购买校验,数量不一致")
		return define.MS200027Err, nil
	}
	to := repo.GetQuery().ResaleListingsItem
	items, err := repo.NewResaleListingsItemRepo(to.WithContext(ctx)).SelectList(
		search.NewWrapper().Where(to.ID.In(resaleListingsItemIds...)).Where(to.Status.Eq(enums.ResaleListingsStatusOnSale.Val())).
			Where(to.SellerID.Neq(userId)))
	if err != nil {
		log.Ctx(ctx).Errorf("转卖挂单购买校验,查询挂单物品失败, resaleListingsItemIds:%+v,err:%+v", utils.Obj2JsonStr(resaleListingsItemIds), err)
		return err, nil
	}
	if len(items) != len(resaleListingsItemIds) {
		log.Ctx(ctx).Errorf("转卖挂单购买校验,挂单物品数量不一致, resaleListingsItemIds:%+v,items:%+v", utils.Obj2JsonStr(resaleListingsItemIds), utils.Obj2JsonStr(items))
		return define.MS200027Err, nil
	}
	totalAmount := int64(0)
	for _, item := range items {
		totalAmount += item.SalePrice
		if listingsId, ok := listingsItemId2ListingsId[item.ID]; ok {
			if listingsId != item.ResaleListingsID {
				log.Ctx(ctx).Error("转卖挂单购买校验,挂单物品id与挂单id不匹配")
				return define.MS200027Err, nil
			}
		} else {
			log.Ctx(ctx).Error("转卖挂单购买校验,挂单物品id与挂单id不匹配")
			return define.MS200027Err, nil
		}
	}
	if totalAmount != req.TotalAmount {
		log.Ctx(ctx).Errorf("转卖挂单购买校验,挂单物品总价格不一致, resaleListingsItemIds:%+v,items:%+v", utils.Obj2JsonStr(resaleListingsItemIds), utils.Obj2JsonStr(items))
		return define.MS200027Err, nil
	}
	return nil, items
}

// VerifyAddResaleListings 校验挂单出售
func VerifyAddResaleListings(ctx context.Context, req *define.AddResaleListingsReq, resaleItem *model.ResaleItem,
	ycUserItemList []*ycdefine.UserItemData, resaleConfig *define.ResaleItemTradeConfig) error {
	// 检查商品是否上架
	if resaleItem.Status != enums.ResaleItemStatusUp.Val() {
		return define.MS200028Err
	}

	// 转卖配置
	if resaleConfig == nil {
		rf, err := GetResaleItemTradeConfig(ctx, resaleItem)
		if err != nil {
			return err
		}
		resaleConfig = rf
	}

	// 是否可以转卖挂单
	if resaleConfig.ResaleStatus != enums.ConfSwitchOn.Val() {
		return define.MS200020Err.SetMsg("当前商品不可转卖出售")
	}

	// 转卖间隔时间校验
	intervalMinutes := resaleConfig.IntervalMinutes
	if intervalMinutes > 0 {
		nowTime := utils.Now()
		for _, userItem := range ycUserItemList {
			if userItem.Extends.ReceiveType == ycdefine.UserItemReceiveTypeWcReSell.Val() {
				if userItem.CreatedAt.Add(time.Minute * time.Duration(intervalMinutes)).After(nowTime) {
					return define.MS200020Err.SetMsg("有物品未到转卖时间，别着急喔~")
				}
			}
		}
	}

	// 价格校验
	d100 := decimal.NewFromInt32(100)
	reqSalePrice := decimal.NewFromInt(req.SalePrice)
	// 最低售价校验
	minLimitPrice := decimal.NewFromInt(resaleConfig.MinLimitPrice)
	if reqSalePrice.LessThan(minLimitPrice) {
		// 低于最低限价了
		msg := fmt.Sprintf("转卖价格严重偏离市场，不得低于 %v 元", minLimitPrice.Div(d100).Round(2).String())
		return define.MS200021Err.SetMsg(msg)
	}
	// 最高售价校验
	maxLimitPrice := decimal.NewFromInt(resaleConfig.MaxLimitPrice)
	if reqSalePrice.GreaterThan(maxLimitPrice) {
		// 大于最高限价了
		msg := fmt.Sprintf("转卖价格严重偏离市场，不得高于 %v 元", maxLimitPrice.Div(d100).Round(2).String())
		return define.MS200022Err.SetMsg(msg)
	}

	// 挂单价格不能低于 2 分钱
	if req.SalePrice < 2 {
		return define.MS200021Err.SetMsg("转卖价格不得低于 0.02 元")
	}

	return nil
}

// TakeDownResaleListings 下架挂单（传主单 id）
func TakeDownResaleListings(ctx context.Context, id int64, operator string) error {
	// 加锁
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewResaleListingsLock(utils.StrVal(id), locker.ResaleListingsDown)))
	if !l.Lock(ctx) {
		return response.TooManyRequestErr
	}
	defer l.UnLock(ctx)

	logPrefix := "TakeDownResaleListings"
	rlSchema := repo.GetQuery().ResaleListings
	resaleListings, err := repo.NewResaleListingsRepo(rlSchema.WithContext(ctx)).
		SelectOne(search.NewWrapper().Where(rlSchema.ID.Eq(id)))
	if err != nil {
		return err
	}
	// 出售中 -> 已下架
	if resaleListings.Status != enums.ResaleListingsStatusOnSale.Val() {
		return define.MS200023Err
	}

	downItemCount, err := execDownResaleListings(ctx, resaleListings, operator)
	if err != nil {
		return err
	}
	// 更新统计数据
	if downItemCount > 0 {
		err = HandleResaleListingsCanceled(ctx, resaleListings.ItemID, downItemCount)
		if err != nil {
			log.Ctx(ctx).Errorf("%s HandleResaleListingsCanceled error: %v", logPrefix, err)
		}
	}

	return nil
}

// execDownResaleListings 执行挂单下架
func execDownResaleListings(ctx context.Context, resaleListings *model.ResaleListings, operator string) (int32, error) {
	// 获取子单
	statusList := []int32{enums.ResaleListingsItemStatusOnSale.Val(), enums.ResaleListingsItemStatusTrading.Val()}
	rliSchema := repo.GetQuery().ResaleListingsItem
	rliWrapper := search.NewWrapper().
		Where(rliSchema.ResaleListingsID.Eq(resaleListings.ID)).
		Where(rliSchema.Status.In(statusList...))
	rlItems, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(ctx)).SelectList(rliWrapper)
	if err != nil {
		return 0, err
	}
	var userItemIDs []string
	var rlItemIDs []int64
	var tradingStatusIDs []int64
	for _, rlItem := range rlItems {
		if rlItem.Status == enums.ResaleListingsItemStatusTrading.Val() {
			tradingStatusIDs = append(tradingStatusIDs, rlItem.ID)
		} else {
			userItemIDs = append(userItemIDs, rlItem.UserItemID)
			rlItemIDs = append(rlItemIDs, rlItem.ID)
		}
	}

	if len(rlItemIDs) == 0 {
		return 0, define.MS200023Err.SetMsg("当前没有可下架的挂单物品")
	}

	// 事务执行
	err = repo.ExecGenTx(ctx, func(ctx context.Context) error {
		// 下架所有子单
		rliTxSchema := repo.Query(ctx).ResaleListingsItem
		rliUpdateParams := map[string]interface{}{
			"status": enums.ResaleListingsItemStatusDown.Val(),
		}
		rliUpdateWrapper := search.NewWrapper().
			Where(rliTxSchema.ResaleListingsID.Eq(resaleListings.ID)).
			Where(rliTxSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())).
			Where(rliTxSchema.ID.In(rlItemIDs...))
		err = repo.NewResaleListingsItemRepo(rliTxSchema.WithContext(ctx)).UpdateField(rliUpdateParams, rliUpdateWrapper)
		if err != nil {
			return err
		}

		// 处理主单
		rlUpdateParams := map[string]interface{}{
			"operator": operator,
		}
		if len(tradingStatusIDs) == 0 {
			// 没有“交易中”的物品时才下架主单
			rlUpdateParams["status"] = enums.ResaleListingsStatusDown.Val()
		} else {
			// 更新主单挂单数量
			rlUpdateParams["listing_quantity"] = gorm.Expr("listing_quantity - ?", len(rlItemIDs))
		}
		rlTxSchema := repo.Query(ctx).ResaleListings
		rlUpdateWrapper := search.NewWrapper().Where(rlTxSchema.ID.Eq(resaleListings.ID))
		err = repo.NewResaleListingsRepo(rlTxSchema.WithContext(ctx)).UpdateField(rlUpdateParams, rlUpdateWrapper)
		if err != nil {
			return err
		}
		// 更新云仓物品状态：出售中 -> 持有中
		if len(userItemIDs) > 0 {
			updateStatusReq := &yc_open.UpdateUserItemStatusReq{
				UserItemIDs:    userItemIDs,
				OriginalStatus: ycdefine.UserItemStatusForSale,
				UpdateStatus:   ycdefine.UserItemStatusOwned,
			}
			err = yc_open.UpdateUserItemStatus(ctx, updateStatusReq)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return int32(len(rlItemIDs)), nil
}

func processSingleListing(ctx context.Context, listing *model.ResaleListings, operator string) error {
	ctx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()

	// 事务处理
	if _, err := execDownResaleListings(ctx, listing, operator); err != nil {
		log.Ctx(ctx).Errorf("执行挂单下架失败, itemId=%s, listingsId=%v, err=%+v", listing.ItemID, listing.ID, err)
		warn.SendDefaultWarnMsg(ctx, "【执行挂单下架失败】", fmt.Sprintf("itemId : %v listingsId: %v, 错误信息: %v", listing.ItemID, listing.ID, err))
		return errors.Wrapf(err, "事务执行失败, listingID: %d", listing.ID)
	}
	return nil
}

// TakeDownResaleListingsByItemId 根据itemId下架所有在售挂单
func TakeDownResaleListingsByItemId(ctx context.Context, itemId string, operator string) error {
	if itemId == "" {
		return errors.New("itemId 不能为空")
	}
	// 加redis锁 防止重复执行
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewResaleItemLockActionLock(locker.UpdateResaleItem, itemId)))
	if !l.Lock(ctx) {
		return errors.New("已在执行下架在售挂单")
	}
	defer l.UnLock(ctx)
	pageSize := 20
	for {
		// 查询分页数据
		rl := repo.Query(ctx).ResaleListings
		wrapper := search.NewWrapper().Where(rl.ItemID.Eq(itemId), rl.Status.Eq(enums.ResaleListingsStatusOnSale.Val())).OrderBy(rl.CreatedAt, rl.ID)
		resaleListingsList, err := repo.NewResaleListingsRepo(rl.WithContext(ctx).Limit(pageSize)).SelectList(wrapper)
		if err != nil {
			return errors.Wrap(err, "获取转卖挂单失败")
		}
		if len(resaleListingsList) == 0 {
			log.Ctx(ctx).Infof("暂无商品转卖挂单")
			break
		}

		// 分批次处理主挂单
		batchSize := 10
		for i := 0; i < len(resaleListingsList); i += batchSize {
			end := i + batchSize
			if end > len(resaleListingsList) {
				end = len(resaleListingsList)
			}
			batch := resaleListingsList[i:end]

			// 限制最大并发数
			var wg sync.WaitGroup
			sem := make(chan struct{}, 5) // 最大并发数为 5
			batchErr := make(chan error, len(batch))

			for _, listing := range batch {
				sem <- struct{}{}
				wg.Add(1)
				go func(listing *model.ResaleListings) {
					defer func() {
						wg.Done()
						<-sem
					}()
					if err := processSingleListing(ctx, listing, operator); err != nil {
						batchErr <- err
					}
				}(listing)
			}

			// 等待所有并发任务完成
			wg.Wait()
			close(batchErr)

			// 检查错误
			if err, ok := <-batchErr; ok {
				log.Ctx(ctx).Errorf("批量处理失败, itemId: %s, err: %v", itemId, err)
			}
		}
		return nil
	}
	return nil
}
