package logic

import (
	"context"
	"database/sql"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/global"
	"marketplace_service/pkg/middleware"
	"marketplace_service/pkg/middleware/g/auth"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/pkg/utils/snowflakeutl"
	"marketplace_service/third_party/tmt"
	"marketplace_service/third_party/yc_open"
	"strconv"
	"time"
)

// ValidateYcSteamItem 验证云仓商品状态
func ValidateYcSteamItem(ctx context.Context, skuID string) (*yc_open.QuerySteamItemData, error) {
	steamItem, err := yc_open.QuerySteamItemBySkuNo(ctx, skuID)
	if err != nil {
		return nil, errors.Wrap(err, "查询商品信息失败")
	}
	if steamItem == nil {
		log.Ctx(ctx).Warnf("云仓商品 SKU:%s 不存在", skuID)
		return nil, define.MS200005Err
	}
	if steamItem.SupplyStatus != 1 {
		log.Ctx(ctx).Warnf("商品 SKU:%s 当前未在供应链中启用或不可售", skuID)
		return nil, define.MS200006Err
	}
	if steamItem.SellListings == 0 {
		log.Ctx(ctx).Warnf("商品 SKU:%s 剩余可售数量为0", skuID)
		return nil, define.MS200007Err
	}
	return steamItem, nil
}

// ValidateStatusTransition 验证状态转移是否合法
func ValidateStatusTransition(oldStatus, newStatus int32) error {
	switch oldStatus {
	case enums.MallItemStatusWaiting.Val():
		if newStatus != enums.MallItemStatusUp.Val() {
			return errors.New("非待上架状态不能改为其他状态")
		}
	case enums.MallItemStatusUp.Val():
		if newStatus != enums.MallItemStatusDown.Val() {
			return errors.New("已上架商品只能下架")
		}
	case enums.MallItemStatusDown.Val():
		if newStatus != enums.MallItemStatusUp.Val() {
			return errors.New("已下架商品只能重新上架")
		}
	default:
		return errors.New("无效的直购商品状态")
	}
	return nil
}

// 构建排序Score
func buildZSetScore(priority int32, timestamp int64) float64 {
	if priority < 0 {
		priority = 0
	}
	if priority > 99999 {
		priority = 99999
	}
	score := (int64(priority) << 44) | (timestamp & 0xFFFFFFFFFFF)
	return float64(score)
}

// HandleIndexOnStatusChange 添加商城首页直购商品缓存
func HandleIndexOnStatusChange(ctx context.Context, m *model.MallItem) error {
	now := time.Now()
	key := constant.GetMallItemListKey()
	switch m.Status {
	case enums.MallItemStatusUp.Val():
		if m.StartTime.Before(now) {
			shelfTimestamp := m.ShelfTime.UnixMilli()
			score := buildZSetScore(m.Priority, shelfTimestamp)
			zMember := &redis.Z{
				Score:  score,
				Member: m.ID,
			}
			if err := global.REDIS.ZAdd(ctx, key, zMember).Err(); err != nil {
				log.Ctx(ctx).Errorf("添加商城首页直购商品缓存失败 err:%+v", err)
				return err
			}
		}

	case enums.MallItemStatusDown.Val():
		if err := global.REDIS.ZRem(ctx, key, m.ID).Err(); err != nil {
			log.Ctx(ctx).Errorf("删除商城首页直购商品缓存失败 err:%+v", err)
			return err
		}
	default:
		return nil
	}
	return nil
}

func HandleStockCacheOnStatusChange(ctx context.Context, m *model.MallItem) error {
	switch m.Status {
	case enums.MallItemStatusUp.Val():
		// 设置商品库存
		if err := global.REDIS.Set(ctx, constant.GetMallItemStockKey(m.ID), m.Stock, 0).Err(); err != nil {
			log.Ctx(ctx).Errorf("设置直购商品库存缓存失败 err:%+v", err)
			return err
		}
	case enums.MallItemStatusDown.Val():
		// 移除商品库存
		if err := global.REDIS.Del(ctx, constant.GetMallItemStockKey(m.ID)).Err(); err != nil {
			log.Ctx(ctx).Errorf("删除直购商品库存缓存失败 err:%+v", err)
			return err
		}
	default:
		return nil
	}
	return nil
}

func ProcessBatchStartedToAddCache(ctx context.Context, batch []*model.MallItem) error {
	var needAdd []*model.MallItem
	itemIDs := make([]string, 0, len(batch))

	for _, item := range batch {
		if time.Now().After(item.StartTime) {
			itemIDs = append(itemIDs, strconv.FormatInt(item.ID, 10))
			needAdd = append(needAdd, item)
		}
	}

	if len(itemIDs) == 0 {
		return nil
	}

	// 批量查询缓存是否存在
	results, err := global.REDIS.ZMScore(ctx, constant.MallItemListKey, itemIDs...).Result()
	if err != nil {
		log.Ctx(ctx).Errorf("批量查询缓存失败: %v", err)
		return err
	}

	for i, item := range needAdd {
		if results[i] == 0 {
			if err := HandleIndexOnStatusChange(ctx, item); err != nil {
				log.Ctx(ctx).Errorf("添加商品缓存失败 ID:%d, err:%v", item.ID, err)
			}
		}
	}
	return nil
}

func ValidateMallItemForPurchase(ctx context.Context, mallItemID int64) (*model.MallItem, error) {
	mi := repo.GetQuery().MallItem
	wrapper := search.NewWrapper().
		Where(mi.ID.Eq(mallItemID), mi.Status.Eq(enums.MallItemStatusUp.Val()))

	mallItem, err := repo.NewMallItemRepo(mi.WithContext(ctx)).SelectOne(wrapper)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, define.MS200012Err
		}
		return nil, errors.Wrap(err, "查询商品失败")
	}

	if time.Now().Before(mallItem.StartTime) {
		return nil, define.MS200011Err
	}

	return mallItem, nil
}

// CheckUserTotalLimit 校验用户总限购
func CheckUserTotalLimit(ctx context.Context, req *define.WebBuyReq, mallItem *model.MallItem, userId string) error {
	if mallItem.TotalLimit <= 0 {
		return nil // 无总量限制
	}
	to := repo.GetQuery().TradeOrder
	toi := repo.GetQuery().TradeOrderItem
	var totalQuantity sql.NullInt32
	err := toi.WithContext(ctx).
		Select(toi.Quantity.Sum().As("total_quantity")).
		Where(toi.MallItemID.Eq(req.MallItemID)).
		Join(to, toi.OrderID.EqCol(to.ID),
			to.OrderStatus.In(enums.OrderStatusUnPaid.Val(), enums.OrderStatusUnDelivered.Val(), enums.OrderStatusDelivered.Val(), enums.OrderStatusCompleted.Val())).
		Where(to.UserID.Eq(userId)).
		Scan(&totalQuantity)
	if err != nil {
		return errors.Wrap(err, "查询用户购买总量失败")
	}
	qty := int32(0)
	if totalQuantity.Valid {
		qty = totalQuantity.Int32
	}

	if req.Quantity+qty > mallItem.TotalLimit {
		return define.MS200013Err.SetMsg(fmt.Sprintf(define.MS200013Err.Msg, mallItem.TotalLimit))
	}
	return nil
}

// CalculateOrderPrice 计算订单价格
func CalculateOrderPrice(quantity int32, item *model.MallItem) *define.CalculateOrderPriceResp {
	resp := &define.CalculateOrderPriceResp{}
	var totalOriginal int64
	var totalDiscount int64
	var totalPayAmount int64

	price := item.SalePrice
	discount := item.Discount
	subtotal := price * int64(quantity)
	discountedPrice := CalculateDiscountedPrice(price, discount)
	discountAmount := subtotal - (discountedPrice * int64(quantity))
	payAmount := discountedPrice * int64(quantity)
	resp.OrderItem = define.OrderItemPriceDetail{
		MallItemID:     item.ID,
		ItemID:         item.ItemID,
		Price:          item.SalePrice,
		DiscountPrice:  discountedPrice,
		Quantity:       quantity,
		DiscountAmount: discountAmount,
		PayAmount:      payAmount,
		Subtotal:       subtotal,
	}
	totalOriginal += subtotal
	totalDiscount += discountAmount
	totalPayAmount += payAmount

	if item.Freight > 0 {
		resp.FreightAmount = item.Freight
		totalPayAmount += item.Freight
	}

	resp.TotalAmount = totalOriginal
	resp.DiscountAmount = totalDiscount
	resp.PayAmount = totalPayAmount

	return resp
}

// CalculateDiscountedPrice 计算折扣后价格(不足1分按照一分钱算)
func CalculateDiscountedPrice(basePrice int64, discount *int32) int64 {
	if discount == nil || *discount <= 0 || *discount >= 100 {
		return basePrice
	}
	discountedPrice := basePrice * int64(*discount) / 100
	if discountedPrice < 1 {
		return 1 // 不足 1 分，按 1 分处理
	}
	return discountedPrice
}

// BuildOrder 构建订单
func BuildOrder(ctx context.Context, req *define.WebBuyReq, mallItem *model.MallItem, orderPrice *define.CalculateOrderPriceResp) (*model.TradeOrder, error) {
	userInfo, ok := auth.GetUserFromCtx(ctx)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}
	// 获取地址信息
	addressInfo, err := tmt.QueryAddressById(ctx, userInfo.Id, req.AddressID)
	if err != nil {
		return nil, errors.Wrap(err, "获取用户地址信息失败")
	}
	orderConsigneeAddress := &define.OrderConsigneeAddress{
		Area:  addressInfo.Area,
		Code:  addressInfo.Code,
		Place: addressInfo.Place,
	}
	orderConsigneeAddressJson := datatypes.JSON(utils.Obj2JsonStr(orderConsigneeAddress))
	orderItemPrice := orderPrice.OrderItem
	tradeOrderItem := &model.TradeOrderItem{
		ID:             snowflakeutl.GenerateID(),
		MallItemID:     orderItemPrice.MallItemID,
		ItemID:         orderItemPrice.ItemID,
		SkuID:          mallItem.SkuID,
		SpuID:          mallItem.SpuID,
		ItemIconURL:    mallItem.ItemIconURL,
		ItemSpecs:      mallItem.ItemSpecs,
		ItemName:       mallItem.ItemName,
		SalePrice:      orderItemPrice.Price,
		DiscountPrice:  orderItemPrice.DiscountPrice,
		Quantity:       orderItemPrice.Quantity,
		Subtotal:       orderItemPrice.Subtotal,
		DiscountAmount: orderItemPrice.DiscountAmount,
		PayAmount:      orderItemPrice.PayAmount,
	}
	tradeOrder := &model.TradeOrder{
		ID:               snowflakeutl.GenerateID(),
		UserID:           userInfo.Id,
		OrderType:        enums.OrderTypeMall.Val(),            // 直购订单
		OrderStatus:      enums.OrderStatusUnPaid.Val(),        // 待支付
		PaymentStatus:    enums.PaymentStatusUnPaid.Val(),      // 未支付
		ShippingStatus:   enums.ShippingStatusNotShipped.Val(), // 未发货
		TotalAmount:      orderPrice.TotalAmount,
		DiscountAmount:   orderPrice.DiscountAmount,
		PayAmount:        orderPrice.PayAmount,
		FreightAmount:    orderPrice.FreightAmount,
		AddressID:        addressInfo.Id,
		ConsigneeName:    addressInfo.Name,
		ConsigneePhone:   addressInfo.MobilePhone,
		ConsigneeAddress: &orderConsigneeAddressJson,
		UserIP:           ctx.Value(middleware.Ip).(string),
		Terminal:         ctx.Value(middleware.ClientType).(string),
		UserRemark:       req.Remark,
		TradeOrderItem:   []*model.TradeOrderItem{tradeOrderItem},
	}
	return tradeOrder, nil
}

// DecrMallItemStock 扣减库存
func DecrMallItemStock(ctx context.Context, mallItemId int64, quantity int32) error {
	r := repo.Query(ctx).MallItem
	result, err := r.WithContext(ctx).
		Where(r.ID.Eq(mallItemId), r.AvailableStock.Gte(quantity)).
		Update(r.AvailableStock, r.AvailableStock.Sub(quantity))
	if err != nil {
		return err
	}
	if result.RowsAffected == 0 {
		return errors.New("扣减库存失败")
	}
	return nil
}

// RollbackMallItemStock 回滚商品库存
func RollbackMallItemStock(ctx context.Context, tradeOrder *model.TradeOrder) error {
	if len(tradeOrder.TradeOrderItem) == 0 {
		return nil
	}
	for _, tradeOrderItem := range tradeOrder.TradeOrderItem {
		// 获取直购商品ID
		mallItemId := tradeOrderItem.MallItemID
		mallItemRollbackStock := constant.MallItemRollbackStockLuaSha
		keys := []string{constant.GetMallItemStockKey(mallItemId), constant.GetUserBuyCountKey(mallItemId, tradeOrder.UserID)}
		args := []interface{}{tradeOrderItem.Quantity}
		result, err := global.REDIS.EvalSha(ctx, mallItemRollbackStock, keys, args...).Int()
		if err != nil {
			log.Ctx(ctx).Errorf("Lua 脚本执行失败 key:%v, err:%+v", keys, err)
			return errors.New("缓存库存回滚失败")
		}
		if result != 1 {
			log.Ctx(ctx).Errorf("Redis库存回滚失败 key:%v, err:%+v", keys, err)
			return errors.New("缓存库存回滚失败")
		}
		// 增加库存
		if err := IncrDBStock(ctx, tradeOrderItem.MallItemID, tradeOrderItem.Quantity); err != nil {
			log.Ctx(ctx).Errorf("DB库存回滚失败 err %+v", err)
			return errors.Wrap(err, "库存回滚失败")
		}
	}
	return nil
}

// IncrDBStock 增加库存
func IncrDBStock(ctx context.Context, mallItemId int64, quantity int32) error {
	mi := repo.Query(ctx).MallItem
	result, err := mi.WithContext(ctx).
		Where(mi.ID.Eq(mallItemId), mi.AvailableStock.Add(quantity).LteCol(mi.Stock)).
		Update(mi.AvailableStock, mi.AvailableStock.Add(quantity))
	if err != nil {
		return err
	}
	if result.RowsAffected == 0 {
		return errors.New("增加库存失败")
	}
	return nil
}

// IncMallItemSoldNum 增加销量
func IncMallItemSoldNum(ctx context.Context, mallItemId int64, quantity int32) error {
	mi := repo.Query(ctx).MallItem
	_, err := mi.WithContext(ctx).
		Where(mi.ID.Eq(mallItemId)).
		Update(mi.SalesVolume, mi.SalesVolume.Add(quantity))
	if err != nil {
		return err
	}
	return nil
}

func AddMallItemSalesVolume(ctx context.Context, orderItems []*model.TradeOrderItem) error {
	for _, orderItem := range orderItems {
		if err := IncMallItemSoldNum(ctx, orderItem.MallItemID, orderItem.Quantity); err != nil {
			return errors.Wrap(err, "增加商品销量失败")
		}
	}
	return nil
}

func AddHomeFeedSalesVolume(ctx context.Context, orderItems []*model.TradeOrderItem, tradeTime time.Time) error {
	for _, orderItem := range orderItems {
		if err := HandleMallTradeCompleted(ctx, orderItem.ItemID, orderItem.Quantity, tradeTime); err != nil {
			return errors.Wrap(err, "增加商品销量失败")
		}
	}
	return nil
}

func IncMallItemUserWishlist(ctx context.Context, mallItemId int64) error {
	mi := repo.Query(ctx).MallItem
	_, err := mi.WithContext(ctx).
		Where(mi.ID.Eq(mallItemId)).
		Update(mi.SalesVolume, mi.SalesVolume.Add(1))
	if err != nil {
		return err
	}
	return nil
}

func GetLatestItemsWithSQL(itemIDs []string) ([]*model.MallItem, error) {
	db := repo.GetQuery().MallItem.UnderlyingDB()
	var results []*model.MallItem
	return results, db.Raw(`
        SELECT *
        FROM (
            SELECT 
                id,
				item_id,
				item_name,
				item_icon_url,
				sale_price,
				discount,
                ROW_NUMBER() OVER (PARTITION BY item_id ORDER BY shelf_time DESC) AS rn
            FROM mall_item
            WHERE item_id IN (?) 
              AND status IN (1,2)
              AND is_del = 0
        ) temp
        WHERE rn = 1
    `, itemIDs).Scan(&results).Error
}

func SetIPIDFromItemInfo(ctx context.Context, item *model.MallItem, itemInfo *tmt.SteamItemInfo) error {
	if len(itemInfo.IpInfo) > 0 {
		ipIDs := make([]string, len(itemInfo.IpInfo))
		for i, info := range itemInfo.IpInfo {
			ipIDs[i] = info.ID
		}
		infos, err := GetIpInfos(ctx, ipIDs...)
		if err != nil {
			return errors.Wrap(err, "获取商品IP信息失败")
		}
		var ipID string
		for _, info := range infos {
			if info.Level == constant.LevelTwo {
				ipID = info.Id
				break
			}
		}
		if ipID == "" {
			ipID = itemInfo.IpInfo[0].ID
		}
		item.IPID = ipID
	}
	return nil
}

func SetCategoryIDFromItemInfo(ctx context.Context, item *model.MallItem, itemInfo *tmt.SteamItemInfo) error {
	if len(itemInfo.ItemClassifyInfo) > 0 {
		categoryIDs := make([]string, len(itemInfo.ItemClassifyInfo))
		for i, info := range itemInfo.ItemClassifyInfo {
			categoryIDs[i] = info.ID
		}
		infos, err := GetItemClassifyInfos(ctx, categoryIDs...)
		if err != nil {
			return errors.Wrap(err, "获取商品分类信息失败")
		}
		var categoryID string
		for _, info := range infos {
			if info.Level == constant.LevelThree {
				categoryID = info.Id
				break
			}
		}
		if categoryID == "" {
			categoryID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
		}
		item.CategoryID = categoryID
	}
	return nil
}

// UpdateMallItemInfo 更新直购商品商品信息
func UpdateMallItemInfo(ctx context.Context, itemId string, itemInfo *tmt.SteamItemInfo) error {
	mi := repo.Query(ctx).MallItem
	wrapper := search.NewWrapper().Where(mi.ItemID.Eq(itemId), mi.Status.Eq(enums.MallItemStatusUp.Val()))

	count, err := repo.NewMallItemRepo(mi.WithContext(ctx)).Count(wrapper)
	if err != nil {
		return errors.Wrap(err, "查询直购商品数量失败")
	}
	if count == 0 {
		return nil
	}

	updateModel := &model.MallItem{
		ItemName:    itemInfo.ItemName,
		ItemSpecs:   itemInfo.Specs,
		ItemIconURL: itemInfo.IconUrl,
	}
	if len(itemInfo.TrademarkInfo) > 0 {
		updateModel.TrademarkID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
	}

	if err := SetCategoryIDFromItemInfo(ctx, updateModel, itemInfo); err != nil {
		return errors.Wrap(err, "设置 CategoryID 失败")
	}
	if err := SetIPIDFromItemInfo(ctx, updateModel, itemInfo); err != nil {
		return errors.Wrap(err, "设置 IPID 失败")
	}

	if err := repo.NewMallItemRepo(mi.WithContext(ctx)).Update(updateModel, wrapper); err != nil {
		return errors.Wrap(err, "更新直购商品失败")
	}

	return nil
}
