package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/tmt"
	"time"
)

func SetResaleItemIPID(ctx context.Context, item *model.ResaleItem, itemInfo *tmt.SteamItemInfo) error {
	if len(itemInfo.IpInfo) > 0 {
		ipIDs := make([]string, len(itemInfo.IpInfo))
		for i, info := range itemInfo.IpInfo {
			ipIDs[i] = info.ID
		}
		infos, err := GetIpInfos(ctx, ipIDs...)
		if err != nil {
			return errors.Wrap(err, "获取商品IP信息失败")
		}
		var ipID string
		for _, info := range infos {
			if info.Level == constant.LevelTwo {
				ipID = info.Id
				break
			}
		}
		if ipID == "" {
			ipID = itemInfo.IpInfo[0].ID
		}
		item.IPID = ipID
	}
	return nil
}

func SetResaleItemCategoryID(ctx context.Context, item *model.ResaleItem, itemInfo *tmt.SteamItemInfo) error {
	if len(itemInfo.ItemClassifyInfo) > 0 {
		categoryIDs := make([]string, len(itemInfo.ItemClassifyInfo))
		for i, info := range itemInfo.ItemClassifyInfo {
			categoryIDs[i] = info.ID
		}
		infos, err := GetItemClassifyInfos(ctx, categoryIDs...)
		if err != nil {
			return errors.Wrap(err, "获取商品分类信息失败")
		}
		var categoryID string
		for _, info := range infos {
			if info.Level == constant.LevelThree {
				categoryID = info.Id
				break
			}
		}
		if categoryID == "" {
			categoryID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
		}
		item.CategoryID = categoryID
	}
	return nil
}

// ValidateResaleItemStatusTransition 验证状态转移是否合法
func ValidateResaleItemStatusTransition(oldStatus, newStatus int32) error {
	switch oldStatus {
	case enums.ResaleItemStatusWaiting.Val():
		if newStatus != enums.ResaleItemStatusUp.Val() {
			return errors.New("非待上架状态不能改为其他状态")
		}
	case enums.ResaleItemStatusUp.Val():
		if newStatus != enums.ResaleItemStatusDown.Val() {
			return errors.New("已上架商品只能下架")
		}
	case enums.ResaleItemStatusDown.Val():
		if newStatus != enums.ResaleItemStatusUp.Val() {
			return errors.New("已下架商品只能重新上架")
		}
	default:
		return errors.New("无效的转卖商品状态")
	}
	return nil
}

// GetResaleItemResaleStatus 获取转卖商品转卖状态
func GetResaleItemResaleStatus(ctx context.Context, item *model.ResaleItem) (int32, error) {
	resaleStatus := item.ResaleStatus
	if resaleStatus == enums.ResaleItemResaleStatusOpen.Val() {
		// 获取全局转卖状态
		cc, err := GetResaleStandardConfig(ctx)
		if err != nil {
			return resaleStatus, err
		}
		resaleStatus = cc.ResaleStatus
	}
	return resaleStatus, nil

}

// GetResaleItemCountDownSecond 获取转卖倒计时秒数
func GetResaleItemCountDownSecond(buyTime time.Time, intervalMinutes int32) int32 {
	second := int32(buyTime.Add(time.Minute*time.Duration(intervalMinutes)).Unix() - utils.Now().Unix())

	if second < 0 {
		second = 0
	}

	return second
}

// GetResaleItemTradeConfig 获取转卖商品交易配置
func GetResaleItemTradeConfig(ctx context.Context, resaleItem *model.ResaleItem) (*define.ResaleItemTradeConfig, error) {
	// 全局配置
	resaleConfig, err := GetResaleStandardConfig(ctx)
	if err != nil {
		return nil, err
	}

	// 先给全局配置兜底
	conf := &define.ResaleItemTradeConfig{
		ResaleStatus:    resaleItem.ResaleStatus, // 转卖开关，取商品本身的
		IntervalMinutes: resaleConfig.IntervalMinutes,
		MinPriceRatio:   resaleConfig.MinPriceRatio,
		MaxPriceRatio:   resaleConfig.MaxPriceRatio,
	}
	if resaleConfig.ResaleStatus == enums.ConfSwitchOff.Val() {
		// 全局配置关闭转卖优先级更高，不考虑商品自身的配置
		conf.ResaleStatus = resaleConfig.ResaleStatus
	}

	// 其他配置商品自身的优先级最高
	if resaleItem.IsCustomPriceLimit == enums.ConfSwitchOn.Val() {
		// 使用特定限价
		if resaleItem.MinPriceRatio != nil {
			conf.MinPriceRatio = *resaleItem.MinPriceRatio
		}
		if resaleItem.MaxPriceRatio != nil {
			conf.MaxPriceRatio = *resaleItem.MaxPriceRatio
		}
	}
	// 交易频次
	if resaleItem.TradeFrequencyType == enums.ResaleItemTradeFrequencyTypeSpecific.Val() {
		// 特定交易频次
		if resaleItem.IntervalMinutes != nil {
			conf.IntervalMinutes = *resaleItem.IntervalMinutes
		}
	}

	d100 := decimal.NewFromInt32(100)
	// 计算最低限价
	if conf.MinPriceRatio > 0 && resaleItem.MarketPrice > 0 {
		// 最低限价： 公允价 x 百分比
		minLimitPrice := decimal.NewFromInt(resaleItem.MarketPrice).
			Mul(decimal.NewFromInt32(conf.MinPriceRatio)).
			Div(d100)
		conf.MinLimitPrice = minLimitPrice.Round(0).IntPart()
	}
	// 计算最高限价
	itemID := resaleItem.ItemID
	maxSalePriceMap, err := GetOrderMaxSalePriceMap(ctx, []string{itemID})
	if err != nil {
		return nil, err
	}
	if maxSalePrice, ok := maxSalePriceMap[itemID]; ok {
		if maxSalePrice > 0 && conf.MaxPriceRatio > 0 {
			// 最高限价：历史最高售价 x 百分比
			maxLimitPrice := decimal.NewFromInt(maxSalePrice).
				Mul(decimal.NewFromInt32(conf.MaxPriceRatio)).
				Div(d100)
			conf.MaxLimitPrice = maxLimitPrice.Round(0).IntPart()
		}
	}

	// 如果最高限价小于最低限价，用公允价来计算最高限价
	if conf.MaxLimitPrice < conf.MinLimitPrice {
		// 最高限价：公允价 x 百分比
		maxLimitPrice := decimal.NewFromInt(resaleItem.MarketPrice).
			Mul(decimal.NewFromInt32(conf.MaxPriceRatio)).
			Div(d100)
		conf.MaxLimitPrice = maxLimitPrice.Round(0).IntPart()
	}

	return conf, nil
}

func BatchGetResaleItemTradeConfigMap(ctx context.Context, resaleItems []*model.ResaleItem) (map[string]*define.ResaleItemTradeConfig, error) {
	if len(resaleItems) == 0 {
		return make(map[string]*define.ResaleItemTradeConfig), nil
	}

	resaleConfig, err := GetResaleStandardConfig(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "获取全局转卖配置失败")
	}

	itemIDs := make([]string, 0, len(resaleItems))
	for _, item := range resaleItems {
		itemIDs = append(itemIDs, item.ItemID)
	}

	maxSalePriceMap, err := GetOrderMaxSalePriceMap(ctx, itemIDs)
	if err != nil {
		return nil, errors.Wrap(err, "获取历史最高售价失败")
	}

	configMap := make(map[string]*define.ResaleItemTradeConfig, len(resaleItems))
	d100 := decimal.NewFromInt32(100)

	for _, item := range resaleItems {
		conf := &define.ResaleItemTradeConfig{
			ResaleStatus:    resaleConfig.ResaleStatus,
			IntervalMinutes: resaleConfig.IntervalMinutes,
			MinPriceRatio:   resaleConfig.MinPriceRatio,
			MaxPriceRatio:   resaleConfig.MaxPriceRatio,
		}
		// 如果全局是关闭状态以全局状态为准
		if resaleConfig.ResaleStatus == enums.ConfSwitchOff.Val() {
			conf.ResaleStatus = resaleConfig.ResaleStatus
		}

		if item.IsCustomPriceLimit == enums.ConfSwitchOn.Val() {
			if item.MinPriceRatio != nil {
				conf.MinPriceRatio = *item.MinPriceRatio
			}
			if item.MaxPriceRatio != nil {
				conf.MaxPriceRatio = *item.MaxPriceRatio
			}
		}

		if item.TradeFrequencyType == enums.ResaleItemTradeFrequencyTypeSpecific.Val() {
			if item.IntervalMinutes != nil {
				conf.IntervalMinutes = *item.IntervalMinutes
			}
		}

		// 最低限价 = 公允价 × 最低比例
		if conf.MinPriceRatio > 0 && item.MarketPrice > 0 {
			conf.MinLimitPrice = decimal.NewFromInt(item.MarketPrice).
				Mul(decimal.NewFromInt32(conf.MinPriceRatio)).
				Div(d100).Ceil().IntPart()
		}

		// 最高限价 = 历史最高价 × 最高比例
		if maxSalePrice, ok := maxSalePriceMap[item.ItemID]; ok && maxSalePrice > 0 && conf.MaxPriceRatio > 0 {
			conf.MaxLimitPrice = decimal.NewFromInt(maxSalePrice).
				Mul(decimal.NewFromInt32(conf.MaxPriceRatio)).
				Div(d100).Ceil().IntPart()
		} else if item.MarketPrice > 0 && conf.MaxPriceRatio > 0 {
			conf.MaxLimitPrice = decimal.NewFromInt(item.MarketPrice).
				Mul(decimal.NewFromInt32(conf.MaxPriceRatio)).
				Div(d100).Ceil().IntPart()
		}

		// 如果最高限价 < 最低限价，使用公允价重新计算最高限价
		if conf.MaxLimitPrice < conf.MinLimitPrice {
			conf.MaxLimitPrice = decimal.NewFromInt(item.MarketPrice).
				Mul(decimal.NewFromInt32(conf.MaxPriceRatio)).
				Div(d100).Ceil().IntPart()
		}

		configMap[item.ItemID] = conf
	}

	return configMap, nil
}

// UpdateResaleItemInfo 更新转卖商品信息
func UpdateResaleItemInfo(ctx context.Context, itemId string, itemInfo *tmt.SteamItemInfo) error {
	ri := repo.Query(ctx).ResaleItem
	wrapper := search.NewWrapper().Where(ri.ItemID.Eq(itemId), ri.Status.Eq(enums.ResaleItemStatusUp.Val()))

	count, err := repo.NewResaleItemRepo(ri.WithContext(ctx)).Count(wrapper)
	if err != nil {
		return errors.Wrap(err, "查询转卖商品数量失败")
	}
	if count == 0 {
		return nil
	}
	// 计算公允价
	marketPrice := itemInfo.PurchasePrice * 105 / 100
	updateModel := &model.ResaleItem{
		ItemName:    itemInfo.ItemName,
		ItemSpecs:   itemInfo.Specs,
		ItemIconURL: itemInfo.IconUrl,
		MarketPrice: marketPrice,
	}
	if len(itemInfo.TrademarkInfo) > 0 {
		updateModel.TrademarkID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
	}

	if err := SetResaleItemCategoryID(ctx, updateModel, itemInfo); err != nil {
		return errors.Wrap(err, "设置 ResaleItem CategoryID 失败")
	}
	if err := SetResaleItemIPID(ctx, updateModel, itemInfo); err != nil {
		return errors.Wrap(err, "设置 ResaleItem IPID 失败")
	}
	if err := repo.NewResaleItemRepo(ri.WithContext(ctx)).Update(updateModel, wrapper); err != nil {
		return errors.Wrap(err, "更新转卖商品失败")
	}

	return nil
}

// HandleResaleStandardConfigOpen 处理转卖标准转卖开关开启 todo: 完善优化
func HandleResaleStandardConfigOpen(ctx context.Context) error {
	// 获取所有上架的并且开启转卖的转卖商品
	ri := repo.Query(ctx).ResaleItem
	wrapper := search.NewWrapper().Where(ri.Status.Eq(enums.ResaleItemStatusUp.Val()), ri.ResaleStatus.Eq(enums.ResaleListingsItemStatusOnSale.Val()))
	resaleItems, err := repo.NewResaleItemRepo(ri.WithContext(ctx)).SelectList(wrapper)
	if err != nil {
		return errors.Wrap(err, "获取所有上架的并且开启转卖的转卖商品失败")
	}
	for _, resaleItem := range resaleItems {
		// 更新首页商品信息(重新上架)
		if err := UpdateHomeFeed(ctx, resaleItem.ItemID); err != nil {
			log.Ctx(ctx).Errorf("更新首页商品信息 err %+v", err)
			continue
		}
	}
	return nil
}

// HandleResaleStandardConfigClose 处理转卖标准转卖开关关闭 todo: 完善优化
// 1. 将所有的转卖商品的转卖状态都关闭 & 同时关闭首页的转卖状态
// 2. 只关闭首页的转卖状态
// 上面两种取一种方式
func HandleResaleStandardConfigClose(ctx context.Context, operator string) error {
	// 获取所有开启转卖的首页商品
	hf := repo.Query(ctx).HomeFeed
	wrapper := search.NewWrapper().Where(hf.ItemResaleStatus.Eq(1))
	homeFeeds, err := repo.NewHomeFeedRepo(hf.WithContext(ctx)).SelectList(wrapper)
	if err != nil {
		return errors.Wrap(err, "查询所有转卖商品失败")
	}
	for _, homeFeed := range homeFeeds {
		// 下架所有的挂单
		if err := TakeDownResaleListingsByItemId(ctx, homeFeed.ItemID, operator); err != nil {
			log.Ctx(ctx).Errorf("下架指定商品所有挂单失败: %+v", err)
			continue
		}
		// 关闭首页的转卖状态
		if err := UpdateHomeFeed(ctx, homeFeed.ItemID); err != nil {
			log.Ctx(ctx).Errorf("更新首页商品失败: %+v", err)
			continue
		}
	}
	return nil
}

// GetItemSoldOut 获取商品是否转卖直购都下架
func GetItemSoldOut(ctx context.Context, itemId string) int32 {
	mi := repo.Query(ctx).MallItem
	mallItemsCount, err := repo.NewMallItemRepo(mi.WithContext(ctx)).Count(search.NewWrapper().
		Where(mi.ItemID.Eq(itemId), mi.Status.Eq(enums.MallItemStatusUp.Val())))
	if err != nil {
		log.Ctx(ctx).Errorf("查询MallItem商品数量失败: %+v", err)
		return constant.IntTrue
	}
	if mallItemsCount > 0 {
		return constant.IntFalse
	}
	rsi := repo.Query(ctx).ResaleItem
	resaleItemCount, err := repo.NewResaleItemRepo(rsi.WithContext(ctx)).Count(search.NewWrapper().
		Where(rsi.ItemID.Eq(itemId), rsi.Status.Eq(enums.ResaleItemStatusUp.Val())))
	if err != nil {
		log.Ctx(ctx).Errorf("查询ResaleItem商品数量失败: %+v", err)
		return constant.IntTrue
	}
	if resaleItemCount > 0 {
		return constant.IntFalse
	}
	return constant.IntTrue
}

// GetItemSoldOutMap 获取商品是否转卖直购都下架 map
func GetItemSoldOutMap(ctx context.Context, itemIDs []string) map[string]int32 {
	resultMap := make(map[string]int32)
	mi := repo.Query(ctx).MallItem
	type countRecord struct {
		ItemID string `gorm:"column:item_id"`
		Count  int32  `gorm:"column:count"`
	}
	miDB := mi.WithContext(ctx).UnderlyingDB()
	miCountResults := make([]*countRecord, 0)
	err := miDB.Select("item_id, COUNT(*) AS count").
		Where("status = ?", enums.MallItemStatusUp.Val()).
		Where("item_id IN (?)", itemIDs).
		Group("item_id").
		Find(&miCountResults).Error
	if err != nil {
		log.Ctx(ctx).Errorf("查询MallItem商品数量失败: %+v", err)
	} else {
		for _, record := range miCountResults {
			resultMap[record.ItemID] = constant.IntFalse
		}
	}

	rsi := repo.Query(ctx).ResaleItem
	rsiDB := rsi.WithContext(ctx).UnderlyingDB()
	rsiCountResults := make([]*countRecord, 0)
	err = rsiDB.Select("item_id, COUNT(*) AS count").
		Where("status = ?", enums.ResaleItemStatusUp.Val()).
		Where("item_id IN (?)", itemIDs).
		Group("item_id").
		Find(&rsiCountResults).Error
	if err != nil {
		log.Ctx(ctx).Errorf("查询ResaleItem商品数量失败: %+v", err)
	} else {
		for _, record := range rsiCountResults {
			resultMap[record.ItemID] = constant.IntFalse
		}
	}

	for _, itemID := range itemIDs {
		if _, ok := resultMap[itemID]; !ok {
			resultMap[itemID] = constant.IntTrue
		}
	}

	return resultMap
}
