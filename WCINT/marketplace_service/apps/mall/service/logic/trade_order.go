package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"github.com/pkg/errors"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/pkg/utils/snowflakeutl"
	"marketplace_service/third_party/pat"
	"marketplace_service/third_party/wae"
	"marketplace_service/third_party/wat"
	"strconv"
	"strings"
	"time"
)

// CheckUserOrder 检查用户订单
func CheckUserOrder(ctx context.Context, id int64, userId string) error {
	schema := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(schema.ID.Eq(id)).Where(schema.UserID.Eq(userId))
	count, err := repo.NewTradeOrderRepo(schema.WithContext(ctx)).Count(wrapper)
	if err != nil {
		return errors.Wrap(err, "查询订单详情失败")
	}
	if count == 0 {
		return errors.New("订单不存在")
	}
	return nil
}

// CancelTradeOrder 取消订单
func CancelTradeOrder(ctx context.Context, id int64, cancelType int32, userId *string) error {
	now := time.Now()
	updateTradeOrder := &model.TradeOrder{
		OrderStatus: enums.OrderStatusCanceled.Val(),
		CancelType:  cancelType,
		CancelAt:    &now,
	}
	schema := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(schema.ID.Eq(id), schema.OrderStatus.Eq(enums.OrderStatusUnPaid.Val()))
	if userId != nil {
		wrapper = wrapper.Where(schema.UserID.Eq(*userId))
	}
	err := repo.NewTradeOrderRepo(schema.WithContext(ctx)).Update(updateTradeOrder, wrapper)
	if err != nil {
		log.Ctx(ctx).Errorf("更新订单状态失败 err: %+v", err)
		return errors.Wrap(err, "更新订单状态失败")
	}
	return nil
}

// DelTradeOrder 删除订单
func DelTradeOrder(ctx context.Context, id int64, userId *string) error {
	updateTradeOrder := &model.TradeOrder{}
	schema := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(schema.ID.Eq(id), schema.OrderStatus.In(enums.OrderStatusCompleted.Val(), enums.OrderStatusCanceled.Val()))
	if userId != nil {
		updateTradeOrder.IsUserDel = enums.IsUserDelYes.Val()
		wrapper = wrapper.Where(schema.UserID.Eq(*userId))
	} else {
		updateTradeOrder.IsUserDel = enums.IsUserDelNo.Val()
	}
	err := repo.NewTradeOrderRepo(schema.WithContext(ctx)).Update(updateTradeOrder, wrapper)
	if err != nil {
		return errors.Wrap(err, "删除订单失败")
	}
	return nil
}

// ConfirmTradeOrder 确定收货
func ConfirmTradeOrder(ctx context.Context, id int64, userId string) error {
	now := time.Now()
	updateTradeOrder := &model.TradeOrder{
		OrderStatus: enums.OrderStatusCompleted.Val(),
		FinishedAt:  &now,
	}
	schema := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(schema.ID.Eq(id), schema.UserID.Eq(userId), schema.OrderStatus.Eq(enums.OrderStatusDelivered.Val()))
	err := repo.NewTradeOrderRepo(schema.WithContext(ctx)).Update(updateTradeOrder, wrapper)
	if err != nil {
		return errors.Wrap(err, "确定收货失败")
	}
	return nil
}

func SetCountdownSecond(tradeOrder *model.TradeOrder) int32 {
	var second int32
	if tradeOrder.OrderStatus == enums.OrderStatusUnPaid.Val() {
		second = int32(tradeOrder.CreatedAt.Add(constant.Countdown).Unix() - time.Now().Unix())
	}
	if second < 0 {
		second = 0
	}
	return second
}

func SetFinishedSecond(tradeOrder *model.TradeOrder) int32 {
	var second int32
	if tradeOrder.OrderStatus == enums.OrderStatusDelivered.Val() && tradeOrder.DeliveredAt != nil {
		second = int32(tradeOrder.DeliveredAt.Add(constant.OrderFinishDay).Unix() - time.Now().Unix())
	}
	if second < 0 {
		second = 0
	}
	return second
}

func SplitArea(area string) []string {
	// 使用 strings.SplitN 分割，最多保留 4 个元素
	parts := strings.SplitN(area, "|", 4)

	// 如果分割后的长度不足 4，补全到 4 个元素
	switch len(parts) {
	case 0, 1, 2, 3:
		for i := len(parts); i < 4; i++ {
			parts = append(parts, "")
		}
	}

	return parts
}

// BuildOrderUploadRequest 构建订单上传请求
func BuildOrderUploadRequest(ctx context.Context, tradeOrder *model.TradeOrder) (*wae.OrderUploadRequest, error) {
	userInfoMap, err := pat.GetUserDetailMap(ctx, []string{tradeOrder.UserID})
	if err != nil {
		return nil, err
	}
	userInfo := userInfoMap[tradeOrder.UserID]
	consigneeAddress := &define.OrderConsigneeAddress{}
	bytes, _ := tradeOrder.ConsigneeAddress.MarshalJSON()
	err = json.Unmarshal(bytes, consigneeAddress)
	if err != nil {
		log.Ctx(ctx).Errorf("用户地址信息转结构体失败 err:%v", err)
		return nil, err
	}
	recvZip := "000000"
	if consigneeAddress.Code != "" {
		recvZip = consigneeAddress.Code
	}
	// 处理省市区
	areaParts := SplitArea(consigneeAddress.Area)
	orderGoodsList := make([]wae.OrderGoods, 0, len(tradeOrder.TradeOrderItem))
	for _, orderItem := range tradeOrder.TradeOrderItem {
		orderGoods := wae.OrderGoods{
			SKUNo:      orderItem.SkuID,
			OChildID:   strconv.FormatInt(orderItem.ID, 10),
			GoodsTitle: orderItem.ItemName,
			GoodsNum:   orderItem.Quantity,
			GoodsSpec:  orderItem.ItemSpecs,
			GoodsPrice: orderItem.SalePrice,
		}
		orderGoodsList = append(orderGoodsList, orderGoods)
	}
	orderItem := wae.OrderItem{
		OID:          strconv.FormatInt(tradeOrder.ID, 10),
		BuyerID:      tradeOrder.UserID,
		BuyerNick:    userInfo.Nickname,
		BuyerComment: tradeOrder.UserRemark,
		PayAmount:    tradeOrder.PayAmount,
		RecvName:     tradeOrder.ConsigneeName,
		RecvPhone:    tradeOrder.ConsigneePhone,
		RecvZip:      recvZip,
		RecvProvince: areaParts[0],
		RecvCity:     areaParts[1],
		RecvDistrict: areaParts[2],
		RecvAddress:  areaParts[3] + consigneeAddress.Place,
		OCreatedAt:   tradeOrder.CreatedAt.Format("2006-01-02 15:04:05"),
		OUpdatedAt:   tradeOrder.UpdatedAt.Format("2006-01-02 15:04:05"),
		OrderItems:   orderGoodsList,
	}
	return &wae.OrderUploadRequest{
		Orders: []wae.OrderItem{orderItem},
	}, err
}

// ProcessUploadOrderToSupply 订单上传到供应商
func ProcessUploadOrderToSupply(ctx context.Context, tradeOrder *model.TradeOrder) error {
	orderUploadRequest, err := BuildOrderUploadRequest(ctx, tradeOrder)
	if err != nil {
		return errors.Wrap(err, "构建订单上传请求失败")
	}

	orderData, err := wae.OrderUpload(ctx, orderUploadRequest)
	if err != nil {
		return errors.Wrap(err, "订单上传失败")
	}
	var failedIds []string
	var successOrders []*wae.Order
	if len(orderData.FailedOrders) > 0 {
		for _, order := range orderData.FailedOrders {
			if order.CNo == "" {
				failedIds = append(failedIds, order.OID)
			} else {
				if order.Message == "订单已上传" {
					successOrders = append(successOrders, &order)
				}
			}
		}
	}
	if len(orderData.SuccessOrders) > 0 {
		for _, order := range orderData.SuccessOrders {
			successOrders = append(successOrders, &order)
		}
	}
	if len(successOrders) > 0 {
		for _, order := range successOrders {
			if order.OID == strconv.FormatInt(tradeOrder.ID, 10) {
				updateTradeOrder := &model.TradeOrder{
					ID:                 tradeOrder.ID,
					SupplyChainOrderID: order.CNo,
				}
				err = repo.NewTradeOrderRepo(repo.GetQuery().TradeOrder.WithContext(ctx)).UpdateById(updateTradeOrder)
				if err != nil {
					return errors.Wrap(err, "更新订单状态失败")
				}
			}
		}
	}
	if len(failedIds) > 0 {
		log.Ctx(ctx).Errorf("订单上传失败, orderIds:%v", failedIds)
		return errors.New("订单上传失败")
	}
	return nil
}

// ProcessTimeoutOrder 处理超时订单
func ProcessTimeoutOrder(ctx context.Context, tradeOrder *model.TradeOrder) error {
	if tradeOrder.OrderStatus != enums.OrderStatusUnPaid.Val() {
		return nil // 状态已变，跳过
	}
	// todo: 查询钱包服务支付状态
	// 修改订单状态
	if err := CancelTradeOrder(ctx, tradeOrder.ID, enums.CancelTypeTimeOut.Val(), nil); err != nil {
		return errors.Wrap(err, "修改订单状态失败")
	}
	toi := repo.GetQuery().TradeOrderItem
	toiWrapper := search.NewWrapper().Where(toi.OrderID.Eq(tradeOrder.ID))
	orderItems, err := repo.NewTradeOrderItemRepo(toi.WithContext(ctx)).SelectList(toiWrapper)
	if err != nil {
		return errors.Wrap(err, "查询订单子单失败")
	}
	// 获取订单项
	err = repo.ExecGenTx(ctx, func(tx context.Context) error {
		tradeOrder.TradeOrderItem = orderItems
		if err := RollbackMallItemStock(tx, tradeOrder); err != nil {
			return errors.Wrap(err, "回滚库存失败")
		}
		return nil
	})
	if err != nil {
		log.Ctx(ctx).Error("回滚库存失败", err)
		return errors.Wrap(err, "回滚库存失败")
	}
	return nil
}

// UpdateTradeOrderDeliver 订单发货通知
func UpdateTradeOrderDeliver(ctx context.Context, req []*define.UpdateTradeOrderDeliver) []int64 {
	log.Ctx(ctx).Infof("订单发货通知, req:%+v", utils.Obj2JsonStr(req))
	schema := repo.GetQuery().TradeOrder
	freightSchema := repo.GetQuery().TradeOrderFreight
	var failId []int64
	for _, item := range req {
		freightTime := item.FreightTime.Add(time.Hour * 8)
		updateTradeOrder := &model.TradeOrder{
			OrderStatus: enums.OrderStatusDelivered.Val(),
			DeliveredAt: &freightTime,
		}
		wrapper := search.NewWrapper().Where(schema.SupplyChainOrderID.Eq(item.CNo), schema.OrderStatus.Eq(enums.OrderStatusUnDelivered.Val()))
		err := repo.NewTradeOrderRepo(schema.WithContext(ctx)).Update(updateTradeOrder, wrapper)
		if err != nil {
			log.Ctx(ctx).Errorf("订单发货失败, err:%+v, item:%+v", err, item)
			failId = append(failId, item.Mid)
			continue
		}

		// 新增物流信息
		tradeOrder, err := repo.NewTradeOrderRepo(schema.WithContext(ctx)).SelectOne(search.NewWrapper().
			Where(schema.SupplyChainOrderID.Eq(item.CNo)))
		if err != nil {
			log.Ctx(ctx).Errorf("订单发货失查询直购订单失败, err:%+v, cNo:%+v", err, item.CNo)
			failId = append(failId, item.Mid)
			continue
		}
		tradeOrderItem, err := repo.NewTradeOrderItemRepo(repo.GetQuery().TradeOrderItem.WithContext(ctx)).SelectOne(search.NewWrapper().
			Where(repo.GetQuery().TradeOrderItem.OrderID.Eq(tradeOrder.ID)).Where(repo.GetQuery().TradeOrderItem.SkuID.Eq(item.SkuNo)))
		if err != nil {
			log.Ctx(ctx).Errorf("订单发货,查询订单子单失败, err:%+v, item:%+v", err, item)
			failId = append(failId, item.Mid)
			continue
		}
		// 新增
		saveTradeOrderFreight := &model.TradeOrderFreight{
			ID:             snowflakeutl.GenerateID(),
			OrderID:        tradeOrder.ID,
			OrderItemID:    tradeOrderItem.ID,
			DeliveryNumber: item.FreightNo,
			DeliveredAt:    &freightTime,
		}
		queryRes, err := wae.FreightQuery(ctx, &wae.FreightQueryRequest{
			FreightNo: item.FreightNo,
		})
		if err != nil {
			log.Ctx(ctx).Errorf("订单发货,物流信息变更查询物流信息失败, err:%+v, freightNo:%+v", err, item.FreightNo)
		}
		if queryRes != nil {
			recordsJson := datatypes.JSON(utils.Obj2JsonStr(queryRes.Info))
			saveTradeOrderFreight.DeliveryCompany = queryRes.Com
			saveTradeOrderFreight.Status = int32(queryRes.Status)
			saveTradeOrderFreight.Checked = int32(queryRes.Checked)
			saveTradeOrderFreight.Records = &recordsJson
		}
		err = repo.NewTradeOrderFreightRepo(freightSchema.WithContext(ctx)).Save(saveTradeOrderFreight)
		if err != nil {
			log.Ctx(ctx).Errorf("订单发货,订单物流信息保存失败, err:%+v, item:%+v", err, item)
			failId = append(failId, item.Mid)
		}
	}
	return failId
}

// PayOrderRefund 支付单退款
func PayOrderRefund(ctx context.Context, req *define.TradeOrderRefundReq) (*wat.RefundRes, error) {
	refundRes, err := wat.Refund(ctx, &wat.RefundForm{
		RechargeOrderId: req.RechargeOrderID,
		Amount:          req.Amount,
	})
	if err != nil {
		return nil, errors.Wrap(err, "订单发起退款失败")
	}
	switch refundRes.Status {
	case wat.RefundSuccess:
		log.Ctx(ctx).Errorf("订单发起退款成功, refundRes: %+v", utils.Obj2JsonStr(refundRes))
		break
	case wat.RefundFail:
		log.Ctx(ctx).Errorf("订单发起退款失败, refundRes: %+v", utils.Obj2JsonStr(refundRes))
		break
	case wat.RefundUnknown:
		log.Ctx(ctx).Errorf("订单发起退款超时, refundRes: %+v", utils.Obj2JsonStr(refundRes))
		break
	}
	return refundRes, nil
}

// IsDuplicatePayment 判断订单是否重复支付
func IsDuplicatePayment(order *model.TradeOrder, req *define.TradeOrderPaySuccessReq) bool {
	return order.PaymentStatus == enums.PaymentStatusPaid.Val() && order.PayOrderID != req.RechargeOrderID
}

// IsOrderClosed 判断订单是否关闭
func IsOrderClosed(order *model.TradeOrder) bool {
	return order.OrderStatus == enums.OrderStatusCanceled.Val()
}

// IsOrderUnpaid 判断订单是否未支付
func IsOrderUnpaid(order *model.TradeOrder) bool {
	return order.OrderStatus == enums.OrderStatusUnPaid.Val()
}

func ParsePayTime(payTimeStr string) (time.Time, error) {
	layout := "2006-01-02 15:04:05" // Go 的参考时间格式
	loc, _ := time.LoadLocation("Asia/Shanghai")
	return time.ParseInLocation(layout, payTimeStr, loc)
}

func HandleNormalPayment(ctx context.Context, req *define.TradeOrderPaySuccessReq, tradeOrder *model.TradeOrder) error {
	parsePayTime, err := ParsePayTime(req.PayTime)
	if err != nil {
		return errors.Wrap(err, "解析支付时间失败")
	}

	updateTradeOrder := &model.TradeOrder{
		ID:            req.ID,
		OrderStatus:   enums.OrderStatusUnDelivered.Val(),
		PaymentStatus: enums.PaymentStatusPaid.Val(),
		PaymentTime:   &parsePayTime,
		PayOrderID:    req.RechargeOrderID,
		PaymentMethod: req.PayMethod,
	}

	if err = repo.NewTradeOrderRepo(repo.GetQuery().TradeOrder.WithContext(ctx)).UpdateById(updateTradeOrder); err != nil {
		return errors.Wrap(err, "更新订单状态失败")
	}

	if err := AddMallItemSalesVolume(ctx, tradeOrder.TradeOrderItem); err != nil {
		return errors.Wrap(err, "增加商品销量失败")
	}
	// 首页增加销量
	if err := AddHomeFeedSalesVolume(ctx, tradeOrder.TradeOrderItem, *updateTradeOrder.PaymentTime); err != nil {
		return errors.Wrap(err, "首页增加销量失败")
	}

	if err := ProcessUploadOrderToSupply(ctx, tradeOrder); err != nil {
		return errors.Wrap(err, "订单上传失败")
	}
	return nil
}

func HandleDuplicatePayment(ctx context.Context, req *define.TradeOrderPaySuccessReq, tradeOrder *model.TradeOrder) error {
	log.Ctx(ctx).Infof("重复支付订单, req:%+v", utils.Obj2JsonStr(req))
	refundReq := &define.TradeOrderRefundReq{
		RechargeOrderID: req.RechargeOrderID,
		Amount:          tradeOrder.PayAmount,
	}
	refundRes, err := PayOrderRefund(ctx, refundReq)
	log.Ctx(ctx).Infof("重复支付订单退款, refundRes:%+v", utils.Obj2JsonStr(refundRes))
	if err != nil {
		return errors.Wrap(err, "订单退款失败")
	}
	return nil
}

func HandleOrderClosed(ctx context.Context, req *define.TradeOrderPaySuccessReq, tradeOrder *model.TradeOrder) error {
	log.Ctx(ctx).Infof("订单已关闭, req:%+v", utils.Obj2JsonStr(req))
	refundReq := &define.TradeOrderRefundReq{
		RechargeOrderID: req.RechargeOrderID,
		Amount:          tradeOrder.PayAmount,
	}
	refundRes, err := PayOrderRefund(ctx, refundReq)
	log.Ctx(ctx).Infof("订单已关闭退款, refundRes:%+v", utils.Obj2JsonStr(refundRes))
	if err != nil {
		return errors.Wrap(err, "订单退款失败")
	}
	return nil
}

// UpdateTradeOrderUpdateFreight 修改物流
func UpdateTradeOrderUpdateFreight(ctx context.Context, req []*define.UpdateTradeOrderUpdateFreight) []int64 {
	log.Ctx(ctx).Infof("修改物流, req:%+v", utils.Obj2JsonStr(req))
	schema := repo.GetQuery().TradeOrder
	freightSchema := repo.GetQuery().TradeOrderFreight
	var failId []int64
	for _, item := range req {
		cNo := item.CNo
		wrapper := search.NewWrapper().Where(schema.SupplyChainOrderID.Eq(item.CNo))
		tradeOrder, err := repo.NewTradeOrderRepo(schema.WithContext(ctx)).SelectOne(wrapper)
		if err != nil {
			log.Ctx(ctx).Errorf("修改物流,查询直购订单失败, err:%+v, cNo:%+v", err, cNo)
			failId = append(failId, item.Mid)
			continue
		}
		tradeOrderItem, err := repo.NewTradeOrderItemRepo(repo.GetQuery().TradeOrderItem.WithContext(ctx)).SelectOne(search.NewWrapper().
			Where(repo.GetQuery().TradeOrderItem.OrderID.Eq(tradeOrder.ID)).Where(repo.GetQuery().TradeOrderItem.SkuID.Eq(item.SkuNo)))
		if err != nil {
			log.Ctx(ctx).Errorf("修改物流,查询订单子单失败, err:%+v, item:%+v", err, item)
			failId = append(failId, item.Mid)
			continue
		}
		// 修改物流信息
		updateTradeOrderFreight := &model.TradeOrderFreight{
			DeliveryNumber: item.FreightNo,
		}
		queryRes, err := wae.FreightQuery(ctx, &wae.FreightQueryRequest{
			FreightNo: item.FreightNo,
		})
		if err != nil {
			log.Ctx(ctx).Errorf("修改物流,物流信息变更查询物流信息失败, err:%+v, freightNo:%+v", err, item.FreightNo)
		}
		if queryRes != nil {
			freightTime := item.FreightTime.Add(time.Hour * 8)
			recordsJson := datatypes.JSON(utils.Obj2JsonStr(queryRes.Info))
			updateTradeOrderFreight.DeliveryCompany = queryRes.Com
			updateTradeOrderFreight.Status = int32(queryRes.Status)
			updateTradeOrderFreight.Checked = int32(queryRes.Checked)
			updateTradeOrderFreight.OrderItemID = tradeOrderItem.ID
			updateTradeOrderFreight.DeliveredAt = &freightTime
			updateTradeOrderFreight.Records = &recordsJson
		}
		updateFreightWrapper := search.NewWrapper().Where(freightSchema.OrderID.Eq(tradeOrder.ID), freightSchema.OrderItemID.Eq(tradeOrderItem.ID))
		err = repo.NewTradeOrderFreightRepo(freightSchema.WithContext(ctx)).Update(updateTradeOrderFreight, updateFreightWrapper)
		if err != nil {
			log.Ctx(ctx).Errorf("修改物流,修改物流失败, err:%+v, item:%+v", err, item)
			failId = append(failId, item.Mid)
			continue
		}
	}
	return failId
}

// UpdateTradeOrderFreightInfo 物流信息变更通知
func UpdateTradeOrderFreightInfo(ctx context.Context, req []*define.UpdateTradeOrderFreightInfo) []int64 {
	log.Ctx(ctx).Infof("物流信息变更通知, req:%+v", utils.Obj2JsonStr(req))
	schema := repo.GetQuery().TradeOrder
	freightSchema := repo.GetQuery().TradeOrderFreight
	var failId []int64
	for _, item := range req {
		cNo := item.CNo
		wrapper := search.NewWrapper().Where(schema.SupplyChainOrderID.Eq(item.CNo))
		tradeOrder, err := repo.NewTradeOrderRepo(schema.WithContext(ctx)).SelectOne(wrapper)
		if err != nil {
			log.Ctx(ctx).Errorf("物流信息变更查询直购订单失败, err:%+v, cNo:%+v", err, cNo)
			failId = append(failId, item.Mid)
			continue
		}
		recordsJson := datatypes.JSON(item.Info)
		freightWrapper := search.NewWrapper().Where(freightSchema.OrderID.Eq(tradeOrder.ID)).
			Where(freightSchema.DeliveryNumber.Eq(item.FreightNo))
		tradeOrderFreight, err := repo.NewTradeOrderFreightRepo(freightSchema.WithContext(ctx)).SelectOne(freightWrapper)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 新增
				saveTradeOrderFreight := &model.TradeOrderFreight{
					ID:              snowflakeutl.GenerateID(),
					OrderID:         tradeOrder.ID,
					DeliveryNumber:  item.FreightNo,
					DeliveryCompany: item.Com,
					Status:          item.Status,
					Checked:         item.Checked,
					Records:         &recordsJson,
				}
				err = repo.NewTradeOrderFreightRepo(freightSchema.WithContext(ctx)).Save(saveTradeOrderFreight)
				if err != nil {
					log.Ctx(ctx).Errorf("订单物流信息保存失败, err:%+v, item:%+v", err, item)
					failId = append(failId, item.Mid)
				}
				continue
			}
			log.Ctx(ctx).Errorf("物流信息变更查询物流信息失败, err:%+v, cNo:%+v", err, cNo)
			failId = append(failId, item.Mid)
			continue
		}
		// 修改
		updateTradeOrderFreight := &model.TradeOrderFreight{
			DeliveryNumber:  item.FreightNo,
			DeliveryCompany: item.Com,
			Status:          item.Status,
			Checked:         item.Checked,
			Records:         &recordsJson,
		}
		updateFreightWrapper := search.NewWrapper().Where(freightSchema.ID.Eq(tradeOrderFreight.ID))
		err = repo.NewTradeOrderFreightRepo(freightSchema.WithContext(ctx)).Update(updateTradeOrderFreight, updateFreightWrapper)
		if err != nil {
			log.Ctx(ctx).Errorf("订单修改物流失败, err:%+v, item:%+v", err, item)
			failId = append(failId, item.Mid)
			continue
		}
	}
	return failId
}

// FinishTradeOrderTradeOrder 自动收货
func FinishTradeOrderTradeOrder(ctx context.Context) error {
	now := time.Now()
	updateTradeOrder := &model.TradeOrder{
		OrderStatus: enums.OrderStatusCompleted.Val(),
		FinishedAt:  &now,
	}
	schema := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(schema.OrderStatus.Eq(enums.OrderStatusDelivered.Val()), schema.DeliveredAt.Lte(time.Now().Add(-constant.OrderFinishDay)))
	err := repo.NewTradeOrderRepo(schema.WithContext(ctx)).Update(updateTradeOrder, wrapper)
	if err != nil && !errors.Is(err, repo.UpdateFail) {
		return errors.Wrap(err, "自动收货失败")
	}
	return nil
}

// SyncAdminTradeOrderFreight 同步物流信息
func SyncAdminTradeOrderFreight(ctx context.Context, req *define.SyncAdminTradeOrderFreightReq) error {
	freightSchema := repo.GetQuery().TradeOrderFreight
	freight, err := repo.NewTradeOrderFreightRepo(freightSchema.WithContext(ctx)).SelectOne(
		search.NewWrapper().Where(freightSchema.OrderItemID.Eq(req.OrderItemId)))
	if err != nil {
		log.Ctx(ctx).Errorf("查询物流信息失败, err:%+v", err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.Wrap(err, "物流信息不存在")
		}
		return errors.Wrap(err, "查询物流信息失败")
	}
	queryRes, err := wae.FreightQuery(ctx, &wae.FreightQueryRequest{
		FreightNo: freight.DeliveryNumber,
	})
	if queryRes != nil {
		recordsJson := datatypes.JSON(utils.Obj2JsonStr(queryRes.Info))
		updateTradeOrderFreight := &model.TradeOrderFreight{
			Status:  int32(queryRes.Status),
			Checked: int32(queryRes.Checked),
			Records: &recordsJson,
		}
		updateFreightWrapper := search.NewWrapper().Where(freightSchema.OrderItemID.Eq(req.OrderItemId))
		err = repo.NewTradeOrderFreightRepo(freightSchema.WithContext(ctx)).Update(updateTradeOrderFreight, updateFreightWrapper)
		if err != nil {
			log.Ctx(ctx).Errorf("修改物流信息失败, err:%+v", err)
			return errors.Wrap(err, "修改物流信息失败")
		}
	}
	return nil
}

// GetTradeOrderFreight 查询物流信息
func GetTradeOrderFreight(ctx context.Context, ids []int64) map[int64][]*model.TradeOrderFreight {
	freight := make(map[int64][]*model.TradeOrderFreight)
	if ids == nil || len(ids) == 0 {
		return freight
	}
	freightSchema := repo.GetQuery().TradeOrderFreight
	list, err := repo.NewTradeOrderFreightRepo(freightSchema.WithContext(ctx)).SelectList(search.NewWrapper().Where(freightSchema.OrderID.In(ids...)))
	if err != nil {
		log.Ctx(ctx).Errorf("查询物流信息失败, err:%+v", err)
		return freight
	}
	for _, item := range list {
		freight[item.OrderID] = append(freight[item.OrderID], item)
	}
	return freight
}

func CheckUserPendingOrder(ctx context.Context, userId string, mallItemId int64) (*model.TradeOrder, error) {
	to := repo.GetQuery().TradeOrder
	toi := repo.GetQuery().TradeOrderItem
	order, err := to.WithContext(ctx).Select(to.ID, to.OrderStatus, to.PaymentStatus, to.CreatedAt).
		Where(to.UserID.Eq(userId), to.OrderStatus.Eq(enums.OrderStatusUnPaid.Val())).
		Join(toi, toi.OrderID.EqCol(to.ID), toi.MallItemID.Eq(mallItemId)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return order, nil
}
