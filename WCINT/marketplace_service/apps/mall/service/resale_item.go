package service

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/locker"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/pkg/utils/kafka_util"
	"marketplace_service/pkg/utils/snowflakeutl"
	"marketplace_service/third_party/wat"
	"marketplace_service/third_party/yc_open"
	"strconv"
	"time"
)

// AddResaleItem 添加转卖商品
func (s *Service) AddResaleItem(req *define.AddResaleItemReq) (*define.AddResaleItemResp, error) {
	// 判断是否存在
	ri := repo.GetQuery().ResaleItem
	wrapper := search.NewWrapper().Where(ri.ItemID.Eq(req.ItemID))
	resaleItemCount, err := repo.NewResaleItemRepo(ri.WithContext(s.ctx)).Count(wrapper)
	if err != nil {
		return nil, err
	}
	if resaleItemCount > 0 {
		return nil, define.MS200016Err
	}
	steamItem, err := yc_open.QuerySteamItemBySkuNo(s.ctx, req.SkuID)
	if err != nil {
		return nil, errors.Wrap(err, "查询商品信息失败")
	}
	if steamItem == nil {
		log.Ctx(s.ctx).Warnf("云仓商品 SKU:%s 不存在", req.SkuID)
		return nil, define.MS200005Err
	}
	if steamItem.SupplyStatus != 1 {
		log.Ctx(s.ctx).Warnf("商品 SKU:%s 当前未在供应链中启用或不可售", req.SkuID)
		return nil, define.MS200006Err
	}
	itemInfo, err := logic.GetItemInfoByRemote(s.ctx, req.ItemID)
	if err != nil {
		return nil, define.MS200001Err
	}
	// 默认为供应链最低进价*105%
	marketPrice := steamItem.PurchasePrice * 105 / 100
	resaleItem := &model.ResaleItem{
		ID:          snowflakeutl.GenerateID(),
		ItemID:      req.ItemID,
		SkuID:       req.SkuID,
		ItemName:    itemInfo.ItemName,
		ItemSpecs:   itemInfo.Specs,
		ItemIconURL: itemInfo.IconUrl,
		MarketPrice: marketPrice,
		Priority:    req.Priority,
		CreatedBy:   s.GetAdminId(),
		UpdatedBy:   s.GetAdminId(),
	}
	// 设置品牌
	if len(itemInfo.TrademarkInfo) > 0 {
		resaleItem.TrademarkID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
	}
	// 设置 CategoryID
	if err := logic.SetResaleItemCategoryID(s.ctx, resaleItem, itemInfo); err != nil {
		return nil, err
	}

	// 设置 IPID
	if err := logic.SetResaleItemIPID(s.ctx, resaleItem, itemInfo); err != nil {
		return nil, err
	}
	if err := repo.NewResaleItemRepo(ri.WithContext(s.ctx)).Save(resaleItem); err != nil {
		return nil, errors.Wrap(err, "保存转卖商品失败")
	}
	return &define.AddResaleItemResp{ID: resaleItem.ID}, nil
}

// EditResaleItemPriority 更新转卖商品优先级
func (s *Service) EditResaleItemPriority(req *define.EditResaleItemPriorityReq) (*define.EditResaleItemPriorityResp, error) {
	ri := repo.GetQuery().ResaleItem
	wrapper := search.NewWrapper().Where(ri.ID.Eq(req.ID))
	resaleItem, err := repo.NewResaleItemRepo(ri.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询转卖商品失败")
	}
	priority := int32(0)
	if req.Priority != nil {
		priority = *req.Priority
	}
	updateItem := &model.ResaleItem{
		ID:        req.ID,
		Priority:  priority,
		UpdatedBy: s.GetAdminId(),
	}
	if err := repo.NewResaleItemRepo(ri.WithContext(s.ctx).Select(ri.Priority, ri.UpdatedBy)).UpdateById(updateItem); err != nil {
		return nil, errors.Wrap(err, "更新转卖商品优先级失败")
	}
	sContext := s.NewContextWithSpanContext(s.ctx)
	go func(ctx context.Context, itemId string) {
		if err := logic.UpdateHomeFeed(ctx, itemId); err != nil {
			log.Ctx(ctx).Errorf("[Service.EditResaleItemPriority]修改转卖商品优先级更新首页商品失败: %v", err)
		}
	}(sContext, resaleItem.ItemID)
	return &define.EditResaleItemPriorityResp{ID: resaleItem.ID}, nil
}

// EditResaleItem 更新转卖商品转卖设置
func (s *Service) EditResaleItem(req *define.EditResaleItemReq) (*define.EditResaleItemResp, error) {
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewResaleItemLockActionLock(locker.UpdateResaleItem, strconv.FormatInt(req.ID, 10))))
	if !l.Lock(s.ctx) {
		return nil, errors.New("转卖商品更新中，请稍后再试~")
	}
	defer l.UnLock(s.ctx)
	ri := repo.GetQuery().ResaleItem
	wrapper := search.NewWrapper().Where(ri.ID.Eq(req.ID))
	resaleItem, err := repo.NewResaleItemRepo(ri.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询转卖商品失败")
	}
	// 判断转卖标准状态
	standardConfig, err := logic.GetResaleStandardConfig(s.ctx)
	if err != nil {
		return nil, errors.Wrap(err, "获取转卖标准配置失败")
	}
	if standardConfig.ResaleStatus == 0 && *req.ResaleStatus == enums.ResaleItemResaleStatusOpen.Val() {
		return nil, define.MS200029Err
	}
	updateResaleItem := &model.ResaleItem{
		ID:                 req.ID,
		ResaleStatus:       *req.ResaleStatus,
		TradeFrequencyType: req.TradeFrequencyType,
		IntervalMinutes:    req.IntervalMinutes,
		IsCustomPriceLimit: *req.IsCustomPriceLimit,
		MinPriceRatio:      req.MinPriceRatio,
		MaxPriceRatio:      req.MaxPriceRatio,
		UpdatedBy:          s.GetAdminId(),
	}
	if err := repo.NewResaleItemRepo(ri.WithContext(s.ctx).
		Select(ri.ResaleStatus, ri.TradeFrequencyType, ri.IntervalMinutes, ri.IsCustomPriceLimit, ri.MinPriceRatio, ri.MaxPriceRatio, ri.UpdatedBy)).
		UpdateById(updateResaleItem); err != nil {
		return nil, errors.Wrap(err, "更新转卖商品失败")
	}
	// 更新首页
	if err := logic.UpdateHomeFeed(s.ctx, resaleItem.ItemID); err != nil {
		return nil, errors.Wrap(err, "更新首页商品失败")
	}
	// 下架当前转卖商品所有挂单
	if resaleItem.ResaleStatus == enums.ResaleItemResaleStatusOpen.Val() && *req.ResaleStatus == enums.ResaleItemResaleStatusClose.Val() {
		err := logic.TakeDownResaleListingsByItemId(s.ctx, resaleItem.ItemID, s.GetAdminId())
		if err != nil {
			log.Ctx(s.ctx).Errorf("下架转卖挂单失败,err:%+v", err)
			return nil, err
		}
	}
	return &define.EditResaleItemResp{ID: resaleItem.ID}, nil
}

// EditResaleItemStatus 修改转卖商品状态
func (s *Service) EditResaleItemStatus(req *define.EditResaleItemStatusReq) (*define.EditResaleItemStatusResp, error) {
	// 初始化锁 限制更新商品速率
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewResaleItemLockActionLock(locker.UpdateResaleItem, strconv.FormatInt(req.ID, 10))))
	if !l.Lock(s.ctx) {
		return nil, errors.New("转卖商品更新中，请稍后再试~")
	}
	defer l.UnLock(s.ctx)
	ri := repo.GetQuery().ResaleItem
	wrapper := search.NewWrapper().Where(ri.ID.Eq(req.ID))
	resaleItem, err := repo.NewResaleItemRepo(ri.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询转卖商品失败")
	}
	if err := logic.ValidateResaleItemStatusTransition(resaleItem.Status, req.Status); err != nil {
		return nil, err
	}
	updateResaleItem := &model.ResaleItem{
		ID:        req.ID,
		Status:    req.Status,
		UpdatedBy: s.GetAdminId(),
	}
	if req.Status == enums.ResaleItemStatusUp.Val() {
		steamItem, err := yc_open.QuerySteamItemBySkuNo(s.ctx, resaleItem.SkuID)
		if err != nil {
			return nil, errors.Wrap(err, "查询商品信息失败")
		}
		if steamItem == nil {
			log.Ctx(s.ctx).Warnf("云仓商品 SKU:%s 不存在", resaleItem.SkuID)
			return nil, define.MS200005Err
		}
		if steamItem.SupplyStatus != 1 {
			log.Ctx(s.ctx).Warnf("商品 SKU:%s 当前未在供应链中启用或不可售", resaleItem.SkuID)
			return nil, define.MS200006Err
		}
		// 更新商品信息为最新
		itemInfo, err := logic.GetItemInfoByRemote(s.ctx, resaleItem.ItemID)
		if err != nil {
			return nil, define.MS200001Err
		}
		if itemInfo != nil {
			updateResaleItem.ItemName = itemInfo.ItemName
			updateResaleItem.ItemSpecs = itemInfo.Specs
			updateResaleItem.ItemIconURL = itemInfo.IconUrl
			if len(itemInfo.TrademarkInfo) > 0 {
				updateResaleItem.TrademarkID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
			}
			// 设置 CategoryID
			if err := logic.SetResaleItemCategoryID(s.ctx, updateResaleItem, itemInfo); err != nil {
				return nil, err
			}

			// 设置 IPID
			if err := logic.SetResaleItemIPID(s.ctx, updateResaleItem, itemInfo); err != nil {
				return nil, err
			}
		}
		// 设置上架时间
		now := time.Now()
		updateResaleItem.ShelfTime = &now
	}
	if err := repo.NewResaleItemRepo(ri.WithContext(s.ctx)).UpdateById(updateResaleItem); err != nil {
		return nil, errors.Wrap(err, "更新转卖商品失败")
	}
	// 更新首页
	if err := logic.UpdateHomeFeed(s.ctx, resaleItem.ItemID); err != nil {
		return nil, errors.Wrap(err, "更新首页商品失败")
	}
	// 更新云仓引导标识
	upsertSteamItemsExtendReq := yc_open.UpsertSteamItemsExtendReq{
		ItemID:     resaleItem.ItemID,
		SkuNo:      resaleItem.SkuID,
		ItemName:   resaleItem.ItemName,
		IsGuide:    false,
		DataSource: 2,
		Type:       2,
	}
	if req.Status == enums.ResaleItemStatusUp.Val() {
		upsertSteamItemsExtendReq.IsGuide = true
	}
	err = yc_open.UpsertSteamItemsExtend(s.ctx, &upsertSteamItemsExtendReq)
	if err != nil {
		log.Ctx(s.ctx).Errorf("更新云仓商品扩展记录失败: %+v", err)
		return nil, errors.Wrap(err, "更新云仓商品扩展记录失败")
	}
	// 如果是下架 需要下架所有的挂单
	if resaleItem.Status == enums.ResaleItemStatusUp.Val() && req.Status == enums.ResaleItemStatusDown.Val() {
		err := logic.TakeDownResaleListingsByItemId(s.ctx, resaleItem.ItemID, s.GetAdminId())
		if err != nil {
			log.Ctx(s.ctx).Errorf("下架转卖挂单失败,err:%+v", err)
			return nil, err
		}
	}
	return &define.EditResaleItemStatusResp{ID: req.ID}, nil
}

// GetResaleItemList 获取转卖商品分页列表
func (s *Service) GetResaleItemList(req *define.ResaleItemPageReq) (*define.ResaleItemPageResp, error) {
	ri := repo.GetQuery().ResaleItem
	resaleItems, count, err := repo.NewResaleItemRepo(ri.WithContext(s.ctx).Order(ri.CreatedAt.Desc())).QuickSelectPage(req)
	if err != nil {
		return nil, errors.Wrap(err, "查询商品列表失败")
	}
	if len(resaleItems) == 0 {
		return &define.ResaleItemPageResp{
			List:  []*define.ResaleItemPageData{},
			Total: count,
		}, nil
	}
	itemIds := make([]string, len(resaleItems))
	ipIds := make([]string, len(resaleItems))
	categoryIds := make([]string, len(resaleItems))
	trademarkIds := make([]string, len(resaleItems))
	for i, item := range resaleItems {
		itemIds[i] = item.ItemID
		ipIds[i] = item.IPID
		categoryIds[i] = item.CategoryID
		trademarkIds[i] = item.TrademarkID
	}
	itemInfoMap, _ := logic.GetItemInfoMap(s.ctx, itemIds...)
	ipInfoMap, _ := logic.GetIpInfoMap(s.ctx, ipIds...)
	categoryInfoMap, _ := logic.GetItemClassifyInfoMap(s.ctx, categoryIds...)
	trademarkInfoMap, _ := logic.GetTrademarksInfoMap(s.ctx, trademarkIds...)
	itemTradeConfigMap, err := logic.BatchGetResaleItemTradeConfigMap(s.ctx, resaleItems)
	if err != nil {
		return nil, err
	}
	resaleItemList := make([]*define.ResaleItemPageData, 0, len(resaleItems))
	for _, resaleItem := range resaleItems {
		itemTradeConfig := itemTradeConfigMap[resaleItem.ItemID]
		data := &define.ResaleItemPageData{
			ID:                 resaleItem.ID,
			Status:             resaleItem.Status,
			ItemID:             resaleItem.ItemID,
			SkuID:              resaleItem.SkuID,
			ItemName:           resaleItem.ItemName,
			IPID:               resaleItem.IPID,
			ItemSpecs:          resaleItem.ItemSpecs,
			CategoryID:         resaleItem.CategoryID,
			TrademarkID:        resaleItem.TrademarkID,
			MarketPrice:        resaleItem.MarketPrice,
			ResaleStatus:       resaleItem.ResaleStatus,
			TradeFrequencyType: resaleItem.TradeFrequencyType,
			IsCustomPriceLimit: resaleItem.IsCustomPriceLimit,
			Priority:           resaleItem.Priority,
			UpdatedBy:          resaleItem.UpdatedBy,
			UpdatedAt:          resaleItem.UpdatedAt,
		}
		if itemInfo, ok := itemInfoMap[resaleItem.ItemID]; ok {
			data.ItemIconURL = itemInfo.IconUrl
		}
		if ipInfo, ok := ipInfoMap[resaleItem.IPID]; ok {
			data.IPName = ipInfo.Name
		}
		if categoryInfo, ok := categoryInfoMap[resaleItem.CategoryID]; ok {
			data.CategoryName = categoryInfo.Name
		}
		if trademarkInfo, ok := trademarkInfoMap[resaleItem.TrademarkID]; ok {
			data.TrademarkName = trademarkInfo.Name
		}
		if itemTradeConfig != nil {
			data.MaxLimitPrice = itemTradeConfig.MaxLimitPrice
			data.MinLimitPrice = itemTradeConfig.MinLimitPrice
		}
		resaleItemList = append(resaleItemList, data)
	}
	return &define.ResaleItemPageResp{
		List:  resaleItemList,
		Total: count,
	}, nil
}

// ResaleItemBuy 转卖商品购买
func (s *Service) ResaleItemBuy(req *define.ResaleItemBuyReq) (*define.ResaleItemBuyResp, error) {
	userId := s.GetUserId()
	// 初始化锁 用户不能同时转卖商品购买 todo yangxi test
	//l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewResaleOrderLock(s.GetUserId(), locker.ResaleBuy)))
	//if !l.Lock(s.ctx) {
	//	return nil, response.TooManyRequestErr
	//}
	//var locks []*redis_locker.Locker
	//defer func() {
	//	l.UnLock(s.ctx)
	//	if locks != nil && len(locks) > 0 {
	//		for _, lock := range locks {
	//			lock.UnLock(s.ctx)
	//		}
	//	}
	//}()
	// 1: 校验出售单
	err, items := logic.ResaleListingsBuyVerify(s.ctx, req, userId)
	if err != nil {
		log.Ctx(s.ctx).Errorf("ResaleItemBuy,转卖商品购买,转卖挂单购买校验失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, err
	}
	// 2: 校验密码,校验余额
	err = wat.CheckPayPwdAndBalance(s.ctx, &wat.CheckPayPwdAndBalanceForm{
		Pwd:    req.Password,
		Amount: req.TotalAmount,
		UserId: userId,
	})
	if err != nil {
		log.Ctx(s.ctx).Errorf("ResaleItemBuy,转卖商品购买,校验密码和余额失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		if err == define.MS200025Err || err == define.MS200026Err {
			return nil, err
		}
		return nil, global.CommonErr
	}
	// 2.1 锁定用户物品 todo yangxi test
	//locks, err = s.AddResaleUserItemLock(items)
	//if err != nil {
	//	log.Ctx(s.ctx).Errorf("ResaleItemBuy,转卖商品购买,加锁失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
	//	return nil, global.CommonErr
	//}
	req.BatchId = snowflakeutl.GenerateID()
	// 3: 创建订单，锁定出售单
	resaleOrderList, resaleOrderItemList, err := logic.CreateResaleOrderAndLock(s.ctx, userId, req, items)
	if err != nil {
		log.Ctx(s.ctx).Errorf("ResaleItemBuy,转卖商品购买,创建转卖订单失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	// 3.1: 锁定物品-云仓
	err = logic.ResaleLockUserItem(s.ctx, items, resaleOrderList, resaleOrderItemList)
	if err != nil {
		log.Ctx(s.ctx).Errorf("ResaleItemBuy,转卖商品购买,锁定用户物品失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	// 4: 扣余额（买家扣款，卖家加钱，手续费收取）
	paySuccessOrderIds := make([]int64, 0)
	for _, order := range resaleOrderList {
		err = logic.ResaleOrderPay(s.ctx, order)
		if err != nil {
			log.Ctx(s.ctx).Errorf("ResaleItemBuy,转卖商品购买,扣余额失败, order:%+v,err:%+v", utils.Obj2JsonStr(order), err)
			continue
		}
		paySuccessOrderIds = append(paySuccessOrderIds, order.ID)
		// 此时已经完成，卖家物品锁定，卖家余额增加，买家余额扣减，还需要完成买家物品发放
		//发送Kafka
		resaleItemTransfer := &define.ResaleItemTransfer{ID: order.ID}
		_ = kafka_util.SendMsg(s.ctx, constant.ResaleItemTransfer, resaleItemTransfer)
	}
	return &define.ResaleItemBuyResp{
		PaySuccessOrderIds: paySuccessOrderIds,
	}, nil
}

// GetResaleItemDetail 获取转卖商品详情
func (s *Service) GetResaleItemDetail(req *define.ResaleItemDetailReq) (*define.ResaleItemDetailResp, error) {
	ri := repo.GetQuery().ResaleItem
	wrapper := search.NewWrapper().Where(ri.ID.Eq(req.ID))
	resaleItem, err := repo.NewResaleItemRepo(ri.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		log.Error("查询转卖商品失败", err)
	}
	resaleItemDetail := &define.ResaleItemDetailResp{
		ID:                 resaleItem.ID,
		ItemID:             resaleItem.ItemID,
		SkuID:              resaleItem.SkuID,
		ItemName:           resaleItem.ItemName,
		ItemSpecs:          resaleItem.ItemSpecs,
		IPID:               resaleItem.IPID,
		CategoryID:         resaleItem.CategoryID,
		TrademarkID:        resaleItem.TrademarkID,
		MarketPrice:        resaleItem.MarketPrice,
		ResaleStatus:       resaleItem.ResaleStatus,
		TradeFrequencyType: resaleItem.TradeFrequencyType,
		IntervalMinutes:    resaleItem.IntervalMinutes,
		IsCustomPriceLimit: resaleItem.IsCustomPriceLimit,
		MinPriceRatio:      resaleItem.MinPriceRatio,
		MaxPriceRatio:      resaleItem.MaxPriceRatio,
	}
	itemInfo, _ := logic.GetItemInfo(s.ctx, resaleItem.ItemID)
	if itemInfo != nil {
		resaleItemDetail.ItemIconURL = itemInfo.IconUrl
		resaleItemDetail.DetailH5 = itemInfo.DetailH5
		resaleItemDetail.ImageInfos = itemInfo.ImageInfos
		resaleItemDetail.SellListings = itemInfo.SellListings
	}
	ipInfoMap, _ := logic.GetIpInfoMap(s.ctx, resaleItem.IPID)
	categoryInfoMap, _ := logic.GetItemClassifyInfoMap(s.ctx, resaleItem.CategoryID)
	trademarkInfoMap, _ := logic.GetTrademarksInfoMap(s.ctx, resaleItem.TrademarkID)
	if ipInfo, ok := ipInfoMap[resaleItem.IPID]; ok {
		resaleItemDetail.IPName = ipInfo.Name
	}
	if categoryInfo, ok := categoryInfoMap[resaleItem.CategoryID]; ok {
		resaleItemDetail.CategoryName = categoryInfo.Name
	}
	if trademarkInfo, ok := trademarkInfoMap[resaleItem.TrademarkID]; ok {
		resaleItemDetail.TrademarkName = trademarkInfo.Name
	}
	return resaleItemDetail, nil
}
