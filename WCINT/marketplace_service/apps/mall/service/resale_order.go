package service

import (
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/pkg/utils/excelize_lib"
	"time"
)

// GetAdminResaleOrderList 管理端获取转卖订单分页列表
func (s *Service) GetAdminResaleOrderList(req *define.GetAdminResaleOrderListReq) (*define.GetAdminResaleOrderListResp, error) {
	to := repo.GetQuery().ResaleOrder
	resaleOrders, count, err := repo.NewResaleOrderRepo(to.WithContext(s.ctx).Order(to.ID.Desc())).QuickSelectPage(req)
	if err != nil {
		return nil, errors.Wrap(err, "查询转卖订单分页列表失败")
	}
	if len(resaleOrders) == 0 {
		return &define.GetAdminResaleOrderListResp{
			List:  []*define.GetAdminResaleOrderListData{},
			Total: count,
		}, nil
	}
	list := make([]*define.GetAdminResaleOrderListData, 0)
	var userIDs []string
	for _, resaleOrder := range resaleOrders {
		userIDs = append(userIDs, resaleOrder.BuyerID)
		userIDs = append(userIDs, resaleOrder.SellerID)
	}
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询转卖订单分页列表失败,获取用户信息失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	for _, resaleOrder := range resaleOrders {
		data := &define.GetAdminResaleOrderListData{
			ID:               resaleOrder.ID,
			ResaleListingsId: resaleOrder.ResaleListingsID,
			SkuId:            resaleOrder.SkuID,
			ItemId:           resaleOrder.ItemID,
			ItemName:         resaleOrder.ItemName,
			ItemIconUrl:      resaleOrder.ItemIconURL,
			ItemSpecs:        resaleOrder.ItemSpecs,
			SalePrice:        resaleOrder.SalePrice,
			TotalAmount:      resaleOrder.TotalAmount,
			PayAmount:        resaleOrder.PayAmount,
			TotalFee:         resaleOrder.TotalFee,
			Quantity:         resaleOrder.Quantity,
			BuyerId:          resaleOrder.BuyerID,
			BuyerPhone:       resaleOrder.BuyerPhone,
			SellerId:         resaleOrder.SellerID,
			SellerPhone:      resaleOrder.SellerPhone,
			Status:           resaleOrder.Status,
			Terminal:         resaleOrder.Terminal,
			CreatedAt:        resaleOrder.CreatedAt,
		}
		if userDetail, ok := userDetailMap[resaleOrder.BuyerID]; ok {
			data.BuyerNickname = userDetail.Nickname
		}
		if userDetail, ok := userDetailMap[resaleOrder.SellerID]; ok {
			data.SellerNickname = userDetail.Nickname
		}
		list = append(list, data)
	}
	return &define.GetAdminResaleOrderListResp{
		List:  list,
		Total: count,
	}, nil
}

// GetAdminResaleOrderDetail 获取转卖订单详情
func (s *Service) GetAdminResaleOrderDetail(req *define.GetAdminResaleOrderDetailReq) (*define.GetAdminResaleOrderDetailResp, error) {
	to := repo.GetQuery().ResaleOrder
	resaleOrder, err := repo.NewResaleOrderRepo(to.WithContext(s.ctx)).SelectOne(search.NewWrapper().Where(to.ID.Eq(req.ID)))
	if err != nil {
		return nil, errors.Wrap(err, "查询订单详情失败")
	}
	if resaleOrder == nil {
		return nil, errors.New("订单不存在")
	}
	userIDs := []string{resaleOrder.BuyerID, resaleOrder.SellerID}
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单详情失败,获取用户信息失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	resp := &define.GetAdminResaleOrderDetailResp{
		ID:         resaleOrder.ID,
		BuyerId:    resaleOrder.BuyerID,
		SellerId:   resaleOrder.SellerID,
		Status:     resaleOrder.Status,
		Terminal:   resaleOrder.Terminal,
		AppVersion: resaleOrder.AppVersion,
		PaymentAt:  resaleOrder.PaymentAt,
		TransferAt: resaleOrder.TransferAt,
		FinishedAt: resaleOrder.FinishedAt,
		CreatedAt:  resaleOrder.CreatedAt,
	}
	if userDetail, ok := userDetailMap[resaleOrder.BuyerID]; ok {
		resp.BuyerNickname = userDetail.Nickname
	}
	if userDetail, ok := userDetailMap[resaleOrder.SellerID]; ok {
		resp.SellerNickname = userDetail.Nickname
	}
	resp.ItemInfo = &define.GetAdminResaleOrderDetailData{
		SkuId:            resaleOrder.SkuID,
		ItemId:           resaleOrder.ItemID,
		ItemName:         resaleOrder.ItemName,
		ItemIconUrl:      resaleOrder.ItemIconURL,
		ItemSpecs:        resaleOrder.ItemSpecs,
		SalePrice:        resaleOrder.SalePrice,
		TotalAmount:      resaleOrder.TotalAmount,
		PayAmount:        resaleOrder.PayAmount,
		TotalFee:         resaleOrder.TotalFee,
		Quantity:         resaleOrder.Quantity,
		ResaleListingsId: resaleOrder.ResaleListingsID,
	}
	return resp, nil
}

// ExportResaleOrderList 导出转卖订单列表
func (s *Service) ExportResaleOrderList(req *define.GetAdminResaleOrderListReq) error {
	dataList := make([]*define.GetAdminResaleOrderListData, 0)
	for i := 1; i < 11000; i++ {
		req.PageSize = 100
		req.Page = i
		page, err := s.GetAdminResaleOrderList(req)
		if err != nil {
			return err
		}
		if len(page.List) == 0 {
			break
		}
		dataList = append(dataList, page.List...)
	}
	excel := excelize_lib.NewExcel()
	dataKey := make([]map[string]string, 0)
	dataKey = append(dataKey, map[string]string{"key": "created_at", "title": "下单时间", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "id", "title": "合同编号", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_id", "title": "商品ID", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "sku_id", "title": "SKU ID", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_name", "title": "商品名称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_specs", "title": "规格", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "sale_price", "title": "售价", "width": "20", "is_num": "1"})
	dataKey = append(dataKey, map[string]string{"key": "quantity", "title": "购买数量", "width": "20", "is_num": "1"})
	dataKey = append(dataKey, map[string]string{"key": "total_amount", "title": "订单金额", "width": "20", "is_num": "1"})
	dataKey = append(dataKey, map[string]string{"key": "total_fee", "title": "手续费", "width": "20", "is_num": "1"})
	dataKey = append(dataKey, map[string]string{"key": "buyer_id", "title": "买家ID", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "buyer_nickname", "title": "买家昵称", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "resale_listings_id", "title": "出售单号", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "seller_id", "title": "卖家用户ID", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "seller_nickname", "title": "卖家昵称", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "status", "title": "订单状态", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "terminal", "title": "下单终端", "width": "30", "is_num": "0"})
	data := make([]map[string]interface{}, 0)
	for _, idata := range dataList {

		data = append(data, map[string]interface{}{
			"created_at":         utils.GetDateTimeFormatStr(idata.CreatedAt),
			"id":                 utils.StrVal(idata.ID),
			"item_id":            idata.ItemId,
			"sku_id":             idata.SkuId,
			"item_name":          idata.ItemName,
			"item_specs":         idata.ItemSpecs,
			"sale_price":         "¥" + utils.FenToYuanString(idata.SalePrice),
			"quantity":           idata.Quantity,
			"total_amount":       "¥" + utils.FenToYuanString(idata.TotalAmount),
			"total_fee":          "¥" + utils.FenToYuanString(idata.TotalFee),
			"buyer_id":           idata.BuyerId,
			"buyer_nickname":     idata.BuyerNickname,
			"resale_listings_id": idata.ResaleListingsId,
			"seller_id":          idata.SellerId,
			"seller_nickname":    idata.SellerNickname,
			"status":             enums.ResaleOrderStatusMap[idata.Status],
			"terminal":           enums.TerminalMap[idata.Terminal],
		})
	}

	err := excel.ExportToStream(dataKey, data, s.ctx.(*gin.Context))
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.ExportResaleOrderList] ExportToWeb err:%v", err)
		return response.SystemErr
	}
	return nil
}

// GetWebResaleOrderBuyList 查询我购买的转卖订单列表
func (s *Service) GetWebResaleOrderBuyList(req *define.GetWebResaleOrderBuyListReq) (*define.GetWebResaleOrderBuyListResp, error) {
	schema := repo.GetQuery().ResaleOrder
	wrapper := search.NewWrapper().Where(schema.BuyerID.Eq(s.GetUserId()), schema.Status.In(enums.ResaleOrderStatusShowList...)).OrderBy(schema.ID.Desc())
	if req.Status != nil {
		wrapper = wrapper.Where(schema.Status.Eq(*req.Status))
	}
	resaleOrders, count, err := repo.NewResaleOrderRepo(schema.WithContext(s.ctx)).SelectPage(wrapper, req)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取我购买的转卖订单列表失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	if len(resaleOrders) == 0 {
		return &define.GetWebResaleOrderBuyListResp{
			List:  []*define.GetWebResaleOrderBuyListData{},
			Total: count,
		}, nil
	}

	userIDs := make([]string, 0, len(resaleOrders))
	for _, resaleOrder := range resaleOrders {
		userIDs = append(userIDs, resaleOrder.SellerID)
	}
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取我购买的转卖订单列表,获取用户信息失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	list := make([]*define.GetWebResaleOrderBuyListData, 0, len(resaleOrders))
	for _, resaleOrder := range resaleOrders {
		data := &define.GetWebResaleOrderBuyListData{
			ID:          resaleOrder.ID,
			ItemId:      resaleOrder.ItemID,
			ItemName:    resaleOrder.ItemName,
			ItemIconUrl: resaleOrder.ItemIconURL,
			ItemSpecs:   resaleOrder.ItemSpecs,
			SalePrice:   resaleOrder.SalePrice,
			TotalAmount: resaleOrder.TotalAmount,
			Quantity:    resaleOrder.Quantity,
			Status:      resaleOrder.Status,
			SoldOut:     logic.GetItemSoldOut(s.ctx, resaleOrder.ItemID),
		}
		if userDetail, ok := userDetailMap[resaleOrder.SellerID]; ok {
			data.SellerNickname = utils.MaskNickname(userDetail.Nickname)
			data.SellerAvatar = userDetail.Avatar
		}
		list = append(list, data)
	}

	return &define.GetWebResaleOrderBuyListResp{
		List:  list,
		Total: count,
	}, nil
}

// GetWebResaleOrderSaleList 查询我出售的转卖订单列表
func (s *Service) GetWebResaleOrderSaleList(req *define.GetWebResaleOrderSaleListReq) (*define.GetWebResaleOrderSaleListResp, error) {
	schema := repo.GetQuery().ResaleOrder
	wrapper := search.NewWrapper().Where(schema.SellerID.Eq(s.GetUserId()), schema.Status.Eq(enums.ResaleOrderStatusCompleted.Val())).OrderBy(schema.ID.Desc())
	if req.Status != nil {
		wrapper = wrapper.Where(schema.Status.Eq(*req.Status))
	}
	resaleOrders, count, err := repo.NewResaleOrderRepo(schema.WithContext(s.ctx)).SelectPage(wrapper, req)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询我出售的转卖订单列表失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	if len(resaleOrders) == 0 {
		return &define.GetWebResaleOrderSaleListResp{
			List:  []*define.GetWebResaleOrderSaleListData{},
			Total: count,
		}, nil
	}

	userIDs := make([]string, 0, len(resaleOrders))
	for _, resaleOrder := range resaleOrders {
		userIDs = append(userIDs, resaleOrder.BuyerID)
	}
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询我出售的转卖订单列表失败,获取用户信息失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}

	list := make([]*define.GetWebResaleOrderSaleListData, 0, len(resaleOrders))
	for _, resaleOrder := range resaleOrders {
		data := &define.GetWebResaleOrderSaleListData{
			ID:          resaleOrder.ID,
			ItemId:      resaleOrder.ItemID,
			ItemName:    resaleOrder.ItemName,
			ItemIconUrl: resaleOrder.ItemIconURL,
			ItemSpecs:   resaleOrder.ItemSpecs,
			SalePrice:   resaleOrder.SalePrice,
			TotalAmount: resaleOrder.TotalAmount,
			Quantity:    resaleOrder.Quantity,
			Status:      resaleOrder.Status,
			SoldOut:     logic.GetItemSoldOut(s.ctx, resaleOrder.ItemID),
		}
		if userDetail, ok := userDetailMap[resaleOrder.BuyerID]; ok {
			data.BuyerNickname = utils.MaskNickname(userDetail.Nickname)
			data.BuyerAvatar = userDetail.Avatar
		}
		list = append(list, data)
	}

	return &define.GetWebResaleOrderSaleListResp{
		List:  list,
		Total: count,
	}, nil
}

// GetWebResaleOrderBuyDetail 查询我购买的转卖订单详情
func (s *Service) GetWebResaleOrderBuyDetail(req *define.GetWebResaleOrderBuyDetailReq) (*define.GetWebResaleOrderBuyDetailResp, error) {
	to := repo.GetQuery().ResaleOrder
	resaleOrder, err := repo.NewResaleOrderRepo(to.WithContext(s.ctx)).SelectOne(search.NewWrapper().
		Where(to.ID.Eq(req.ID), to.BuyerID.Eq(s.GetUserId())))
	if err != nil {
		return nil, errors.Wrap(err, "查询订单详情失败")
	}
	if resaleOrder == nil {
		return nil, errors.New("订单不存在")
	}
	userIDs := []string{resaleOrder.BuyerID, resaleOrder.SellerID}
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单详情失败,获取用户信息失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	resp := &define.GetWebResaleOrderBuyDetailResp{
		ID:          resaleOrder.ID,
		Status:      resaleOrder.Status,
		BuyerId:     resaleOrder.BuyerID,
		SellerId:    resaleOrder.SellerID,
		ItemName:    resaleOrder.ItemName,
		SalePrice:   resaleOrder.SalePrice,
		Quantity:    resaleOrder.Quantity,
		TotalAmount: resaleOrder.TotalAmount,
		TotalFee:    resaleOrder.TotalFee,
		CreatedAt:   resaleOrder.CreatedAt,
		PaymentAt:   resaleOrder.PaymentAt,
		FinishedAt:  resaleOrder.FinishedAt,
	}
	if userDetail, ok := userDetailMap[resaleOrder.BuyerID]; ok {
		resp.BuyerNickname = utils.MaskNickname(userDetail.Nickname)
		resp.BuyerRealName = utils.MaskRealName(userDetail.RealName)
	}
	if userDetail, ok := userDetailMap[resaleOrder.SellerID]; ok {
		resp.SellerNickname = utils.MaskNickname(userDetail.Nickname)
		resp.SellerRealName = utils.MaskRealName(userDetail.RealName)
	}
	resp.ItemInfo = &define.GetWebResaleOrderBuyDetailData{
		ItemId:      resaleOrder.ItemID,
		ItemName:    resaleOrder.ItemName,
		ItemIconUrl: resaleOrder.ItemIconURL,
		ItemSpecs:   resaleOrder.ItemSpecs,
		SalePrice:   resaleOrder.SalePrice,
		TotalAmount: resaleOrder.TotalAmount,
		PayAmount:   resaleOrder.PayAmount,
		Quantity:    resaleOrder.Quantity,
		SoldOut:     logic.GetItemSoldOut(s.ctx, resaleOrder.ItemID),
	}
	return resp, nil
}

// GetWebResaleOrderSaleDetail 查询我出售的转卖订单详情
func (s *Service) GetWebResaleOrderSaleDetail(req *define.GetWebResaleOrderSaleDetailReq) (*define.GetWebResaleOrderSaleDetailResp, error) {
	to := repo.GetQuery().ResaleOrder
	resaleOrder, err := repo.NewResaleOrderRepo(to.WithContext(s.ctx)).SelectOne(search.NewWrapper().
		Where(to.ID.Eq(req.ID), to.SellerID.Eq(s.GetUserId())))
	if err != nil {
		return nil, errors.Wrap(err, "查询订单详情失败")
	}
	if resaleOrder == nil {
		return nil, errors.New("订单不存在")
	}
	userIDs := []string{resaleOrder.BuyerID, resaleOrder.SellerID}
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单详情失败,获取用户信息失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	resp := &define.GetWebResaleOrderSaleDetailResp{
		ID:                 resaleOrder.ID,
		Status:             resaleOrder.Status,
		BuyerId:            resaleOrder.BuyerID,
		SellerId:           resaleOrder.SellerID,
		ItemName:           resaleOrder.ItemName,
		SalePrice:          resaleOrder.SalePrice,
		Quantity:           resaleOrder.Quantity,
		TotalAmount:        resaleOrder.TotalAmount,
		TotalFee:           resaleOrder.TotalFee,
		SellerIncomeAmount: resaleOrder.PayAmount - resaleOrder.TotalFee,
		CreatedAt:          resaleOrder.CreatedAt,
		PaymentAt:          resaleOrder.PaymentAt,
		FinishedAt:         resaleOrder.FinishedAt,
	}
	if userDetail, ok := userDetailMap[resaleOrder.BuyerID]; ok {
		resp.BuyerNickname = utils.MaskNickname(userDetail.Nickname)
		resp.BuyerRealName = utils.MaskRealName(userDetail.RealName)
	}
	if userDetail, ok := userDetailMap[resaleOrder.SellerID]; ok {
		resp.SellerNickname = utils.MaskNickname(userDetail.Nickname)
		resp.SellerRealName = utils.MaskRealName(userDetail.RealName)
	}
	resp.ItemInfo = &define.GetWebResaleOrderSaleDetailData{
		ItemId:             resaleOrder.ItemID,
		ItemName:           resaleOrder.ItemName,
		ItemIconUrl:        resaleOrder.ItemIconURL,
		ItemSpecs:          resaleOrder.ItemSpecs,
		SalePrice:          resaleOrder.SalePrice,
		TotalAmount:        resaleOrder.TotalAmount,
		SellerIncomeAmount: resaleOrder.PayAmount - resaleOrder.TotalFee,
		Quantity:           resaleOrder.Quantity,
		SoldOut:            logic.GetItemSoldOut(s.ctx, resaleOrder.ItemID),
	}
	return resp, nil
}

// GetWebResaleOrderRecentList 查询最近成交记录
func (s *Service) GetWebResaleOrderRecentList(req *define.GetWebResaleOrderRecentListReq) (*define.GetWebResaleOrderRecentListResp, error) {
	if req.GetPage()*req.GetPageSize() > constant.RecentListMax {
		return nil, define.MS200017Err.SetMsg(fmt.Sprintf(define.MS200014Err.Msg, constant.RecentListMax))
	}
	schema := repo.GetQuery().ResaleOrder
	wrapper := search.NewWrapper().Where(schema.Status.Eq(enums.ResaleOrderStatusCompleted.Val()), schema.ItemID.Eq(req.ItemId)).OrderBy(schema.ID.Desc())
	resaleOrders, count, err := repo.NewResaleOrderRepo(schema.WithContext(s.ctx)).SelectPage(wrapper, req)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询最近成交记录失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	if len(resaleOrders) == 0 {
		return &define.GetWebResaleOrderRecentListResp{
			List:  []*define.GetWebResaleOrderRecentListData{},
			Total: count,
		}, nil
	}

	userIDs := make([]string, 0, len(resaleOrders))
	for _, resaleOrder := range resaleOrders {
		userIDs = append(userIDs, resaleOrder.BuyerID)
	}
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询最近成交记录失败,获取用户信息失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}

	list := make([]*define.GetWebResaleOrderRecentListData, 0, len(resaleOrders))
	for _, resaleOrder := range resaleOrders {
		data := &define.GetWebResaleOrderRecentListData{
			ItemId:    resaleOrder.ItemID,
			ItemName:  resaleOrder.ItemName,
			ItemSpecs: resaleOrder.ItemSpecs,
			SalePrice: resaleOrder.SalePrice,
			Quantity:  resaleOrder.Quantity,
			CreatedAt: resaleOrder.CreatedAt,
		}
		if userDetail, ok := userDetailMap[resaleOrder.BuyerID]; ok {
			data.BuyerNickname = utils.MaskNickname(userDetail.Nickname)
			data.BuyerAvatar = userDetail.Avatar
		}
		list = append(list, data)
	}

	return &define.GetWebResaleOrderRecentListResp{
		List:  list,
		Total: count,
	}, nil
}

// ReleaseUserItems 释放锁单失败订单的背包物品
func (s *Service) ReleaseUserItems(req *define.ReleaseUserItemsReq) (*define.ReleaseUserItemsResp, error) {
	ro := repo.GetQuery().ResaleOrder
	var searchWrapper *search.Wrapper
	if req.ID != 0 {
		searchWrapper = search.NewWrapper().Where(ro.ID.Eq(req.ID), ro.Status.Eq(enums.ResaleOrderStatusUnPaid.Val()))
	} else {
		searchWrapper = search.NewWrapper().
			Where(ro.Status.Eq(enums.ResaleOrderStatusUnPaid.Val()),
				ro.CreatedAt.Lte(time.Now().Add(-time.Minute*1)), ro.CreatedAt.Gte(time.Now().Add(-time.Minute*30)))
	}
	resaleOrders, err := repo.NewResaleOrderRepo(ro.WithContext(s.ctx).Preload(ro.ResaleOrderItem)).SelectList(searchWrapper)
	if err != nil {
		return nil, err
	}
	spanContext := s.NewContextWithSpanContext(s.ctx)
	err = logic.ReleaseUserItems(spanContext, resaleOrders)
	if err != nil {
		log.Ctx(spanContext).Errorf("释放锁单失败订单的背包物品失败, err:%+v", err)
		return nil, err
	}
	return &define.ReleaseUserItemsResp{}, err
}

// LockedHandler 物品异常锁定订单处理
func (s *Service) LockedHandler(req *define.LockedHandlerReq) (*define.LockedHandlerResp, error) {
	ro := repo.GetQuery().ResaleOrder
	var searchWrapper *search.Wrapper
	if req.ID != 0 {
		searchWrapper = search.NewWrapper().Where(ro.ID.Eq(req.ID), ro.Status.Eq(enums.ResaleOrderStatusItemLocked.Val()))
	} else {
		searchWrapper = search.NewWrapper().
			Where(ro.Status.Eq(enums.ResaleOrderStatusItemLocked.Val()),
				ro.CreatedAt.Lte(time.Now().Add(-time.Minute*1)), ro.CreatedAt.Gte(time.Now().Add(-time.Minute*30)))
	}
	resaleOrders, err := repo.NewResaleOrderRepo(ro.WithContext(s.ctx).Preload(ro.ResaleOrderItem)).SelectList(searchWrapper)
	if err != nil {
		return nil, err
	}
	spanContext := s.NewContextWithSpanContext(s.ctx)
	err = logic.LockedHandler(spanContext, resaleOrders)
	if err != nil {
		log.Ctx(spanContext).Errorf("物品异常锁定订单处理失败，err:%+v", err)
		return nil, err
	}
	return &define.LockedHandlerResp{}, err
}

// ResaleItemTransferHandler 物品发放异常订单处理
func (s *Service) ResaleItemTransferHandler(req *define.ResaleItemTransferHandlerReq) (*define.ResaleItemTransferHandlerResp, error) {
	ro := repo.GetQuery().ResaleOrder
	var searchWrapper *search.Wrapper
	if req.ID != 0 {
		searchWrapper = search.NewWrapper().Where(ro.ID.Eq(req.ID), ro.Status.Eq(enums.ResaleOrderStatusTransfer.Val()))
	} else {
		searchWrapper = search.NewWrapper().
			Where(ro.Status.Eq(enums.ResaleOrderStatusTransfer.Val()),
				ro.CreatedAt.Lte(time.Now().Add(-time.Minute*1)), ro.CreatedAt.Gte(time.Now().Add(-time.Minute*30)))
	}
	resaleOrders, err := repo.NewResaleOrderRepo(ro.WithContext(s.ctx).Preload(ro.ResaleOrderItem)).SelectList(searchWrapper)
	if err != nil {
		return nil, err
	}
	spanContext := s.NewContextWithSpanContext(s.ctx)
	err = logic.ResaleItemTransferHandler(spanContext, resaleOrders)
	if err != nil {
		log.Ctx(spanContext).Errorf("物品发放异常订单处理失败，err:%+v", err)
		return nil, err
	}
	return &define.ResaleItemTransferHandlerResp{}, err
}
