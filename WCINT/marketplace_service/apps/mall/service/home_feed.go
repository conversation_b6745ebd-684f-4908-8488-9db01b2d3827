package service

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"strconv"
)

// GetHomeFeedList 获取首页商品列表
func (s *Service) GetHomeFeedList(req *define.WebGetHomeFeedListReq) (*define.WebGetHomeFeedListResp, error) {
	resp := &define.WebGetHomeFeedListResp{
		List:    make([]*define.WebHomeFeedListData, 0),
		HasMore: false,
	}
	offset, pageSize := search.Paginate(req.GetPageSize(), req.GetPage())
	var homeFeeds []*model.HomeFeed
	var err error
	hf := repo.GetQuery().HomeFeed
	if req.IPID == "" {
		cacheKey := constant.GetHomeFeedListKey()
		opt := &redis.ZRangeBy{
			Min:    "-inf",
			Max:    "+inf",
			Offset: int64(offset),
			Count:  int64(pageSize),
		}
		homeFeedStrIds, err := global.REDIS.ZRevRangeByScore(s.ctx, cacheKey, opt).Result()
		if err != nil {
			return nil, errors.Wrap(err, "获取首页列表失败")
		}
		homeFeedIds := make([]int64, 0, len(homeFeedStrIds))
		for _, idStr := range homeFeedStrIds {
			if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
				homeFeedIds = append(homeFeedIds, id)
			}
		}
		if len(homeFeedIds) == 0 {
			return resp, nil
		}
		qw := search.NewWrapper().Where(hf.ID.In(homeFeedIds...)).OrderBy(hf.Priority.Desc(), hf.LastTradeTime.Desc())
		homeFeeds, err = repo.NewHomeFeedRepo(hf.WithContext(s.ctx).Preload(hf.HomeFeedStats)).SelectList(qw)
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取首页列表失败  err: %v, req: %+v", err, req)
			return nil, err
		}
	} else {
		wrapper := search.NewWrapper().Where(hf.IPID.Eq(req.IPID), hf.Status.Eq(1))
		// 综合
		if req.OrderBy == 0 || req.OrderBy == enums.HomeFeedOrderByTypeDefault.Val() {
			wrapper = wrapper.OrderBy(hf.Priority.Desc(), hf.LastTradeTime.Desc())
		}
		// 销量
		if req.OrderBy == enums.HomeFeedOrderByTypeSaleVolume.Val() {
			wrapper = wrapper.OrderBy(hf.SalesVolume.Desc(), hf.Priority.Desc(), hf.LastTradeTime.Desc())
		}
		// 价格
		if req.OrderBy == enums.HomeFeedOrderByTypePrice.Val() {
			if req.SortOrder == constant.SortOrderDesc {
				wrapper = wrapper.OrderBy(hf.SalePrice.Desc(), hf.Priority.Desc(), hf.LastTradeTime.Desc())
			}
			wrapper = wrapper.OrderBy(hf.SalePrice.Asc(), hf.Priority.Desc(), hf.LastTradeTime.Desc())
		}
		homeFeeds, err = repo.NewHomeFeedRepo(hf.WithContext(s.ctx).Preload(hf.HomeFeedStats).Offset(offset).Limit(pageSize)).SelectList(wrapper)
		if err != nil {
			return nil, errors.Wrap(err, "获取商品列表失败")
		}
		if len(homeFeeds) == 0 {
			return resp, nil
		}
	}
	// 获取商品ID
	itemIds := make([]string, 0)
	for _, homeFeed := range homeFeeds {
		if homeFeed.ItemMallStatus == 1 {
			itemIds = append(itemIds, homeFeed.ItemID)
		}
	}
	// 获取直购商品价格
	mi := repo.GetQuery().MallItem
	miWrapper := search.NewWrapper().Select(mi.ItemID, mi.SalePrice, mi.Discount).Where(mi.ItemID.In(itemIds...), mi.Status.Eq(enums.MallItemStatusUp.Val()))
	mallItems, err := repo.NewMallItemRepo(mi.WithContext(s.ctx)).SelectList(miWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取商品列表失败  err: %v, req: %+v", err, req)
		return nil, err
	}
	mallItemMap := make(map[string]*model.MallItem, len(mallItems))
	for _, mallItem := range mallItems {
		mallItemMap[mallItem.ItemID] = mallItem
	}
	homeFeedListData := make([]*define.WebHomeFeedListData, 0, len(homeFeeds))
	for _, homeFeed := range homeFeeds {
		hfl := &define.WebHomeFeedListData{
			ID:             homeFeed.ID,
			Status:         homeFeed.Status,
			ResaleStatus:   homeFeed.ItemResaleStatus,
			ItemMallStatus: homeFeed.ItemMallStatus,
			ItemID:         homeFeed.ItemID,
			ItemName:       homeFeed.ItemName,
			ItemSpecs:      homeFeed.ItemSpecs,
			ItemIconURL:    homeFeed.ItemIconURL,
			Priority:       homeFeed.Priority,
			SalePrice:      homeFeed.SalePrice,
			OriginalPrice:  homeFeed.SalePrice,
			ResaleCount:    homeFeed.ResaleCount,
			SalesVolume:    homeFeed.SalesVolume,
			WishCount:      homeFeed.HomeFeedStats.WishCount,
		}
		if homeFeed.ItemMallStatus == 1 {
			mallItem := mallItemMap[homeFeed.ItemID]
			discountPrice := logic.CalculateDiscountedPrice(mallItem.SalePrice, mallItem.Discount)
			if discountPrice == homeFeed.SalePrice {
				hfl.OriginalPrice = mallItem.SalePrice
			}
		}
		homeFeedListData = append(homeFeedListData, hfl)
	}
	resp.List = homeFeedListData
	resp.HasMore = len(resp.List) >= req.GetPageSize()
	return resp, nil
}

// GetHomeFeedDetail 获取首页商品详情
func (s *Service) GetHomeFeedDetail(req *define.WebGetHomeFeedDetailReq) (*define.WebGetHomeFeedDetailResp, error) {
	hf := repo.GetQuery().HomeFeed
	qw := search.NewWrapper()
	if req.ID > 0 {
		qw.Where(hf.ID.Eq(req.ID))
	} else {
		qw.Where(hf.ItemID.Eq(req.ItemID))
	}
	homeFeed, err := repo.NewHomeFeedRepo(hf.WithContext(s.ctx).Preload(hf.HomeFeedStats)).SelectOne(qw)
	if err != nil {
		return nil, errors.Wrap(err, "查询首页商品详情失败")
	}
	resp := &define.WebGetHomeFeedDetailResp{
		ID:             homeFeed.ID,
		Status:         homeFeed.Status,
		ResaleStatus:   homeFeed.ItemResaleStatus,
		ItemMallStatus: homeFeed.ItemMallStatus,
		ItemID:         homeFeed.ItemID,
		ItemName:       homeFeed.ItemName,
		ItemSpecs:      homeFeed.ItemSpecs,
		ItemIconURL:    homeFeed.ItemIconURL,
		ResaleCount:    homeFeed.ResaleCount,
		SalesVolume:    homeFeed.SalesVolume,
		SalePrice:      homeFeed.SalePrice,
		WishCount:      homeFeed.HomeFeedStats.WishCount,
		ViewCount:      homeFeed.HomeFeedStats.ViewCount,
		IPID:           homeFeed.IPID,
	}
	if homeFeed.ItemResaleStatus == 1 && homeFeed.ResaleCount > 0 {
		rl := repo.GetQuery().ResaleListings
		warp := search.NewWrapper().Select(rl.ItemID, rl.SalePrice).
			Where(rl.ItemID.Eq(homeFeed.ItemID), rl.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())).
			OrderBy(rl.SalePrice)
		listings, err := repo.NewResaleListingsRepo(rl.WithContext(s.ctx).Limit(1)).SelectOne(warp)
		if err != nil && err != gorm.ErrRecordNotFound {
			log.Ctx(s.ctx).Errorf("获取转卖挂单最小挂单价失败, itemId:%+v,err:%+v", homeFeed.ItemID, err)
			return nil, errors.Wrap(err, "获取转卖挂单最小挂单价失败")
		}
		if listings != nil {
			resp.SalePrice = listings.SalePrice
		}
	}
	itemInfo, _ := logic.GetItemInfo(s.ctx, homeFeed.ItemID)
	resp.Detail = itemInfo.DetailH5
	resp.ImageInfos = itemInfo.ImageInfos
	if len(itemInfo.TrademarkInfo) > 0 {
		resp.TrademarkID = itemInfo.TrademarkInfo[len(itemInfo.TrademarkInfo)-1].ID
	}
	trademarkInfoMap, _ := logic.GetTrademarksInfoMap(s.ctx, resp.TrademarkID)
	if trademarkInfo, ok := trademarkInfoMap[resp.TrademarkID]; ok {
		resp.TrademarkName = trademarkInfo.Name
	}
	ipInfoMap, _ := logic.GetIpInfoMap(s.ctx, homeFeed.IPID)
	if ipInfo, ok := ipInfoMap[homeFeed.IPID]; ok {
		resp.IPName = ipInfo.Name
		resp.IPIconURL = ipInfo.Icon
	}
	if homeFeed.ItemResaleStatus == 1 {
		resaleInstructionConfig, err := logic.GetResaleInstructionConfig(s.ctx)
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取转卖说明配置失败  err: %v, req: %+v", err, req)
			return nil, err
		}
		resp.ResaleInstruction = &define.ResaleInstruction{
			Content:   resaleInstructionConfig.Content,
			BannerUrl: resaleInstructionConfig.BannerUrl,
		}
	}
	if s.GetUserId() != "" {
		userWishlist, _ := logic.IsWished(s.ctx, s.GetUserId(), homeFeed.ItemID)
		if userWishlist != nil {
			resp.UserWishlistStatus = 1
		}
	}
	return resp, nil
}

// AddViewCount 增加浏览次数
func (s *Service) AddViewCount(req *define.WebAddHomeFeedViewCountReq) (*define.WebAddHomeFeedViewCountResp, error) {
	redisKey := constant.GetItemViewCountKey(req.ItemID)
	_, err := global.REDIS.Incr(s.ctx, redisKey).Result()
	if err != nil {
		log.Ctx(s.ctx).Errorf("Redis 增加浏览次数失败, itemId=%s, err=%v", req.ItemID, err)
	}
	return &define.WebAddHomeFeedViewCountResp{}, nil
}

// SyncViewCountToDB 同步浏览次数至数据库
func (s *Service) SyncViewCountToDB(req *define.OpenSyncViewCountToDBReq) (*define.OpenSyncViewCountToDBResp, error) {
	// 获取所有上架的商品
	hf := repo.GetQuery().HomeFeed
	wrapper := search.NewWrapper().Select(hf.ItemID).Where(hf.Status.Eq(enums.HomeFeedStatusUp.Val()))
	homeFeedList, err := repo.NewHomeFeedRepo(hf.WithContext(s.ctx)).SelectList(wrapper)
	if err != nil {
		return nil, err
	}
	for _, homeFeed := range homeFeedList {
		redisKey := constant.GetItemViewCountKey(homeFeed.ItemID)
		count, _ := global.REDIS.Get(s.ctx, redisKey).Int64()
		if count > 0 {
			// 使用 Lua 脚本保证 INCR 和 GET 的原子性
			script := `
            local current = redis.call("GET", KEYS[1])
            if tonumber(current) > 0 then
                redis.call("DEL", KEYS[1])
                return current
            end
            return nil
        `
			// 尝试获取并删除计数
			scriptResult, err := global.REDIS.Eval(s.ctx, script, []string{redisKey}).Int()
			if err != nil && err != redis.Nil {
				log.Ctx(s.ctx).Errorf("Redis 获取浏览次数失败, itemId=%s, err=%v", homeFeed.ItemID, err)
				continue
			}
			if scriptResult != 0 {
				// 转换为整数
				hfs := repo.GetQuery().HomeFeedStats
				_, err = hfs.WithContext(s.ctx).Where(hfs.ItemID.Eq(homeFeed.ItemID)).
					Update(hfs.ViewCount, hfs.ViewCount.Add(int32(scriptResult)))
				if err != nil {
					log.Ctx(s.ctx).Errorf("更新浏览次数失败, itemId=%s, err=%v", homeFeed.ItemID, err)
					return &define.OpenSyncViewCountToDBResp{}, nil
				}
			}
		}
	}
	return &define.OpenSyncViewCountToDBResp{}, nil
}

// RefreshHomeFeed 刷新首页数据
func (s *Service) RefreshHomeFeed(req *define.RefreshHomeFeedReq) (*define.RefreshHomeFeedResp, error) {
	if req.ItemIds == nil || len(req.ItemIds) == 0 {
		return &define.RefreshHomeFeedResp{}, nil
	}
	for _, itemId := range req.ItemIds {
		if err := logic.UpdateHomeFeed(s.ctx, itemId); err != nil {
			return nil, errors.Wrap(err, "刷新首页商品基础信息失败")
		}
		// 查询直购最后交易时间和直购销量
		salesVolume, lastTradeTime, err := logic.GetTradeVolumeAndTime(s.ctx, itemId)
		if err != nil {
			return nil, err
		}
		// 查询转卖挂单数量
		rli := repo.GetQuery().ResaleListingsItem
		resaleCount, err := repo.NewResaleListingsItemRepo(rli.WithContext(s.ctx)).
			Count(search.NewWrapper().Where(rli.ItemID.Eq(itemId), rli.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())))
		if err != nil {
			return nil, errors.Wrap(err, "查询转卖商品挂单数量失败")
		}

		// 查询想要数量
		uw := repo.GetQuery().UserWishlist
		wishCount, err := repo.NewUserWishlistRepo(uw.WithContext(s.ctx)).
			Count(search.NewWrapper().Where(uw.ItemID.Eq(itemId), uw.Status.Eq(enums.UserWishlistStatusWant.Val())))
		if err != nil {
			return nil, errors.Wrap(err, "想要数量")
		}
		// 使用事务统一更新销量、交易时间、挂单数、想购数量
		if err := logic.UpdateHomeFeedAndStats(s.ctx, itemId, salesVolume, lastTradeTime, int32(resaleCount), int32(wishCount)); err != nil {
			return nil, err
		}
		sContext := s.NewContextWithSpanContext(s.ctx)
		go func(ctx context.Context, itemId string) {
			homeFeed, err := repo.GetQuery().HomeFeed.WithContext(s.ctx).
				Where(repo.GetQuery().HomeFeed.ItemID.Eq(itemId)).
				First()
			if err != nil {
				log.Ctx(s.ctx).Errorf("异步更新缓存失败, itemId=%s, err=%v", itemId, err)
				return
			}
			if err := logic.AddHomeFeedCacheOnStatusChange(s.ctx, homeFeed); err != nil {
				log.Ctx(s.ctx).Errorf("更新首页缓存失败, itemId=%s, err=%v", itemId, err)
			}
		}(sContext, itemId)
	}
	return &define.RefreshHomeFeedResp{}, nil
}
