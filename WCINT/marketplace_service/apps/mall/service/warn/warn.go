package warn

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"marketplace_service/global"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/mor"
)

type MsgMentionOption struct {
	MentionedList       []string `json:"mentioned_list"`        // userid的列表，提醒群中的指定成员(@某个成员)，@all表示提醒所有人
	MentionedMobileList []string `json:"mentioned_mobile_list"` // 手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人
}

// SendDefaultWarnMsg 使用默认的Id进行告警告警
func SendDefaultWarnMsg(ctx context.Context, title, msg string) {
	env := "测试"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		env = "正式"
	}
	var message = fmt.Sprintf("【%v】 %v \n%v", env, title, msg)
	id := global.GlobalConfig.WarnId
	req := &mor.WarnReq{
		Id:  id,
		Msg: message,
	}
	res, err := mor.Warn(ctx, req)
	if err != nil {
		if err == context.Canceled {
			SendWarnMsg(context.Background(), id, msg)
		}
		log.Ctx(ctx).Errorf("[warn.SendDefaultWarnMsg] 调用告警接口失败,id: %v, msg:%v, err:%v", req.Id, req.Msg, err)
	} else {
		log.Ctx(ctx).Infof("[warn.SendDefaultWarnMsg] 调用告警接口成功,id: %v, msg:%v, res:%v", req.Id, req.Msg, utils.Obj2JsonStr(res))
	}
}

func SendDefaultWarnMsgWithMentionOption(ctx context.Context, title, msg string, option *MsgMentionOption) {
	env := "测试"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		env = "正式"
	}
	var message = fmt.Sprintf("【%v】 %v \n%v", env, title, msg)
	id := global.GlobalConfig.WarnId
	req := &mor.WarnReq{
		Id:  id,
		Msg: message,
	}
	if option != nil {
		if len(option.MentionedList) > 0 {
			req.MentionedList = option.MentionedList
		}
		if len(option.MentionedMobileList) > 0 {
			req.MentionedMobileList = option.MentionedMobileList
		}
	}
	res, err := mor.Warn(ctx, req)
	if err != nil {
		if err == context.Canceled {
			SendWarnMsg(context.Background(), id, msg)
		}
		log.Ctx(ctx).Errorf("[warn.SendDefaultWarnMsg] 调用告警接口失败,id: %v, msg:%v, err:%v", req.Id, req.Msg, err)
	} else {
		log.Ctx(ctx).Infof("[warn.SendDefaultWarnMsg] 调用告警接口成功,id: %v, msg:%v, res:%v", req.Id, req.Msg, utils.Obj2JsonStr(res))
	}
}

// SendWarnMsg 告警
func SendWarnMsg(ctx context.Context, id, msg string) {
	go func() {
		req := &mor.WarnReq{
			Id:  id,
			Msg: msg,
		}
		res, err := mor.Warn(ctx, req)
		if err != nil {
			log.Ctx(ctx).Errorf("[warn.SendWarnMsg] 调用告警接口失败,id: %v, msg:%v, err:%v", req.Id, req.Msg, err)
		} else {
			log.Ctx(ctx).Infof("[warn.SendWarnMsg] 调用告警接口成功,id: %v, msg:%v, res:%v", req.Id, req.Msg, utils.Obj2JsonStr(res))
		}
	}()
}
