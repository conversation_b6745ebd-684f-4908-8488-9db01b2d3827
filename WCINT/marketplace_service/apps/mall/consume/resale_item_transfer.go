package consume

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/global"
	"marketplace_service/pkg/middleware"
	"marketplace_service/pkg/utils"
)

type ResaleItemTransferConsumer struct {
	middleware.BaseConsumer
}

func NewResaleItemTransferConsumer() *ResaleItemTransferConsumer {
	return &ResaleItemTransferConsumer{
		BaseConsumer: middleware.NewBaseConsumer(constant.ResaleItemTransfer, constant.CommonGroup),
	}
}

func (o *ResaleItemTransferConsumer) GetTopic() string {
	return constant.ResaleItemTransfer
}

func (o *ResaleItemTransferConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *ResaleItemTransferConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "ResaleItemTransferConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[resale_item_transfer]ResaleItemTransfer kafka data:%s", utils.Obj2JsonStr(event.Message()))

		// 解析消息
		data := &define.ResaleItemTransfer{}
		err := utils.JsonStrToStruct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return global.CommonErr.Err(err)
		}
		log.Ctx(ctx).Infof("[resale_item_transfer]ResaleItemTransfer data:%s", utils.Obj2JsonStr(data))
		err = logic.ResaleOrderUserItemTransfer(ctx, data.ID)
		if err != nil {
			log.Ctx(ctx).Errorf("转卖物品转移失败 orderId:%+v, err:%v", data.ID, err)
		}
		err = logic.ResaleTradeCompleted(ctx, data.ID)
		if err != nil {
			log.Ctx(ctx).Errorf("转卖订单完成失败 orderId:%+v, err:%v", data.ID, err)
		}
		return nil
	}
	return middleware.SafeHandler(handler)
}
