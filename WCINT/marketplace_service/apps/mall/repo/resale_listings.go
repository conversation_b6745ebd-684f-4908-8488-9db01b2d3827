package repo

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/pkg/utils"
)

func (r *ResaleListingsRepository) UpdateSoldQuantityWithTx(ctx context.Context, listingsID int64, qty int32, totalIncome, totalFee int64) error {
	db := r.do.UnderlyingDB()

	return db.Transaction(func(tx *gorm.DB) error {
		// 1. 锁定记录（不需要Select("id")，直接查询完整记录）
		var listing model.ResaleListings
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("id = ? AND is_del = 0", listingsID).
			First(&listing).
			Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Ctx(ctx).Errorf("转卖挂单不存在或已删除 listingsID:%d", listingsID)
				return errors.New("转卖挂单不存在或已删除")
			}
			return err
		}

		// 2. 前置检查（可选，增加可读性）
		if listing.Status != enums.ResaleListingsStatusOnSale.Val() {
			log.Ctx(ctx).Errorf("转卖挂单不在可销售状态 listingsID:%d, listing:%+v", listingsID, utils.Obj2JsonStr(listing))
			return errors.New("挂单不在可销售状态")
		}
		if listing.SoldQuantity+qty > listing.ListingQuantity {
			log.Ctx(ctx).Errorf("库存不足 listingsID:%d, listing:%+v", listingsID, utils.Obj2JsonStr(listing))
			return errors.New("库存不足")
		}

		// 3. 执行更新
		result := tx.Exec(`
            UPDATE resale_listings 
            SET 
                sold_quantity = sold_quantity + ?,
                total_income = total_income + ?,
                total_fee = total_fee + ?,
                status = CASE 
                    WHEN sold_quantity >= listing_quantity THEN ?
                    ELSE status 
                END,
                latest_trade_at = NOW(3)
            WHERE 
                id = ? 
                AND status = ?
                AND sold_quantity + ? <= listing_quantity
                AND is_del = 0`,
			qty, totalIncome, totalFee, enums.ResaleListingsStatusSoldOut.Val(),
			listingsID, enums.ResaleListingsStatusOnSale.Val(), qty)

		if result.Error != nil {
			return result.Error
		}

		if result.RowsAffected == 0 {
			// 理论上不会走到这里，因为前面已经检查过
			log.Ctx(ctx).Errorf("更新失败：并发冲突 listing:%+v", utils.Obj2JsonStr(listing))
			return errors.New("更新失败：并发冲突")
		}
		return nil
	})
}
