package repo

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/dal/query"
	"marketplace_service/pkg/pagination"
	"marketplace_service/pkg/search"
)

type ResaleListingsRepository struct {
	do query.IResaleListingsDo
}

func NewResaleListingsRepo(do query.IResaleListingsDo) *ResaleListingsRepository {
	return &ResaleListingsRepository{
		do: do,
	}
}

func (r *ResaleListingsRepository) SelectOne(wrapper *search.Wrapper) (*model.ResaleListings, error) {
	records, err := r.do.Scopes(wrapper.Build()).Find()
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	if len(records) > 1 {
		return nil, errors.New("more than one item found")
	}
	return records[0], nil
}

func (r *ResaleListingsRepository) SelectList(wrapper *search.Wrapper) ([]*model.ResaleListings, error) {
	records, err := r.do.Scopes(wrapper.Build()).Find()
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (r *ResaleListingsRepository) SelectPage(wrapper *search.Wrapper, req pagination.IPagination) ([]*model.ResaleListings, int64, error) {
	records, count, err := r.do.Scopes(wrapper.Build()).
		FindByPage(search.Paginate(req.GetPageSize(), req.GetPage()))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *ResaleListingsRepository) QuickSelectPage(req pagination.IPagination) ([]*model.ResaleListings, int64, error) {
	records, count, err := r.do.Scopes(search.MakeCondition(req)).
		FindByPage(search.Paginate(req.GetPageSize(), req.GetPage()))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *ResaleListingsRepository) Count(wrapper *search.Wrapper) (int64, error) {
	count, err := r.do.Scopes(wrapper.Build()).Count()
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *ResaleListingsRepository) Save(model *model.ResaleListings) error {
	err := r.do.Create(model)
	if err != nil {
		return err
	}
	return nil
}

func (r *ResaleListingsRepository) BatchSave(models []*model.ResaleListings, batchSize int) error {
	err := r.do.CreateInBatches(models, batchSize)
	if err != nil {
		return err
	}
	return nil
}

func (r *ResaleListingsRepository) UpdateById(model *model.ResaleListings) error {
	result, err := r.do.Updates(model)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *ResaleListingsRepository) Update(ms *model.ResaleListings, wrapper *search.Wrapper) error {
	if wrapper != nil {
		r.do = r.do.Scopes(
			wrapper.Build(),
		)
	}
	result, err := r.do.Updates(ms)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *ResaleListingsRepository) UpdateField(params interface{}, wrapper *search.Wrapper) error {
	if wrapper != nil {
		r.do = r.do.Scopes(
			wrapper.Build(),
		)
	}
	result, err := r.do.Updates(params)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *ResaleListingsRepository) RemoveByIds(ms ...*model.ResaleListings) error {
	result, err := r.do.Delete(ms...)
	if err != nil {
		return err
	} else if result.RowsAffected != int64(len(ms)) {
		return UpdateFail
	}
	return nil
}
