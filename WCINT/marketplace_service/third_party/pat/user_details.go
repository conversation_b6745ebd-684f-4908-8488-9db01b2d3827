package pat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
)

type UserDetailsResp struct {
	Code int32         `json:"code" form:"code"`
	Desc string        `json:"desc" form:"desc"`
	Data []*UserDetail `json:"data" form:"data"`
}

type UserDetail struct {
	Id          string `json:"_id,omitempty" form:"_id"`
	MobilePhone string `json:"mobile_phone,omitempty" form:"mobile_phone"`
	Status      int32  `json:"status,omitempty" form:"status"`
	Nickname    string `json:"nickname,omitempty" form:"nickname"`
	RealName    string `json:"real_name" form:"real_name"`
	Avatar      string `json:"avatar,omitempty" form:"avatar"`
	Type        int32  `json:"type,omitempty" form:"type"`
}

// GetUserDetails 获取用户信息
func GetUserDetails(ctx context.Context, userIds []string) ([]*UserDetail, error) {
	rsp := &UserDetailsResp{}

	req, err := request.Pat()
	if err != nil {
		return nil, err
	}

	opts := []utilRequest.Option{
		utilRequest.WithParams(map[string]interface{}{
			"user_ids": userIds,
		}),
		utilRequest.WithMethodPost(),
	}
	err = req.Call(
		ctx,
		"open/user/v1/user_details/",
		&rsp,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	return rsp.Data, err
}

// GetUserDetailMap 获取用户信息
func GetUserDetailMap(ctx context.Context, userIds []string) (map[string]*UserDetail, error) {
	rsp := &UserDetailsResp{}

	req, err := request.Pat()
	if err != nil {
		return nil, err
	}

	opts := []utilRequest.Option{
		utilRequest.WithParams(map[string]interface{}{
			"user_ids": userIds,
		}),
		utilRequest.WithMethodPost(),
	}
	err = req.Call(
		ctx,
		"open/user/v1/user_details/",
		&rsp,
		opts...,
	)
	if err != nil {
		return nil, err
	}
	res := make(map[string]*UserDetail)
	for _, item := range rsp.Data {
		res[item.Id] = item
	}

	return res, err
}
