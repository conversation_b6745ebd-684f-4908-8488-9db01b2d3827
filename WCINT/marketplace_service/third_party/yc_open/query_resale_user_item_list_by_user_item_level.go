package yc_open

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"fmt"
	"github.com/pkg/errors"
	"marketplace_service/global"
	"marketplace_service/pkg/pagination"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/yc_open/define"
	"time"
)

type (
	QueryResaleUserItemListByUserItemLevelReq struct {
		pagination.Pagination
		ItemID      string   `json:"item_id" validate:"required"`
		OpenUserID  string   `json:"open_user_id" validate:"required"`
		UserItemIDs []string `json:"user_item_ids"`
		StatusList  []any    `json:"status_list"`
		CreatedAtLt string   `json:"created_at_lt"` // 创建时间小于
	}
	QueryResaleUserItemListByUserItemLevelData struct {
		List    []*define.UserItemData `json:"list"`
		HasMore bool                   `json:"has_more"`
	}
	QueryResaleUserItemListByUserItemLevelResp struct {
		Code int32                                      `json:"code" form:"code"`
		Desc string                                     `json:"desc" form:"desc"`
		Data QueryResaleUserItemListByUserItemLevelData `json:"data" form:"data"`
	}
)

// QueryResaleUserItemListByUserItemLevel 获取转卖商品持仓列表（商品维度聚合）
func QueryResaleUserItemListByUserItemLevel(ctx context.Context, req *QueryResaleUserItemListByUserItemLevelReq) (*QueryResaleUserItemListByUserItemLevelData, error) {
	log.Ctx(ctx).Infof("QueryResaleUserItemListByUserItemLevel req:%+v", req)
	rsp := &QueryResaleUserItemListByUserItemLevelResp{}

	params := map[string]interface{}{
		"open_user_id": req.OpenUserID,
		"item_id":      req.ItemID,
		"page":         req.GetPage() - 1,
		"limit":        req.GetPageSize(),
		"sign":         "",
	}
	if len(req.UserItemIDs) > 0 {
		params["user_item_ids"] = req.UserItemIDs
	}
	if len(req.StatusList) > 0 {
		params["status_list"] = req.StatusList
	}
	if req.CreatedAtLt != "" {
		params["created_at_lt"] = req.CreatedAtLt
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("QueryResaleUserItemListByUserItemLevel sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("QueryResaleUserItemListByUserItemLevel GetAppAccess err，返回数据：%v", err)
		return nil, err
	}

	url := "/open/user_item/resale_user_item_list"
	err = utilRequest.New(ctx,
		utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url),
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken)),
	).Call(&rsp)

	log.Ctx(ctx).Infof("QueryResaleUserItemListByUserItemLevel  params:%+v", params)
	if err != nil {
		log.Ctx(ctx).Infof("QueryResaleUserItemListByUserItemLevel err:%v", err)
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("QueryResaleUserItemListByUserItemLevel 请求异常，返回数据：%v", utils.Obj2JsonStr(rsp))

		return nil, errors.New(fmt.Sprintf("code: %d, msg: %s", rsp.Code, rsp.Desc))
	}

	log.Ctx(ctx).Infof("QueryResaleUserItemListByUserItemLevel 请求成功 params:%+v,rsp:%+v", params, rsp)

	return &rsp.Data, err
}
