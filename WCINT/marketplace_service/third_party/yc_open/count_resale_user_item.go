package yc_open

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"fmt"
	"github.com/pkg/errors"
	"marketplace_service/global"
	"marketplace_service/pkg/utils"
	"time"
)

type (
	CountResaleUserItemReq struct {
		OpenUserID  string   `json:"open_user_id" validate:"required"`
		ItemIDs     []string `json:"item_ids" validate:"required"`
		StatusList  []any    `json:"status_list"`
		CreatedAtLt string   `json:"created_at_lt"` // 创建时间小于
	}
	CountResaleUserItemData struct {
		ItemID string `json:"item_id"`
		Count  int64  `json:"count"`
	}
	CountResaleUserItemResp struct {
		Code int32                      `json:"code" form:"code"`
		Desc string                     `json:"desc" form:"desc"`
		Data []*CountResaleUserItemData `json:"data" form:"data"`
	}
)

// CountResaleUserItem 获取转卖商品物品计数
func CountResaleUserItem(ctx context.Context, req *CountResaleUserItemReq) ([]*CountResaleUserItemData, error) {
	log.Ctx(ctx).Infof("CountResaleUserItem req:%+v", req)
	rsp := &CountResaleUserItemResp{}

	params := map[string]interface{}{
		"open_user_id": req.OpenUserID,
		"item_ids":     req.ItemIDs,
	}
	if len(req.StatusList) > 0 {
		params["status_list"] = req.StatusList
	}
	if req.CreatedAtLt != "" {
		params["created_at_lt"] = req.CreatedAtLt
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("CountResaleUserItem sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("CountResaleUserItem GetAppAccess err，返回数据：%v", err)
		return nil, err
	}

	url := "/open/user_item/count_resale_user_item"
	err = utilRequest.New(ctx,
		utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url),
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken)),
	).Call(&rsp)

	log.Ctx(ctx).Infof("CountResaleUserItem  params:%+v", params)
	if err != nil {
		log.Ctx(ctx).Infof("CountResaleUserItem err:%v", err)
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("CountResaleUserItem 请求异常，返回数据：%v", utils.Obj2JsonStr(rsp))

		return nil, errors.New(fmt.Sprintf("code: %d, msg: %s", rsp.Code, rsp.Desc))
	}

	log.Ctx(ctx).Infof("CountResaleUserItem 请求成功 params:%+v,rsp:%+v", params, rsp)

	return rsp.Data, err
}
