package define

import "time"

type (
	UserItemStatusEnum int32
)

func (r UserItemStatusEnum) Val() int32 {
	return int32(r)
}

const (
	UserItemStatusOwned   UserItemStatusEnum = 1   // 持有中
	UserItemStatusForSale UserItemStatusEnum = 100 // 出售中
)

type (
	UserItemReceiveTypeEnum int32
)

func (r UserItemReceiveTypeEnum) Val() int32 {
	return int32(r)
}

const (
	UserItemReceiveTypeFantasyMartBuy UserItemReceiveTypeEnum = 10  // 二手交易
	UserItemReceiveTypeDeposit        UserItemReceiveTypeEnum = 123 // 云仓寄存
	UserItemReceiveTypeIssueItem      UserItemReceiveTypeEnum = 124 // 首发商品
	UserItemReceiveTypeAirdrop        UserItemReceiveTypeEnum = 125 // 空投商品
	UserItemReceiveTypeSynthesis      UserItemReceiveTypeEnum = 126 // 融合获得
	UserItemReceiveTypeStory          UserItemReceiveTypeEnum = 127 // 故事探索
	UserItemReceiveTypeWcReSell       UserItemReceiveTypeEnum = 128 // 文潮转卖
)

type UserItemData struct {
	ID         string `json:"_id"`
	Status     int32  `json:"status"`
	RelatedIDs struct {
		ExchangeToken   int32 `json:"exchange_token"`
		ExchangeBalance int32 `json:"exchange_balance"`
	} `json:"related_ids"`
	Extends struct {
		ReceiveType int32 `json:"receive_type"`
		QualityType int32 `json:"quality_type"`
	} `json:"extends"`
	TradeInfo struct {
		SaleMode     int32      `json:"sale_mode"`
		BuyPrice     int32      `json:"buy_price"`
		BuyTime      *time.Time `json:"buy_time"`
		FusionTags   int32      `json:"fusion_tags"`
		FusionStatus int32      `json:"fusion_status"`
		StoryTags    int32      `json:"story_tags"`
		StoryStatus  int32      `json:"story_status"`
		DeliveryTime *time.Time `json:"delivery_time"`
	} `json:"trade_info"`
	CreatedAt     time.Time `json:"created_at"`
	SortPrice     *int32    `json:"sort_price,omitempty"` // 使用指针处理 null
	ItemID        string    `json:"item_id"`
	ItemName      string    `json:"item_name"`
	Specification []struct {
		SpecificationNo    string `json:"specification_no"`
		SpecificationName  string `json:"specification_name"`
		SpecificationValue string `json:"specification_value"`
	} `json:"specification"`
	IconURL    string   `json:"icon_url"`
	MarketName string   `json:"market_name"`
	IPID       []string `json:"ip_id"`
	Price      struct {
		ZeroSup struct {
			SellPrice    int32 `json:"sell_price"`
			MinPrice     int32 `json:"min_price"`
			SupSellPrice int32 `json:"sup_sell_price"`
			SupMinPrice  int32 `json:"sup_min_price"`
			SellListings int32 `json:"sell_listings"`
		} `json:"zero_sup"`
		Unx struct {
			SellPrice    int32 `json:"sell_price"`
			SellListings int32 `json:"sell_listings"`
		} `json:"unx"`
	} `json:"price"`
}
