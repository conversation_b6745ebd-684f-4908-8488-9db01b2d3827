package yc_open

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"fmt"
	"github.com/pkg/errors"
	"marketplace_service/global"
	"marketplace_service/third_party/yc_open/define"
)

type UpdateUserItemStatusReq struct {
	UserItemIDs    []string                  `json:"user_item_ids"`
	OriginalStatus define.UserItemStatusEnum `json:"original_status"`
	UpdateStatus   define.UserItemStatusEnum `json:"update_status"`
	Retry          bool                      `json:"retry"`          // 是否使用重试策略
	IdempotentKey  string                    `json:"idempotent_key"` // 幂等 key，使用重试策略时必传
}

type UpdateUserItemStatusResp struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data interface{} `json:"data" form:"data"`
}

// UpdateUserItemStatus 查询商品的持仓列表信息 仓
func UpdateUserItemStatus(ctx context.Context, req *UpdateUserItemStatusReq) error {
	rsp := &UpdateUserItemStatusResp{}
	params := map[string]interface{}{
		"user_item_ids":   req.UserItemIDs,
		"original_status": req.OriginalStatus,
		"update_status":   req.UpdateStatus,
		"sign":            "",
	}
	if req.Retry && req.IdempotentKey != "" {
		params["retry"] = req.Retry
		params["idempotent_key"] = req.IdempotentKey
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("UpdateUserItemStatus sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return err
	}

	url := "/open/user_item/update_user_item_status"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethod("PUT"),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("更新气仓物品请求失败，返回数据：%v", err)
		return err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("更新气仓物品状态失败，返回数据：%v", rsp)
		return errors.New(fmt.Sprintf("更新气仓物品状态失败, code: %d, msg: %s", rsp.Code, rsp.Desc))
	}

	return nil
}
