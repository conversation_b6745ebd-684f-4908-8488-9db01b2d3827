package yc_open

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"fmt"
	"github.com/pkg/errors"
	"marketplace_service/global"
	"marketplace_service/pkg/pagination"
	"marketplace_service/pkg/utils"
	"time"
)

type (
	QueryResaleUserItemListByItemLevelReq struct {
		pagination.Pagination
		OpenUserID string `json:"open_user_id" validate:"required"`
		ItemName   string `json:"item_name"`
	}
	QueryResaleUserItemListByItemLevelInfo struct {
		ItemID string `json:"item_id"`
		Count  int64  `json:"count"`
	}
	QueryResaleUserItemListByItemLevelData struct {
		List  []*QueryResaleUserItemListByItemLevelInfo `json:"list"`
		Total int64                                     `json:"total"`
	}
	QueryResaleUserItemListByItemLevelResp struct {
		Code int32                                  `json:"code" form:"code"`
		Desc string                                 `json:"desc" form:"desc"`
		Data QueryResaleUserItemListByItemLevelData `json:"data" form:"data"`
	}
)

// QueryResaleUserItemListByItemLevel 获取转卖商品持仓列表（商品维度聚合）
func QueryResaleUserItemListByItemLevel(ctx context.Context, req *QueryResaleUserItemListByItemLevelReq) (*QueryResaleUserItemListByItemLevelData, error) {
	log.Ctx(ctx).Infof("QueryResaleUserItemListByItemLevel req:%+v", req)
	rsp := &QueryResaleUserItemListByItemLevelResp{}

	params := map[string]interface{}{
		"open_user_id": req.OpenUserID,
		"item_name":    req.ItemName,
		"page":         req.GetPage() - 1,
		"limit":        req.GetPageSize(),
		"sign":         "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("QueryResaleUserItemListByItemLevel sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("QueryResaleUserItemListByItemLevel GetAppAccess err，返回数据：%v", err)
		return nil, err
	}

	url := "/open/user_item/resale_item_list"
	err = utilRequest.New(ctx,
		utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url),
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken)),
	).Call(&rsp)

	log.Ctx(ctx).Infof("QueryResaleUserItemListByItemLevel  params:%+v", params)
	if err != nil {
		log.Ctx(ctx).Infof("QueryResaleUserItemListByItemLevel err:%v", err)
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("QueryResaleUserItemListByItemLevel 请求异常，返回数据：%v", utils.Obj2JsonStr(rsp))

		return nil, errors.New(fmt.Sprintf("code: %d, msg: %s", rsp.Code, rsp.Desc))
	}

	log.Ctx(ctx).Infof("QueryResaleUserItemListByItemLevel 请求成功 params:%+v,rsp:%+v", params, rsp)

	return &rsp.Data, err
}
