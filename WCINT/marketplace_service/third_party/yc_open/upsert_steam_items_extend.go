package yc_open

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"fmt"
	"github.com/pkg/errors"
	"marketplace_service/global"
)

type UpsertSteamItemsExtendReq struct {
	ItemID     string `json:"item_id"`
	SkuNo      string `json:"sku_no"`
	ItemName   string `json:"item_name"`
	IsGuide    bool   `json:"is_guide"`
	DataSource int32  `json:"data_source"`
	Type       int32  `json:"type"`
}

type UpsertSteamItemsExtendResp struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data interface{} `json:"data" form:"data"`
}

// UpsertSteamItemsExtend 更新商品扩展记录
func UpsertSteamItemsExtend(ctx context.Context, req *UpsertSteamItemsExtendReq) error {
	rsp := &UpdateUserItemStatusResp{}
	params := map[string]interface{}{
		"item_id":     req.ItemID,
		"sku_no":      req.SkuNo,
		"item_name":   req.ItemName,
		"is_guide":    req.IsGuide,
		"data_source": req.DataSource,
		"type":        req.Type,
		"sign":        "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("UpsertSteamItemsExtend sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return err
	}

	url := "/open/steam_item/upsert_steam_items_extend"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("更新商品扩展记录，返回数据：%v", err)
		return err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("更新商品扩展记录，返回数据：%v", rsp)
		return errors.New(fmt.Sprintf("更新商品扩展记录失败, code: %d, msg: %s", rsp.Code, rsp.Desc))
	}

	return nil
}
