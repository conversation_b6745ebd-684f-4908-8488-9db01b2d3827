package yc_open

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"marketplace_service/global"
	"marketplace_service/pkg/utils"
	"net"
	"time"
)

type UserItemTransferForm struct {
	UserItemIds  []string `json:"user_item_ids" form:"user_item_ids"`
	ToOpenUserId string   `json:"to_open_user_id" form:"to_open_user_id"`
	BuyPrice     int64    `json:"buy_price" form:"buy_price"`
	BuyTime      string   `json:"buy_time" form:"buy_time"`
}

type UserItemTransferResp struct {
	Code int32                   `json:"code" form:"code"`
	Desc string                  `json:"desc" form:"desc"`
	Data []*UserItemTransferData `json:"data" form:"data"`
}

type UserItemTransferData struct {
	UserItemId       string `json:"_id" form:"_id"`
	OriginUserItemId string `json:"origin_user_item_id" form:"origin_user_item_id"`
}

type UserItemTransferResult struct {
	Data   []*UserItemTransferData `json:"data" form:"data"`
	Status int32                   `json:"status" form:"status"`
}

const (
	UserItemTransferSuccess = 1
	UserItemTransferUnknown = 0
	UserItemTransferFail    = -1
)

// UserItemTransfer 物品转移
func UserItemTransfer(ctx context.Context, form *UserItemTransferForm) (*UserItemTransferResult, error) {
	log.Ctx(ctx).Infof("UserItemTransfer form:%+v", utils.Obj2JsonStr(form))
	rsp := &UserItemTransferResp{}
	result := &UserItemTransferResult{}
	params := map[string]interface{}{
		"to_open_user_id": form.ToOpenUserId,
		"user_item_ids":   form.UserItemIds,
		"buy_price":       form.BuyPrice,
		"buy_time":        form.BuyTime,
		"sign":            "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("UserItemTransfer sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("UserItemTransfer GetAppAccess err，返回数据：%v", err)
		result.Status = UserItemTransferFail
		return result, err
	}

	url := "/open/user_item/v2/user_item_transfer"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		// todo yangxi test
		utilRequest.WithMethodPost(), utilRequest.WithTimeOut(time.Second*5),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)

	log.Ctx(ctx).Infof("UserItemTransfer SynthesisUserItemTransfer params:%+v", params)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// 未知异常
			log.Ctx(ctx).Errorf("物品转移超时异常，返回数据：%v", err)
			result.Status = UserItemTransferUnknown
			return result, response.Fail.SetMsg("物品转移未知异常")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("物品转移超时异常，返回数据：%v", err)
			result.Status = UserItemTransferUnknown
			return result, response.Fail.SetMsg("物品转移请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("物品转移异常，返回数据：%v", err)
		result.Status = UserItemTransferUnknown
		return result, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("物品转移异常，返回数据：%v", utils.Obj2JsonStr(rsp))
		result.Status = UserItemTransferFail
		return result, response.Fail.SetMsg("物品转移失败")
	}
	log.Ctx(ctx).Infof("物品转移成功 params:%+v,rsp:%+v", params, rsp)
	result.Status = UserItemTransferSuccess
	result.Data = rsp.Data
	return result, nil
}
