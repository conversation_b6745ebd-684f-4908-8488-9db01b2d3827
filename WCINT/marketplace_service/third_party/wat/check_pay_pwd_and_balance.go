package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"marketplace_service/apps/mall/define"
	"marketplace_service/pkg/utils"
)

const PwdError = 80205230
const BalanceInsufficientError = 80201210
const CheckPayPwdAndBalanceSuccess = 0
const CheckPayPwdAndBalanceFail = -1

type CheckPayPwdAndBalanceForm struct {
	UserId string `json:"user_id" form:"user_id"`
	Pwd    string `json:"pwd" form:"pwd"`
	Amount int64  `json:"amount" form:"amount"`
}

type GetCheckPayPwdAndBalanceResp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data string `json:"data" form:"data"`
}

// CheckPayPwdAndBalance 检测用户余额是否充足和密码是否正确
func CheckPayPwdAndBalance(ctx context.Context, form *CheckPayPwdAndBalanceForm) error {
	rsp := &GetCheckPayPwdAndBalanceResp{}

	req, err := request.Wat()
	if err != nil {
		return err
	}

	opts := []utilRequest.Option{
		utilRequest.WithParams(map[string]interface{}{
			"user_id": form.UserId,
			"pwd":     form.Pwd,
			"amount":  form.Amount,
		}),
		utilRequest.WithMethodGet(),
	}
	err = req.Call(
		ctx,
		"open/wallet/v1/check_pay_pwd_and_balance",
		&rsp,
		opts...,
	)
	if err != nil {
		return err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("检测用户余额是否充足和密码是否正确失败,form:%+v, res:%+v", utils.Obj2JsonStr(form), utils.Obj2JsonStr(rsp))
		if rsp.Code == PwdError {
			return define.MS200025Err
		} else if rsp.Code == BalanceInsufficientError {
			return define.MS200026Err
		}
		return response.Fail.SetMsg(rsp.Desc)
	}
	return nil
}
