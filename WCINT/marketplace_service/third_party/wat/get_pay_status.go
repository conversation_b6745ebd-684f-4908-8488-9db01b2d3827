package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"marketplace_service/pkg/utils"
)

type GetPayStatusResp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data bool   `json:"data" form:"data"`
}

func GetPayStatus(ctx context.Context, orderId int64) (bool, error) {
	rsp := &GetPayStatusResp{}
	req, err := request.Wat()
	if err != nil {
		return false, err
	}
	err = req.Call(
		ctx,
		"open/wallet/order/v1/recharge/pay_status",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{"order_id": orderId}),
		utilRequest.WithMethodGet(),
	)
	if err != nil {
		return false, err
	}

	if rsp.Code != 0 {
		return false, response.Fail.SetMsg(rsp.Desc)
	}
	log.Ctx(ctx).Infof("GetPayStatus，orderId:%+v，返回数据：%+v", orderId, utils.Obj2JsonStr(rsp))
	return rsp.Data, err
}
