package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"marketplace_service/pkg/utils"
	"net"
	"time"
)

type SpdbC2cBalancePayForm struct {
	BuyUserId  string                        `json:"buy_user_id"`
	SaleUserId string                        `json:"sale_user_id"`
	Amount     int64                         `json:"amount"`
	Fee        int64                         `json:"fee"`
	Extends    *SpdbC2cBalancePayFormExtends `json:"extends"`
	DataFrom   []*SpdbC2cBalancePayFormData  `json:"dataFrom"`
}

type SpdbC2cBalancePayFormExtends struct {
	FromUserId    string `json:"from_user_id"`
	BalanceFrom   string `json:"balance_from"`
	ResaleOrderId string `json:"resale_order_id"`
}

type SpdbC2cBalancePayFormData struct {
	PoolWalletId *string                       `json:"pool_wallet_id"`
	FromUserId   *string                       `json:"from_user_id,omitempty"`
	ToWalletId   *string                       `json:"to_wallet_id,omitempty"`
	Amount       *int64                        `json:"amount"`
	Extends      *SpdbC2cBalancePayFormExtends `json:"extends"`
	FromWalletId *string                       `json:"from_wallet_id,omitempty"`
	ToUserId     *string                       `json:"to_user_id,omitempty"`
	TransferType *int32                        `json:"transfer_type,omitempty"`
	Payer        *int32                        `json:"payer,omitempty"`
}

type SpdbC2cBalancePayRsp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data any    `json:"data" form:"data"`
}

const (
	SpdbC2cBalancePaySuccess = 1
	SpdbC2cBalancePayUnknown = 0
	SpdbC2cBalancePayFail    = -1
)

// SpdbC2cBalancePay 余额支付(浦发)
func SpdbC2cBalancePay(ctx context.Context, form *SpdbC2cBalancePayForm) (int32, error) {
	rsp := &SpdbC2cBalancePayRsp{}
	req, err := request.Wat()
	if err != nil {
		return SpdbC2cBalancePayFail, err
	}
	params := map[string]interface{}{
		"fee":          form.Fee,
		"buy_user_id":  form.BuyUserId,
		"sale_user_id": form.SaleUserId,
		"amount":       form.Amount,
		"extends":      form.Extends,
		"dataFrom":     form.DataFrom,
	}

	err = req.Call(
		ctx,
		"open/wallet/v1/spdb_c2c_balance_pay",
		&rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		//todo yangxi test
		utilRequest.WithTimeOut(5*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return SpdbC2cBalancePayUnknown, response.Fail.SetMsg("余额支付(浦发)请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("余额支付(浦发)超时异常，返回数据：%v", err)
			return SpdbC2cBalancePayUnknown, response.Fail.SetMsg("余额支付(浦发)请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("余额支付(浦发)异常，返回数据：%v", err)
		return SpdbC2cBalancePayFail, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("余额支付(浦发)异常，返回数据：%v", utils.Obj2JsonStr(rsp))
		return SpdbC2cBalancePayFail, response.Fail.SetMsg("余额支付(浦发)失败")
	}
	log.Ctx(ctx).Infof("余额支付(浦发)成功，返回数据：%v", utils.Obj2JsonStr(rsp))
	return SpdbC2cBalancePaySuccess, err
}
