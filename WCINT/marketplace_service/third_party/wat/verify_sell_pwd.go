package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"github.com/pkg/errors"
	"marketplace_service/pkg/utils"
)

type VerifySellPwdResp struct {
	Code int32       `json:"code"`
	Desc string      `json:"desc"`
	Data interface{} `json:"data"`
}

// VerifySellPwd 校验出售密码
func VerifySellPwd(ctx context.Context, userID, sellPwd string) error {
	rsp := &VerifySellPwdResp{}

	req, err := request.Wat()
	if err != nil {
		return err
	}

	params := map[string]interface{}{
		"user_id":  userID,
		"sell_pwd": sellPwd,
	}
	opts := []utilRequest.Option{
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
	}
	err = req.Call(
		ctx,
		"open/wallet/v1/verify_sell_pwd",
		&rsp,
		opts...,
	)
	if err != nil {
		return err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("校验用户出售密码失败, params:%+v, res:%+v", utils.Obj2JsonStr(params), utils.Obj2JsonStr(rsp))
		return errors.New(rsp.Desc)
	}

	return nil
}
