package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"marketplace_service/pkg/utils"
)

type CheckSellPwdInfoForm struct {
	UserId string `json:"user_id" form:"user_id"`
}

type GetCheckSellPwdInfoData struct {
	IsSet             bool  `json:"is_set"`
	NeedToInputPwd    bool  `json:"need_to_input_pwd"`
	RemainingAttempts int32 `json:"remaining_attempts"`
	RetryAfterSeconds int32 `json:"retry_after_seconds"`
}

type GetCheckSellPwdInfoResp struct {
	Code int32                    `json:"code"`
	Desc string                   `json:"desc"`
	Data *GetCheckSellPwdInfoData `json:"data"`
}

// CheckSellPwdInfo 检测用户出售密码信息
func CheckSellPwdInfo(ctx context.Context, form *CheckSellPwdInfoForm) (*GetCheckSellPwdInfoData, error) {
	rsp := &GetCheckSellPwdInfoResp{}

	req, err := request.Wat()
	if err != nil {
		return nil, err
	}

	opts := []utilRequest.Option{
		utilRequest.WithParams(map[string]interface{}{
			"user_id": form.UserId,
		}),
		utilRequest.WithMethodGet(),
	}
	err = req.Call(
		ctx,
		"open/wallet/v1/check_sell_pwd_info",
		&rsp,
		opts...,
	)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("检测用户出售密码信息失败,form:%+v, res:%+v", utils.Obj2JsonStr(form), utils.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg(rsp.Desc)
	}

	return rsp.Data, nil
}
