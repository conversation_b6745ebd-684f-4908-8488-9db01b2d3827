package main

import (
	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModelFrm() {
	dsn := "root:nnA6MXpPQhqkxxEBLo@tcp(127.0.0.1:8002)/marketplace_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/mall/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"tinyint": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*int32"
			}
			return "int32"
		},
		"int": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*int32"
			}
			return "int32"
		},
		"bigint": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*int64"
			}
			return "int64"
		},
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	mallItem := g.GenerateModelAs("mall_item", "MallItem")

	tradeOrderItem := g.GenerateModelAs("trade_order_item", "TradeOrderItem")

	tradeOrder := g.GenerateModelAs("trade_order", "TradeOrder",
		gen.FieldRelate(field.HasMany, "TradeOrderItem", tradeOrderItem,
			&field.RelateConfig{
				RelateSlicePointer: true,
				GORMTag:            (field.GormTag{}).Append("foreignKey", "order_id"),
			},
		),
	)

	itemStatistic := g.GenerateModelAs("item_statistic", "ItemStatistic")

	userWishlist := g.GenerateModelAs("user_wishlist", "UserWishlist")

	tradeOrderFreight := g.GenerateModelAs("trade_order_freight", "TradeOrderFreight")

	resaleItem := g.GenerateModelAs("resale_item", "ResaleItem")
	homeFeedStats := g.GenerateModelAs("home_feed_stats", "HomeFeedStats")
	homeFeed := g.GenerateModelAs("home_feed", "HomeFeed",
		gen.FieldRelate(field.HasOne, "HomeFeedStats", homeFeedStats,
			&field.RelateConfig{
				RelateSlicePointer: false,
				GORMTag:            (field.GormTag{}).Append("foreignKey", "home_feed_id"),
			},
		),
	)
	resaleListingsItem := g.GenerateModelAs("resale_listings_item", "ResaleListingsItem")

	resaleListings := g.GenerateModelAs("resale_listings", "ResaleListings",
		gen.FieldRelate(field.HasMany, "ResaleListingsItem", resaleListingsItem,
			&field.RelateConfig{
				RelateSlicePointer: true,
				GORMTag:            (field.GormTag{}).Append("foreignKey", "resale_listings_id"),
			},
		),
	)

	resaleOrderItem := g.GenerateModelAs("resale_order_item", "ResaleOrderItem")

	commonConfig := g.GenerateModelAs("common_config", "CommonConfig")

	resaleOrder := g.GenerateModelAs("resale_order", "ResaleOrder",
		gen.FieldRelate(field.HasMany, "ResaleOrderItem", resaleOrderItem,
			&field.RelateConfig{
				RelateSlicePointer: true,
				GORMTag:            (field.GormTag{}).Append("foreignKey", "resale_order_id"),
			},
		),
	)

	applyBasic := make([]interface{}, 0)
	applyBasic = append(
		applyBasic, mallItem, tradeOrder, tradeOrderItem, userWishlist, itemStatistic, tradeOrderFreight, resaleItem, homeFeedStats, homeFeed, commonConfig,
		resaleListingsItem, resaleListings, resaleOrderItem, resaleOrder,
	)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

func genRepoFrm() {
	GenRepo("mall", "MallItem", "mall_item", "IMallItemDo", "MallItem")
	GenRepo("mall", "TradeOrderItem", "trade_order_item", "ITradeOrderItemDo", "TradeOrderItem")
	GenRepo("mall", "TradeOrder", "trade_order", "ITradeOrderDo", "TradeOrder")
	GenRepo("mall", "UserWishlist", "user_wishlist", "IUserWishlistDo", "UserWishlist")
	GenRepo("mall", "ItemStatistic", "item_statistic", "IItemStatisticDo", "ItemStatistic")
	GenRepo("mall", "TradeOrderFreight", "trade_order_freight", "ITradeOrderFreightDo", "TradeOrderFreight")
	GenRepo("mall", "ResaleItem", "resale_item", "IResaleItemDo", "ResaleItem")
	GenRepo("mall", "HomeFeedStats", "home_feed_stats", "IHomeFeedStatsDo", "HomeFeedStats")
	GenRepo("mall", "HomeFeed", "home_feed", "IHomeFeedDo", "HomeFeed")
	GenRepo("mall", "ResaleListingsItem", "resale_listings_item", "IResaleListingsItemDo", "ResaleListingsItem")
	GenRepo("mall", "ResaleListings", "resale_listings", "IResaleListingsDo", "ResaleListings")
	GenRepo("mall", "ResaleOrderItem", "resale_order_item", "IResaleOrderItemDo", "ResaleOrderItem")
	GenRepo("mall", "ResaleOrder", "resale_order", "IResaleOrderDo", "ResaleOrder")
	GenRepo("mall", "CommonConfig", "common_config", "ICommonConfigDo", "CommonConfig")
}

func main() {
	genModelFrm()
	genRepoFrm()
}
