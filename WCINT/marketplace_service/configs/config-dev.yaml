service:
  name: 'marketplace_service'
  version: '1.0.0'
  address: ":8801"
  token: "f6ZdQaLXI2XjXLwm"
  env: "dev"
mysql:
  marketplace_service:
    host: '127.0.0.1'
    port: 8002
    name: 'marketplace_service'
    user: 'root'
    password: 'nnA6MXpPQhqkxxEBLo'
    prefix: ''
    # 时区
    loc: Asia%2FShanghai
redis:
  marketplace_service:
    host: '127.0.0.1'
    port: 8001
    user: ''
    password: 'A6MXpPQhqkxxEBLo'
    db: 0
kafka:
  - name: 'marketplace_service'
    address:
      - 127.0.0.1:9092
logger:
  filename: './logs/info.log'

master_http:
  hosts:
    pat:
      host: "https://sit-wcjs-api.ahbq.com.cn/pat"
      auth: "Bearer UpVroDzmASRxfNvl"
    set:
      host: "https://sit-wcjs-api.ahbq.com.cn/set"
      auth: "Bearer FbsD6iVCltCSb6rE"
    tmt:
      host: "https://sit-wcjs-api.ahbq.com.cn/tmt"
      auth: "Bearer pwhQrrRzPkPv94PK"
    mor:
      host: "https://sit-wcjs-api.ahbq.com.cn/mor"
      auth: "Bearer c5kOw8O6rmq2xhAc"
    wat:
      host: "https://sit-wcjs-api.ahbq.com.cn/wat"
      auth: "Bearer kG3KEfByneVFKGaf"
pbs:
  admin_host: "http://sit-wcjs-api.ahbq.com.cn/goapi/pbs_admin"
yc:
  client_id: "yc_trade_market"
  client_secret: "HXe6DEg2SuB2kc2WaHAsHWRcEVuZDetYSTBP27trRJtC"
  host: "https://sit-openapi-yc.pandamart.vip/api"
warn_id: "608bbb4a403700007f00765c"
wae:
  app_key: "wcjs_sim"
  app_secret: "87e44tse087r6gs8erhg3g"
  host: "https://sim-api-tpsc.genkimart.ltd/apisco"
