package logic

import (
	"context"
	"errors"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/facade"
	"app_service/global"
	"app_service/pkg/cache"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// ValidatePostParams 验证帖子参数
func ValidatePostParams(description string, price int64) error {
	// 基本参数验证
	if description == "" {
		return define.CC500004Err
	}
	if price <= 0 {
		return define.CC500005Err
	}

	// 验证价格是否为10的倍数（即最多一位小数）
	// 因为价格以分为单位，一位小数对应10分的倍数
	if price%10 != 0 {
		return define.CC500006Err
	}

	return nil
}

// GetUserPostByID 获取用户的帖子（验证所有权）
func GetUserPostByID(ctx context.Context, userID, postID string) (*model.Post, error) {
	postSchema := repo.GetQuery().Post

	// 查询帖子是否存在且属于当前用户
	queryWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, postID).
		Eq(postSchema.MerchantID, userID).
		Build()

	post, err := repo.NewPostRepo(postSchema.WithContext(ctx)).SelectOne(queryWrapper)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, define.CC500001Err // 帖子不存在
		}
		log.Ctx(ctx).Errorf("查询帖子失败: %v", err)
		return nil, commondefine.CommonErr
	}

	return post, nil
}

// SetPostDetailCache 设置帖子详情缓存（支持随机TTL防雪崩）
func SetPostDetailCache(ctx context.Context, postID string, postDetail *define.GetPostDetailResp) error {
	cacheKey := constant.GetPostDetailKey(postID)
	opts := cache.DefaultCacheOptions(constant.PostDetailTTL)

	return cache.SetCache(ctx, cacheKey, postDetail, opts)
}

// SetPostListCache 设置帖子列表缓存（只缓存第一页，支持随机TTL防雪崩）
func SetPostListCache(ctx context.Context, page, size int, postList *define.GetPostListResp) error {
	// 只缓存第一页
	if page != 1 {
		return nil
	}

	cacheKey := constant.GetPostListKey(page, size)
	opts := cache.DefaultCacheOptions(constant.PostListFirstTTL)

	return cache.SetCache(ctx, cacheKey, postList, opts)
}

// InvalidatePostCache 失效帖子相关缓存
func InvalidatePostCache(ctx context.Context, postID string) error {
	// 删除帖子详情缓存
	cacheKey := constant.GetPostDetailKey(postID)
	if err := global.REDIS.Del(ctx, cacheKey).Err(); err != nil {
		log.Ctx(ctx).Errorf("删除帖子详情缓存失败 postID:%s, err:%v", postID, err)
		return err
	}

	// 删除帖子列表缓存（只删除第一页缓存，因为只缓存第一页）
	listPattern := "app_service:card_community:post:list:page:1:*"
	keys, err := global.REDIS.Keys(ctx, listPattern).Result()
	if err != nil {
		log.Ctx(ctx).Errorf("获取帖子列表缓存键失败 pattern:%s, err:%v", listPattern, err)
		return err
	}

	if len(keys) > 0 {
		if err := global.REDIS.Del(ctx, keys...).Err(); err != nil {
			log.Ctx(ctx).Errorf("删除帖子列表缓存失败 keys:%v, err:%v", keys, err)
			return err
		}
	}

	return nil
}

// GetPostDetailFromCache 获取帖子详情（缓存+数据库）
func GetPostDetailFromCache(ctx context.Context, postID string) (*define.GetPostDetailResp, error) {
	var postDetail *define.GetPostDetailResp

	cacheKey := constant.GetPostDetailKey(postID)
	err := cache.GetCache(ctx, cacheKey, &postDetail)
	if errors.Is(err, redis.Nil) {
		// 缓存未命中，从数据库查询
		postDetail, err = GetPostDetailByRemote(ctx, postID)
		if err != nil {
			// 如果是数据不存在错误，设置空值缓存
			if errors.Is(err, define.CC500001Err) {
				cacheKey := constant.GetPostDetailKey(postID)
				if cacheErr := cache.SetNullCache(ctx, cacheKey); cacheErr != nil {
					log.Ctx(ctx).Warnf("设置帖子详情空值缓存失败: %v", cacheErr)
				}
			}
			log.Ctx(ctx).Errorf("从数据库获取帖子详情失败 err:%+v", err)
			return nil, err
		}
		// 设置缓存
		if err := SetPostDetailCache(ctx, postID, postDetail); err != nil {
			log.Ctx(ctx).Warnf("设置帖子详情缓存失败: %v", err)
		}
		log.Ctx(ctx).Infof("设置帖子详情缓存成功 postId:%v", postID)
	} else if err != nil {
		// 其他缓存错误（包括空值缓存）
		if errors.Is(err, cache.ErrCacheDataNotFound) {
			return nil, define.CC500001Err
		}
		log.Ctx(ctx).Errorf("从缓存获取帖子详情失败 err:%+v", err)
		return nil, err
	}
	return postDetail, nil
}

// GetPostDetailByRemote 从数据库获取帖子详情（不使用缓存，强制刷新缓存）
func GetPostDetailByRemote(ctx context.Context, postID string) (*define.GetPostDetailResp, error) {
	// 从数据库查询
	postSchema := repo.GetQuery().Post
	queryWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, postID).
		Build()

	post, err := repo.NewPostRepo(postSchema.WithContext(ctx)).SelectOne(queryWrapper)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, define.CC500001Err
		}
		log.Ctx(ctx).Errorf("查询帖子详情失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 获取媒体文件
	mediaFiles, err := post.GetMediaFiles()
	if err != nil {
		log.Ctx(ctx).Errorf("获取媒体文件失败: %v", err)
		mediaFiles = []define.MediaFile{}
	}

	// 获取商户信息
	userInfo, err := facade.GetNodeUser(ctx, post.MerchantID)
	if err != nil {
		log.Ctx(ctx).Errorf("获取商户信息失败: %v", err)
		return nil, commondefine.CommonErr
	}

	merchantInfo := define.UserInfo{
		ID:     post.MerchantID,
		Name:   userInfo.PatbgDetail.Nickname,
		Avatar: userInfo.PatbgDetail.Avatar,
	}

	result := &define.GetPostDetailResp{
		ID:           post.ID,
		MerchantID:   post.MerchantID,
		MerchantInfo: merchantInfo,
		Description:  post.Description,
		Price:        post.Price,
		MediaFiles:   mediaFiles,
		Status:       post.GetStatus(),
		CreatedAt:    post.CreatedAt,
		UpdatedAt:    post.UpdatedAt,
	}

	return result, nil
}

// GetPostListFromCache 从缓存获取帖子列表（只缓存第一页，不使用空值缓存）
func GetPostListFromCache(ctx context.Context, page, size int) (*define.GetPostListResp, error) {
	// 只缓存第一页
	if page != 1 {
		return nil, nil
	}

	var postList *define.GetPostListResp
	cacheKey := constant.GetPostListKey(page, size)
	err := cache.GetCache(ctx, cacheKey, &postList)

	switch {
	case err == nil:
		// 缓存命中，返回数据
		return postList, nil
	case errors.Is(err, redis.Nil):
		// 缓存未命中
		return nil, nil
	default:
		// 其他缓存错误
		log.Ctx(ctx).Errorf("获取帖子列表缓存失败 page:%d, size:%d, err:%v", page, size, err)
		return nil, err
	}
}
